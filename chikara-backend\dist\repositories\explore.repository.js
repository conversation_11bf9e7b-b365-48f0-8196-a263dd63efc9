import { db } from "../lib/db.js";
import { Prisma } from "@prisma/client";
import { handleError, handleInternalError } from "../utils/log.js";
export const getStaticNodesByLocation = async (location) => {
    const nodes = await db.explore_static_node.findMany({
        where: { location },
        include: {
            shop: true,
        },
        orderBy: { id: "asc" },
    });
    return nodes.map((node) => {
        const title = node.nodeType === "SHOP" && node.shop ? node.shop.name : node.title;
        const description = node.nodeType === "SHOP" && node.shop ? node.shop.description : node.description;
        return {
            id: node.id,
            nodeType: node.nodeType,
            title,
            description,
            position: node.position,
            metadata: node.metadata,
            location: node.location,
            shopId: node.shopId,
            expiresAt: undefined,
        };
    });
};
export const getActivePlayerNodesByLocation = async (userId, location) => {
    const nodes = await db.explore_player_node.findMany({
        where: {
            userId,
            location,
            status: {
                in: ["available", "locked", "current"],
            },
            OR: [
                {
                    expiresAt: {
                        gt: new Date(),
                    },
                },
                {
                    expiresAt: null,
                },
            ],
        },
        orderBy: { createdAt: "desc" },
    });
    return nodes.map((node) => ({
        id: node.id,
        nodeType: node.nodeType,
        title: node.title,
        description: node.description,
        position: node.position,
        metadata: node.metadata,
        status: node.status,
        location: node.location,
        expiresAt: node.expiresAt,
    }));
};
export const createPlayerNode = async (userId, nodeType, title, description, position, location, expirationHours, metadata, status = "available") => {
    const expiresAt = new Date();
    if (expirationHours) {
        if (expirationHours <= 0 || expirationHours > 168) {
            return handleInternalError(`expirationHours must be between 1 and 168, received: ${expirationHours}`);
        }
        expiresAt.setHours(expiresAt.getHours() + expirationHours);
    }
    const node = await db.explore_player_node.create({
        data: {
            userId,
            nodeType,
            title,
            description,
            position,
            location,
            metadata: metadata ?? Prisma.DbNull,
            expiresAt: expirationHours ? expiresAt : null,
            status,
        },
    });
    return {
        ...node,
        position: node.position,
        metadata: node.metadata,
    };
};
export const cleanupExpiredPlayerNodes = async (userId) => {
    const result = await db.explore_player_node.deleteMany({
        where: {
            userId,
            expiresAt: {
                lte: new Date(),
            },
        },
    });
    return result.count;
};
export const getStaticNodeById = async (nodeId) => {
    const node = await db.explore_static_node.findUnique({
        where: { id: nodeId },
        include: {
            shop: true,
        },
    });
    if (!node)
        return null;
    const title = node.nodeType === "SHOP" && node.shop ? node.shop.name : node.title;
    const description = node.nodeType === "SHOP" && node.shop ? node.shop.description : node.description;
    return {
        ...node,
        title,
        description,
        position: node.position,
        metadata: node.metadata,
    };
};
export const getPlayerNodeById = async (nodeId, userId) => {
    const node = await db.explore_player_node.findFirst({
        where: {
            id: nodeId,
            userId,
            OR: [
                {
                    expiresAt: {
                        gt: new Date(),
                    },
                },
                {
                    expiresAt: null,
                },
            ],
        },
    });
    if (!node)
        return null;
    return {
        ...node,
        position: node.position,
        metadata: node.metadata,
    };
};
export const countPlayerNodesByLocation = async (userId, location) => {
    return await db.explore_player_node.count({
        where: {
            userId,
            location,
            status: {
                in: ["available", "locked", "current", "completed"],
            },
            OR: [
                {
                    expiresAt: {
                        gt: new Date(),
                    },
                },
                {
                    expiresAt: null,
                },
            ],
        },
    });
};
export const getPlayerNodesByUserAndLocation = async (userId, location) => {
    const nodes = await db.explore_player_node.findMany({
        where: {
            userId,
            location,
            OR: [
                {
                    expiresAt: {
                        gt: new Date(),
                    },
                },
                {
                    expiresAt: null,
                },
            ],
        },
        orderBy: { id: "asc" },
    });
    return nodes.map((node) => ({
        ...node,
        position: node.position,
        metadata: node.metadata,
    }));
};
export const deletePlayerNode = async (nodeId, userId) => {
    await db.explore_player_node.delete({
        where: { id: nodeId, userId },
    });
};
export const initiateTravel = async (userId, newLocation, method, cost, travelTimeMinutes) => {
    const result = await db.$transaction(async (tx) => {
        const user = await tx.user.findUnique({
            where: { id: userId },
            select: {
                cash: true,
                currentMapLocation: true,
                travelStartTime: true,
                travelEndTime: true,
            },
        });
        if (!user) {
            return handleError("User not found", 400);
        }
        if (user.cash < cost) {
            return handleError("Insufficient cash", 400);
        }
        if (user.currentMapLocation === newLocation) {
            return handleError("Already at this location", 400);
        }
        if (user.travelStartTime && user.travelEndTime && new Date() < user.travelEndTime) {
            return handleError("Already traveling to another location", 400);
        }
        const now = new Date();
        const travelEndTime = new Date(now.getTime() + travelTimeMinutes * 60 * 1000);
        const updatedUser = await tx.user.update({
            where: { id: userId },
            data: {
                cash: user.cash - cost,
                currentMapLocation: newLocation,
                travelStartTime: now,
                travelEndTime: travelEndTime,
                travelMethod: method,
            },
            select: {
                cash: true,
                currentMapLocation: true,
                travelStartTime: true,
                travelEndTime: true,
                travelMethod: true,
            },
        });
        return updatedUser;
    });
    return result;
};
export const getUserTravelStatusOnly = async (userId) => {
    const user = await db.user.findUnique({
        where: { id: userId },
        select: {
            currentMapLocation: true,
            travelStartTime: true,
            travelEndTime: true,
            travelMethod: true,
        },
    });
    if (!user) {
        return handleError("User not found", 400);
    }
    const now = new Date();
    const isTravel = !!(user.travelStartTime && user.travelEndTime && now < user.travelEndTime);
    return {
        isTravel,
        travelingTo: isTravel ? user.currentMapLocation || undefined : undefined,
        travelStartTime: user.travelStartTime || undefined,
        travelEndTime: user.travelEndTime || undefined,
        travelMethod: user.travelMethod || undefined,
        remainingTime: isTravel && user.travelEndTime ? user.travelEndTime.getTime() - now.getTime() : undefined,
    };
};
export const autoCompleteTravelIfNeeded = async (userId) => {
    const result = await db.$transaction(async (tx) => {
        const user = await tx.user.findUnique({
            where: { id: userId },
            select: {
                currentMapLocation: true,
                travelStartTime: true,
                travelEndTime: true,
                travelMethod: true,
            },
        });
        if (!user) {
            return handleError("User not found", 400);
        }
        const now = new Date();
        if (user.travelEndTime && now >= user.travelEndTime) {
            const updateResult = await tx.user.updateMany({
                where: {
                    id: userId,
                    travelEndTime: {
                        not: null,
                        lte: now,
                    },
                },
                data: {
                    travelStartTime: null,
                    travelEndTime: null,
                    travelMethod: null,
                },
            });
            const autoCompleted = updateResult.count > 0;
            return {
                autoCompleted,
                completedDestination: autoCompleted ? user.currentMapLocation || undefined : undefined,
            };
        }
        return {
            autoCompleted: false,
            completedDestination: undefined,
        };
    });
    return result;
};
export const getUserTravelStatus = async (userId, autoComplete = true) => {
    if (!autoComplete) {
        const status = await getUserTravelStatusOnly(userId);
        return {
            ...status,
            autoCompleted: false,
        };
    }
    const completionResult = await autoCompleteTravelIfNeeded(userId);
    const status = await getUserTravelStatusOnly(userId);
    return {
        ...status,
        autoCompleted: completionResult.autoCompleted,
        completedDestination: completionResult.completedDestination,
    };
};
export const lockPlayerNodeForInteraction = async (nodeId, userId) => {
    return await db.$transaction(async (tx) => {
        const node = await tx.explore_player_node.findFirst({
            where: {
                id: nodeId,
                userId,
                status: {
                    in: ["available"],
                },
                OR: [
                    {
                        expiresAt: {
                            gt: new Date(),
                        },
                    },
                    {
                        expiresAt: null,
                    },
                ],
            },
        });
        if (!node) {
            return null;
        }
        const updatedNode = await tx.explore_player_node.updateMany({
            where: {
                id: nodeId,
                userId,
                status: { in: ["available"] },
            },
            data: {
                status: "current",
            },
        });
        if (updatedNode.count === 0) {
            return null;
        }
        return {
            ...node,
            position: node.position,
            metadata: node.metadata,
        };
    });
};
export const completePlayerNodeInteraction = async (nodeId, userId, expirationMinutes) => {
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + expirationMinutes);
    const result = await db.explore_player_node.updateMany({
        where: {
            id: nodeId,
            userId,
            status: "current",
        },
        data: {
            status: "completed",
            expiresAt,
        },
    });
    return result.count > 0;
};
export const unlockPlayerNodeOnFailure = async (nodeId, userId) => {
    const result = await db.explore_player_node.updateMany({
        where: {
            id: nodeId,
            userId,
            status: "current",
        },
        data: {
            status: "available",
        },
    });
    return result.count > 0;
};
export const updatePlayerNodeMetadata = async (nodeId, userId, metadata) => {
    const result = await db.explore_player_node.updateMany({
        where: {
            id: nodeId,
            userId,
        },
        data: {
            metadata,
        },
    });
    return result.count > 0;
};
export const updatePlayerNodeStatus = async (nodeId, userId, status) => {
    const result = await db.explore_player_node.updateMany({
        where: {
            id: nodeId,
            userId,
        },
        data: {
            status,
        },
    });
    return result.count > 0;
};
