import { LogErrorStack } from "../../../utils/log.js";
import {
    generateRandomGrid,
    generateSessionId,
    createObfuscatedGrid,
    createObfuscatedMultiCellResources,
    createEnhancedResources,
} from "./scavenging.helpers.js";
import {
    DEFAULT_ENERGY,
    MAX_DIFFICULTY_TIER,
    MIN_DIFFICULTY_TIER,
    DIFFICULTY_TIERS,
    POTENTIAL_RESOURCES,
} from "./scavenging.constants.js";
import * as ScavengingState from "./scavenging.state.js";
import * as InventoryService from "../../../core/inventory.service.js";
import type { GameSession, Cell, CellPosition, RevealedCellInfo } from "./scavenging.types.js";

/**
 * Generate a scavenging grid for the current user
 */
export const generateGrid = async (userId: number, difficultyTier?: number) => {
    try {
        const tier = difficultyTier || 1;

        if (tier < MIN_DIFFICULTY_TIER || tier > MAX_DIFFICULTY_TIER) {
            return {
                error: `Difficulty tier must be between ${MIN_DIFFICULTY_TIER} and ${MAX_DIFFICULTY_TIER}`,
                statusCode: 400,
            };
        }

        // Check if user already has an active session
        const existingSession = await ScavengingState.getUserActiveSession(userId);
        if (existingSession && !existingSession.gameOver) {
            return { error: "You already have an active scavenging session", statusCode: 400 };
        }

        // Get difficulty configuration
        const difficultyConfig = DIFFICULTY_TIERS[tier];
        const { gridSize, minResources, maxResources } = difficultyConfig;

        const { grid, multiCellResources } = generateRandomGrid(gridSize, minResources, maxResources, tier);

        // Initialize resources object for all potential resources
        const initialResources: Record<number, number> = {};
        for (const potentialResource of POTENTIAL_RESOURCES) {
            initialResources[potentialResource.itemId] = 0;
        }

        // Create a new session
        const sessionId = generateSessionId();
        const gameSession: GameSession = {
            sessionId,
            userId,
            fullGrid: grid,
            multiCellResources,
            energy: DEFAULT_ENERGY,
            gameOver: false,
            resources: initialResources,
            gridSize: gridSize,
            difficultyTier: tier,
            createdAt: new Date(),
            lastActivity: new Date(),
        };

        // Store session in Redis
        const stored = await ScavengingState.storeSession(sessionId, gameSession);
        if (!stored) {
            return { error: "Failed to create scavenging session", statusCode: 500 };
        }

        // Create obfuscated data for client
        const obfuscatedGrid = createObfuscatedGrid(grid, gridSize);
        const obfuscatedMultiCellResources = createObfuscatedMultiCellResources(multiCellResources, grid);
        const enhancedResources = await createEnhancedResources(gameSession.resources);

        return {
            data: {
                success: true,
                sessionId,
                grid: obfuscatedGrid,
                multiCellResources: obfuscatedMultiCellResources,
                energy: gameSession.energy,
                gameOver: gameSession.gameOver,
                resources: enhancedResources,
                gridSize: gridSize,
                difficultyTier: tier,
                timestamp: new Date().toISOString(),
            },
        };
    } catch (error) {
        LogErrorStack({ message: "Error starting scavenging", error });
        return { error: "Failed to start scavenging", statusCode: 500 };
    }
};

export const revealCell = async (userId: number, sessionId: string, row: number, col: number) => {
    try {
        if (!sessionId || row === undefined || col === undefined) {
            return { error: "Missing required parameters", statusCode: 400 };
        }

        // Get session from Redis
        const gameSession = await ScavengingState.getSession(sessionId);
        if (!gameSession) {
            return { error: "Scavenging session not found or expired", statusCode: 404 };
        }

        // Verify session belongs to user
        if (gameSession.userId !== userId) {
            return { error: "Session does not belong to user", statusCode: 403 };
        }

        // Check if game is over or cell is already revealed
        if (gameSession.gameOver || gameSession.energy <= 0) {
            return { error: "Scavenging session is over", statusCode: 400 };
        }

        // Validate coordinates
        if (row < 0 || row >= gameSession.gridSize || col < 0 || col >= gameSession.gridSize) {
            return { error: "Invalid cell coordinates", statusCode: 400 };
        }

        const cell = gameSession.fullGrid[row][col];
        if (cell.revealed) {
            return { error: "Cell is already revealed", statusCode: 400 };
        }

        // Decrease cell health
        cell.health -= 1;

        // Only reveal the cell if health reaches 0
        if (cell.health <= 0) {
            cell.revealed = true;
        }

        // Decrease energy
        gameSession.energy -= 1;
        gameSession.gameOver = gameSession.energy <= 0;

        // Check for completed multi-cell resources (only if cell was revealed)
        if (cell.revealed) {
            for (const resource of gameSession.multiCellResources) {
                const revealedCells = resource.cells.filter(
                    ({ row, col }: CellPosition) => gameSession.fullGrid[row][col].revealed
                ).length;

                const wasFullyRevealed = resource.fullyRevealed;
                resource.revealedCells = revealedCells;
                resource.fullyRevealed = revealedCells === resource.cells.length;

                // If resource was just completed, add to inventory
                if (
                    resource.fullyRevealed &&
                    !wasFullyRevealed &&
                    resource.type !== "empty" &&
                    resource.type !== "hidden" &&
                    typeof resource.type === "number"
                ) {
                    // Ensure the resource exists in the resources object
                    if (!(resource.type in gameSession.resources)) {
                        gameSession.resources[resource.type] = 0;
                    }
                    gameSession.resources[resource.type] += 1;
                }
            }
        }

        // Update session in Redis
        const updated = await ScavengingState.updateSession(sessionId, gameSession);
        if (!updated) {
            return { error: "Failed to update scavenging session", statusCode: 500 };
        }

        // Create obfuscated response data
        const obfuscatedGrid = createObfuscatedGrid(gameSession.fullGrid, gameSession.gridSize);
        const obfuscatedMultiCellResources = createObfuscatedMultiCellResources(
            gameSession.multiCellResources,
            gameSession.fullGrid
        );
        const enhancedResources = await createEnhancedResources(gameSession.resources);

        const revealedCellInfo: RevealedCellInfo = {
            row,
            col,
            resourceType: cell.revealed ? cell.resourceType : "hidden",
            resourceId: cell.revealed ? cell.resourceId : null,
            isPartOfMultiCell: cell.revealed ? cell.isPartOfMultiCell : false,
        };

        return {
            data: {
                success: true,
                grid: obfuscatedGrid,
                multiCellResources: obfuscatedMultiCellResources,
                energy: gameSession.energy,
                gameOver: gameSession.gameOver,
                resources: enhancedResources,
                revealedCell: revealedCellInfo,
                cellDamaged: !cell.revealed, // Indicates if cell was damaged but not revealed
                cellHealth: cell.health,
                cellMaxHealth: cell.maxHealth,
            },
        };
    } catch (error) {
        LogErrorStack({ message: "Error revealing cell in scavenging", error });
        return { error: "Failed to reveal cell", statusCode: 500 };
    }
};

export const displayDevGrid = async (sessionId: string) => {
    try {
        if (!sessionId) {
            return { error: "Missing sessionId", statusCode: 400 };
        }

        // Get session from Redis
        const gameSession = await ScavengingState.getSession(sessionId);
        if (!gameSession) {
            return { error: "Session not found or expired", statusCode: 404 };
        }

        // Update last activity in Redis
        const updated = await ScavengingState.updateSession(sessionId, gameSession);
        if (!updated) {
            return { error: "Failed to update session activity", statusCode: 500 };
        }

        // Return the full grid with all resources revealed for dev mode
        const devGrid = gameSession.fullGrid.map((row: Cell[]) =>
            row.map((cell: Cell) => ({
                ...cell,
                devModeVisible: true, // Mark cells as visible in dev mode
            }))
        );

        return {
            data: {
                success: true,
                sessionId,
                fullGrid: devGrid,
                multiCellResources: gameSession.multiCellResources,
                energy: gameSession.energy,
                gameOver: gameSession.gameOver,
                resources: gameSession.resources,
                gridSize: gameSession.gridSize,
                difficultyTier: gameSession.difficultyTier,
                message: "Full grid data for dev mode - all resources revealed",
            },
        };
    } catch (error) {
        LogErrorStack({ message: "Error displaying dev grid for scavenging", error });
        return { error: "Failed to display dev grid", statusCode: 500 };
    }
};

/**
 * Get the active scavenging session for a user
 */
export const getActiveSession = async (userId: number) => {
    try {
        const gameSession = await ScavengingState.getUserActiveSession(userId);

        if (!gameSession) {
            return { error: "No active scavenging session found", statusCode: 404 };
        }

        // Create obfuscated data for client
        const obfuscatedGrid = createObfuscatedGrid(gameSession.fullGrid, gameSession.gridSize);
        const obfuscatedMultiCellResources = createObfuscatedMultiCellResources(
            gameSession.multiCellResources,
            gameSession.fullGrid
        );
        const enhancedResources = await createEnhancedResources(gameSession.resources);

        return {
            data: {
                success: true,
                sessionId: gameSession.sessionId,
                grid: obfuscatedGrid,
                multiCellResources: obfuscatedMultiCellResources,
                energy: gameSession.energy,
                gameOver: gameSession.gameOver,
                resources: enhancedResources,
                gridSize: gameSession.gridSize,
                difficultyTier: gameSession.difficultyTier,
                timestamp: new Date().toISOString(),
            },
        };
    } catch (error) {
        LogErrorStack({ message: "Error getting active scavenging session", error });
        return { error: "Failed to get active session", statusCode: 500 };
    }
};

/**
 * End a scavenging session and give collected resources to the user
 */
export const endSession = async (userId: number, sessionId: string) => {
    try {
        if (!sessionId) {
            return { error: "Missing sessionId", statusCode: 400 };
        }

        // Get session from Redis to verify ownership
        const gameSession = await ScavengingState.getSession(sessionId);
        if (!gameSession) {
            return { error: "Session not found or expired", statusCode: 404 };
        }

        // Verify session belongs to user
        if (gameSession.userId !== userId) {
            return { error: "Session does not belong to user", statusCode: 403 };
        }

        // Give collected resources to the user
        const itemsGiven: { itemId: number; count: number }[] = [];

        for (const [itemIdStr, count] of Object.entries(gameSession.resources)) {
            if (count > 0) {
                const itemId = Number.parseInt(itemIdStr, 10);

                // Add items to user's inventory
                await InventoryService.AddItemToUser({
                    userId,
                    itemId,
                    amount: count,
                    isTradeable: true,
                });

                itemsGiven.push({ itemId, count });
            }
        }

        // Delete session from Redis
        const deleted = await ScavengingState.deleteSession(sessionId, userId);
        if (!deleted) {
            return { error: "Failed to end session", statusCode: 500 };
        }

        const enhancedResources = await createEnhancedResources(gameSession.resources);

        return {
            data: {
                success: true,
                message: "Scavenging session ended successfully",
                finalResources: enhancedResources,
                itemsGiven,
            },
        };
    } catch (error) {
        LogErrorStack({ message: "Error ending scavenging session", error });
        return { error: "Failed to end session", statusCode: 500 };
    }
};

/**
 * Reset a scavenging session back to its original state (Admin/Dev only)
 */
export const resetGrid = async (userId: number, sessionId: string) => {
    try {
        if (!sessionId) {
            return { error: "Missing sessionId", statusCode: 400 };
        }

        // Get session from Redis
        const gameSession = await ScavengingState.getSession(sessionId);
        if (!gameSession) {
            return { error: "Session not found or expired", statusCode: 404 };
        }

        // Verify session belongs to user
        if (gameSession.userId !== userId) {
            return { error: "Session does not belong to user", statusCode: 403 };
        }

        // Reset all cells to unrevealed state
        for (const row of gameSession.fullGrid) {
            for (const cell of row) {
                cell.revealed = false;
                cell.health = cell.maxHealth; // Reset health to maximum
            }
        }

        // Reset multi-cell resources
        for (const resource of gameSession.multiCellResources) {
            resource.fullyRevealed = false;
            resource.revealedCells = 0;
        }

        // Reset game state and initialize resources properly
        gameSession.energy = DEFAULT_ENERGY; // Reset to initial energy
        gameSession.gameOver = false;

        // Reset resources - reinitialize for all potential resources
        gameSession.resources = {};
        for (const potentialResource of POTENTIAL_RESOURCES) {
            gameSession.resources[potentialResource.itemId] = 0;
        }

        gameSession.lastActivity = new Date();

        // Update session in Redis
        const updated = await ScavengingState.updateSession(sessionId, gameSession);
        if (!updated) {
            return { error: "Failed to reset scavenging session", statusCode: 500 };
        }

        // Create obfuscated data for client
        const obfuscatedGrid = createObfuscatedGrid(gameSession.fullGrid, gameSession.gridSize);
        const obfuscatedMultiCellResources = createObfuscatedMultiCellResources(
            gameSession.multiCellResources,
            gameSession.fullGrid
        );
        const enhancedResources = await createEnhancedResources(gameSession.resources);

        return {
            data: {
                success: true,
                message: "Scavenging session reset successfully",
                sessionId,
                grid: obfuscatedGrid,
                multiCellResources: obfuscatedMultiCellResources,
                energy: gameSession.energy,
                gameOver: gameSession.gameOver,
                resources: enhancedResources,
                gridSize: gameSession.gridSize,
                difficultyTier: gameSession.difficultyTier,
                timestamp: new Date().toISOString(),
            },
        };
    } catch (error) {
        LogErrorStack({ message: "Error resetting scavenging session", error });
        return { error: "Failed to reset session", statusCode: 500 };
    }
};
