const DISCORD_BOT_TOKEN = process.env.DISCORD_BOT_TOKEN;
const DISCORD_SERVER_ID = "444980038811648010";
const DISCORD_ROLE_ID = "445987492378836993";
export const FetchUserDetails = async (token) => {
    const url = "https://discord.com/api/users/@me";
    const response = await fetch(url, {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
};
export const AssignRoleToUser = async (userId) => {
    const url = `https://discord.com/api/guilds/${DISCORD_SERVER_ID}/members/${userId}/roles/${DISCORD_ROLE_ID}`;
    const response = await fetch(url, {
        method: "PUT",
        headers: {
            Authorization: `Bot ${DISCORD_BOT_TOKEN}`,
            "Content-Type": "application/json",
        },
    });
    if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
    }
    return null;
};
export const JoinServer = async (token, userId) => {
    const url = `https://discord.com/api/guilds/${DISCORD_SERVER_ID}/members/${userId}`;
    const body = {
        access_token: token,
    };
    const response = await fetch(url, {
        method: "PUT",
        headers: {
            Authorization: `Bot ${DISCORD_BOT_TOKEN}`,
            "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
    });
    if (!response.ok) {
        throw new Error(`Error adding user to server: ${response.statusText}`);
    }
    return null;
};
