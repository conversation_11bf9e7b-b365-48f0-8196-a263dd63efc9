import { logPlayerAction } from "../../lib/actionLogger.js";
import { handleError, handleInternalError, logger } from "../../utils/log.js";
import { PLAYER_NODE_CONFIG, TRAVEL_COSTS, TRAVEL_TIMES } from "./explore.constants.js";
import * as ExploreHelpers from "./explore.helpers.js";
import * as ExploreRepository from "../../repositories/explore.repository.js";
export const getExploreMapByLocation = async (userId, location) => {
    if (!location) {
        return handleInternalError("No current location found");
    }
    const expiredCount = await ExploreRepository.cleanupExpiredPlayerNodes(userId);
    if (expiredCount > 0) {
        logger.info(`Cleaned up ${expiredCount} expired nodes for user ${userId}`);
    }
    await ExploreHelpers.generatePlayerNodesIfNeeded(userId, location);
    const staticNodes = await ExploreRepository.getStaticNodesByLocation(location);
    const playerNodes = await ExploreRepository.getActivePlayerNodesByLocation(userId, location);
    const enabledStaticNodes = staticNodes.filter((node) => !ExploreHelpers.isNodeTypeDisabled(node.nodeType));
    const enabledPlayerNodes = playerNodes.filter((node) => !ExploreHelpers.isNodeTypeDisabled(node.nodeType));
    const combinedNodes = [
        ...enabledStaticNodes.map((node) => ({
            id: -node.id,
            nodeType: node.nodeType,
            title: node.title,
            description: node.description,
            position: node.position,
            metadata: node.metadata,
            status: "available",
            isStatic: true,
            location: node.location,
            shopId: node.shopId,
            expiresAt: null,
        })),
        ...enabledPlayerNodes.map((node) => ({
            id: node.id,
            nodeType: node.nodeType,
            title: node.title,
            description: node.description,
            position: node.position,
            metadata: node.metadata,
            status: node.status,
            isStatic: false,
            location: node.location,
            shopId: null,
            expiresAt: node.expiresAt,
        })),
    ];
    logger.info(`Returning ${combinedNodes.length} nodes for user ${userId} in ${location} (${enabledStaticNodes.length}/${staticNodes.length} static, ${enabledPlayerNodes.length}/${playerNodes.length} player)`);
    return { data: combinedNodes };
};
export const interactWithNode = async (userId, nodeId, isStatic) => {
    logger.info(`User ${userId} interacting with node ${nodeId} (static: ${isStatic})`);
    let actualNodeId = nodeId;
    if (isStatic && nodeId < 0) {
        actualNodeId = -nodeId;
    }
    if (isStatic) {
        logger.warn(`User ${userId} attempted to interact with static node ${nodeId}`);
        return handleError("Can't interact with static node", 404);
    }
    const nodeData = await ExploreRepository.lockPlayerNodeForInteraction(actualNodeId, userId);
    if (!nodeData) {
        logger.warn(`Node ${actualNodeId} not found, expired, or already in use for user ${userId}`);
        return handleError("Node not found or has expired", 404);
    }
    logger.info(`Locked player node ${actualNodeId} for interaction by user ${userId}`);
    const interactionResult = await ExploreHelpers.handleNodeInteraction(nodeData.nodeType, nodeData.metadata, userId, actualNodeId, isStatic, nodeData.location);
    if (interactionResult.success) {
        if (nodeData.nodeType === "BATTLE") {
            const completed = await ExploreRepository.completePlayerNodeInteraction(actualNodeId, userId, PLAYER_NODE_CONFIG.COMPLETION_EXPIRATION_MINUTES);
            if (completed) {
                logger.info(`Auto-completed BATTLE node ${actualNodeId} for user ${userId}`);
            }
            else {
                logger.warn(`Failed to auto-complete BATTLE node ${actualNodeId} for user ${userId}`);
            }
        }
    }
    else {
        const unlocked = await ExploreRepository.unlockPlayerNodeOnFailure(actualNodeId, userId);
        if (unlocked) {
            logger.info(`Unlocked player node ${actualNodeId} after failed interaction for user ${userId}`);
        }
        else {
            logger.warn(`Failed to unlock player node ${actualNodeId} after failed interaction for user ${userId}`);
        }
    }
    await logPlayerAction("EXPLORE_NODE_INTERACTION", {
        nodeId: actualNodeId,
        nodeType: nodeData.nodeType,
        isStatic,
        interactionResult: interactionResult.data?.action || "unknown",
    }, userId);
    return {
        data: {
            ...interactionResult,
            nodeId,
            isStatic,
        },
    };
};
export const completeNode = async (userId, nodeId) => {
    logger.info(`User ${userId} completing node ${nodeId}`);
    const completed = await ExploreRepository.completePlayerNodeInteraction(nodeId, userId, PLAYER_NODE_CONFIG.COMPLETION_EXPIRATION_MINUTES);
    if (!completed) {
        logger.warn(`Failed to complete node ${nodeId} for user ${userId} - node not found or not in 'current' status`);
        return handleError("Node not found or not ready for completion", 404);
    }
    logger.info(`Successfully completed player node ${nodeId} for user ${userId}`);
    await logPlayerAction("EXPLORE_NODE_COMPLETION", {
        nodeId,
    }, userId);
    return {
        data: {
            nodeId,
            status: "completed",
            message: "Node completed successfully",
        },
    };
};
export const makeScavengeChoice = async (userId, nodeId, choiceIndex) => {
    logger.info(`User ${userId} making scavenge choice ${choiceIndex} for node ${nodeId}`);
    const nodeData = await ExploreRepository.getPlayerNodeById(nodeId, userId);
    if (!nodeData) {
        logger.warn(`Node ${nodeId} not found for user ${userId}`);
        return handleError("Node not found or has expired", 404);
    }
    if (nodeData.nodeType !== "SCAVENGE_NODE") {
        logger.warn(`User ${userId} tried to make scavenge choice on non-scavenge node ${nodeId}`);
        return handleError("This node is not a scavenging node", 400);
    }
    if (nodeData.status !== "current") {
        logger.warn(`User ${userId} tried to make scavenge choice on node ${nodeId} with status ${nodeData.status}`);
        return handleError("This node is not ready for scavenging choices", 400);
    }
    const currentChoices = nodeData.metadata?.choices;
    if (!currentChoices || currentChoices.length < 2) {
        logger.warn(`Node ${nodeId} does not have valid scavenge choices in metadata`);
        return handleError("No scavenging choices available", 400);
    }
    const { processScavengeChoice } = await import("./nodes/scavenging.node.js");
    const scavengeResult = await processScavengeChoice(userId, nodeId, nodeData.location, choiceIndex, currentChoices);
    if (!scavengeResult.success) {
        logger.warn(`Scavenge choice failed for user ${userId} on node ${nodeId}: ${scavengeResult.message}`);
        return handleError(scavengeResult.message, 400);
    }
    const completed = await ExploreRepository.completePlayerNodeInteraction(nodeId, userId, PLAYER_NODE_CONFIG.COMPLETION_EXPIRATION_MINUTES);
    if (completed) {
        logger.info(`Completed scavenging node ${nodeId} for user ${userId}`);
    }
    else {
        logger.warn(`Failed to complete scavenging node ${nodeId} for user ${userId}`);
    }
    await logPlayerAction("EXPLORE_SCAVENGE_CHOICE", {
        nodeId,
        choiceIndex,
        choice: currentChoices[choiceIndex - 1],
        success: scavengeResult.data?.success || false,
        itemReward: scavengeResult.data?.itemReward?.name || null,
    }, userId);
    logger.info(`User ${userId} successfully made scavenge choice ${choiceIndex} for node ${nodeId}`);
    return {
        data: {
            nodeId,
            choiceIndex,
            ...scavengeResult.data,
        },
    };
};
export const changeMapLocation = async (userId, newLocation, method) => {
    logger.info(`User ${userId} attempting to travel to ${newLocation} via ${method}`);
    ExploreHelpers.validateTravelParameters(method, newLocation);
    const cost = TRAVEL_COSTS[method][newLocation];
    const travelTimeMinutes = TRAVEL_TIMES[method][newLocation];
    const updatedUser = await ExploreRepository.initiateTravel(userId, newLocation, method, cost, travelTimeMinutes);
    await logPlayerAction("EXPLORE_TRAVEL_INITIATED", {
        destination: newLocation,
        method,
        cost,
        travelTimeMinutes,
        newCash: updatedUser.cash,
        travelEndTime: updatedUser.travelEndTime,
    }, userId);
    logger.info(`User ${userId} successfully initiated travel to ${newLocation} via ${method} for ${cost} cash, arriving in ${travelTimeMinutes} minutes`);
    return {
        data: {
            travelingTo: updatedUser.currentMapLocation || newLocation,
            newCash: updatedUser.cash,
            cost,
            travelTime: travelTimeMinutes,
            method,
            travelStartTime: updatedUser.travelStartTime || new Date(),
            travelEndTime: updatedUser.travelEndTime || new Date(Date.now() + travelTimeMinutes * 60 * 1000),
        },
    };
};
export const processMiningOperation = async (userId, nodeId) => {
    logger.info(`User ${userId} processing mining operation for node ${nodeId}`);
    const nodeData = await ExploreRepository.getPlayerNodeById(nodeId, userId);
    if (!nodeData) {
        logger.warn(`Node ${nodeId} not found for user ${userId}`);
        return handleError("Node not found or has expired", 404);
    }
    if (nodeData.nodeType !== "MINING_NODE") {
        logger.warn(`User ${userId} tried to process mining operation on non-mining node ${nodeId}`);
        return handleError("This node is not a mining node", 400);
    }
    if (nodeData.status !== "current") {
        logger.warn(`User ${userId} tried to process mining operation on node ${nodeId} with status ${nodeData.status}`);
        return handleError("This node is not ready for mining", 400);
    }
    const miningType = nodeData.metadata?.miningType;
    const difficulty = nodeData.metadata?.difficulty;
    if (!miningType || !difficulty) {
        logger.warn(`Node ${nodeId} does not have valid mining operation details in metadata`);
        return handleError("No mining operation details available", 400);
    }
    const { processMiningOperation: processOperation } = await import("./nodes/mining.node.js");
    const miningResult = await processOperation(userId, nodeId, nodeData.location, miningType, difficulty);
    if (!miningResult.success) {
        logger.warn(`Mining operation failed for user ${userId} on node ${nodeId}: ${miningResult.message}`);
        return handleError(miningResult.message, 400);
    }
    const completed = await ExploreRepository.completePlayerNodeInteraction(nodeId, userId, PLAYER_NODE_CONFIG.COMPLETION_EXPIRATION_MINUTES);
    if (completed) {
        logger.info(`Completed mining node ${nodeId} for user ${userId}`);
    }
    else {
        logger.warn(`Failed to complete mining node ${nodeId} for user ${userId}`);
    }
    await logPlayerAction("EXPLORE_MINING_OPERATION", {
        nodeId,
        miningType,
        difficulty,
        success: miningResult.data?.success || false,
        itemReward: miningResult.data?.itemReward?.name || null,
        injury: miningResult.data?.injury?.name || null,
    }, userId);
    logger.info(`User ${userId} successfully processed mining operation for node ${nodeId}`);
    return {
        data: {
            nodeId,
            ...miningResult.data,
        },
    };
};
export const processForagingOperation = async (userId, nodeId) => {
    logger.info(`User ${userId} processing foraging operation for node ${nodeId}`);
    const nodeData = await ExploreRepository.getPlayerNodeById(nodeId, userId);
    if (!nodeData) {
        logger.warn(`Node ${nodeId} not found for user ${userId}`);
        return handleError("Node not found or has expired", 404);
    }
    if (nodeData.nodeType !== "FORAGING_NODE") {
        logger.warn(`User ${userId} tried to process foraging operation on non-foraging node ${nodeId}`);
        return handleError("This node is not a foraging node", 400);
    }
    if (nodeData.status !== "current") {
        logger.warn(`User ${userId} tried to process foraging operation on node ${nodeId} with status ${nodeData.status}`);
        return handleError("This node is not ready for foraging", 400);
    }
    const foragingType = nodeData.metadata?.foragingType || "herbs";
    const difficulty = nodeData.metadata?.difficulty || "easy";
    const { processForaging: processOperation } = await import("./nodes/foraging.node.js");
    const foragingResult = await processOperation(userId, nodeId, nodeData.location, foragingType, difficulty);
    if (!foragingResult.success) {
        logger.warn(`Foraging operation failed for user ${userId} on node ${nodeId}: ${foragingResult.message}`);
        return handleError(foragingResult.message, 400);
    }
    const completed = await ExploreRepository.completePlayerNodeInteraction(nodeId, userId, PLAYER_NODE_CONFIG.COMPLETION_EXPIRATION_MINUTES);
    if (completed) {
        logger.info(`Completed foraging node ${nodeId} for user ${userId}`);
    }
    else {
        logger.warn(`Failed to complete foraging node ${nodeId} for user ${userId}`);
    }
    await logPlayerAction("EXPLORE_FORAGING_OPERATION", {
        nodeId,
        foragingType,
        difficulty,
        success: foragingResult.data?.success || false,
        itemReward: foragingResult.data?.itemReward?.name || null,
        injury: foragingResult.data?.injury?.name || null,
        experienceGained: foragingResult.data?.experienceGained || null,
    }, userId);
    logger.info(`User ${userId} successfully processed foraging operation for node ${nodeId}`);
    return {
        data: {
            nodeId,
            ...foragingResult.data,
        },
    };
};
export const getTravelStatus = async (userId) => {
    logger.info(`Getting travel status for user ${userId}`);
    const completionResult = await ExploreRepository.autoCompleteTravelIfNeeded(userId);
    if (completionResult.autoCompleted && completionResult.completedDestination) {
        await logPlayerAction("EXPLORE_TRAVEL_COMPLETED", {
            newLocation: completionResult.completedDestination,
            previousLocation: null,
            autoCompleted: true,
        }, userId);
        logger.info(`User ${userId} travel auto-completed to ${completionResult.completedDestination}`);
    }
    const travelStatus = await ExploreRepository.getUserTravelStatusOnly(userId);
    return {
        data: travelStatus,
    };
};
