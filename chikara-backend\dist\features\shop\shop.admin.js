import * as ShopRepository from "../../repositories/shop.repository.js";
import { LogErrorStack, logger } from "../../utils/log.js";
export const createShop = async (body) => {
    try {
        const data = {
            name: body.name,
            shopType: body.shopType,
            avatar: body.avatar,
            description: body.description,
            ...(body.disabled !== undefined && { disabled: body.disabled }),
        };
        const shop = await ShopRepository.createShop(data);
        logger.info("Created new shop: " + JSON.stringify(shop));
        return { data: shop };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to create shop:", error });
        return { error: "Failed to create shop" };
    }
};
export const editShop = async (shopId, body) => {
    try {
        const shopToUpdate = await ShopRepository.findShopById(shopId);
        if (!shopToUpdate) {
            return { error: "Shop not found" };
        }
        const updatedShop = await ShopRepository.updateShop(shopToUpdate.id, body);
        return { data: updatedShop };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to edit shop:", error });
        return { error: "Failed to edit shop" };
    }
};
export const deleteShop = async (shopId) => {
    try {
        await ShopRepository.deleteShop(shopId);
        return { data: { success: true } };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to delete shop:", error });
        return { error: "Failed to delete shop" };
    }
};
export const createShopListing = async (body) => {
    try {
        const data = {
            shop: { connect: { id: body.shopId } },
            item: { connect: { id: body.itemId } },
        };
        if (body.customCost !== undefined)
            data.customCost = body.customCost;
        if (body.repRequired !== undefined)
            data.repRequired = body.repRequired;
        if (body.stock !== undefined)
            data.stock = body.stock;
        if (body.currency !== undefined)
            data.currency = body.currency;
        const newShopListing = await ShopRepository.createShopListing(data);
        if (!newShopListing) {
            return { error: "Failed to create shop listing" };
        }
        return { data: newShopListing };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to create shop listing:", error });
        return { error: "Failed to create shop listing" };
    }
};
export const editShopListing = async (listingId, body) => {
    try {
        const shopListingToUpdate = await ShopRepository.findShopListingById(listingId);
        if (!shopListingToUpdate) {
            return { error: "Shop listing not found" };
        }
        const updateValues = {};
        if (body.shopId !== undefined) {
            updateValues.shop = { connect: { id: Number(body.shopId) } };
        }
        if (body.itemId !== undefined) {
            updateValues.item = { connect: { id: Number(body.itemId) } };
        }
        const directUpdateParams = ["customCost", "repRequired", "stock", "currency"];
        for (const param of directUpdateParams) {
            if (body[param] !== undefined) {
                if (param === "currency") {
                    updateValues[param] = body[param];
                }
                else if (["customCost", "repRequired", "stock"].includes(param)) {
                    updateValues[param] = Number(body[param]);
                }
            }
        }
        const updatedListing = await ShopRepository.updateShopListing(listingId, updateValues);
        return { data: updatedListing };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to edit shop listing:", error });
        return { error: "Failed to edit shop listing" };
    }
};
export const deleteShopListing = async (listingId) => {
    try {
        await ShopRepository.deleteShopListing(listingId);
        return { data: { success: true } };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to delete shop listing:", error });
        return { error: "Failed to delete shop listing" };
    }
};
