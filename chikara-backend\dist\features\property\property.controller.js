import * as propertyRepository from "../../repositories/property.repository.js";
import { LogErrorStack } from "../../utils/log.js";
import * as UserRepository from "../../repositories/user.repository.js";
export const getHousingList = async () => {
    try {
        const housingList = await propertyRepository.findHousingList();
        if (!housingList) {
            throw new Error("Property list not found");
        }
        return housingList;
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
};
export const purchaseProperty = async (propertyId, userId) => {
    try {
        const [property, currentUser] = await Promise.all([
            propertyRepository.findPropertyById(propertyId),
            UserRepository.getUserById(userId),
        ]);
        if (!property) {
            return { error: "Property not found" };
        }
        if (!currentUser) {
            return { error: "User not found", statusCode: 404 };
        }
        if (currentUser.cash < property.cost) {
            return { error: "Not enough cash to complete purchase." };
        }
        const existingProperty = await propertyRepository.findUserPropertyByUserAndPropertyId(userId, propertyId);
        if (existingProperty) {
            return { error: "You already own this property" };
        }
        const userProperties = await propertyRepository.findFirstUserProperty(userId);
        const result = await propertyRepository.executePropertyPurchase(userId, propertyId, currentUser.cash, property.cost, !userProperties);
        return { data: result[1] };
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
};
export const sellProperty = async (propertyId, userId) => {
    try {
        const userProperty = await propertyRepository.findUserPropertyByUserAndPropertyIdWithProperty(userId, propertyId);
        if (!userProperty) {
            return { error: "You don't own this property" };
        }
        if (userProperty.isPrimary) {
            return { error: "Cannot sell primary property" };
        }
        const user = await UserRepository.getUserById(userId);
        const sellPrice = Math.floor(userProperty.property.cost * 0.2);
        if (!user) {
            return { error: "User not found", statusCode: 404 };
        }
        const result = await propertyRepository.executePropertySale(userProperty.id, userId, user.cash, sellPrice);
        return {
            data: {
                soldFor: sellPrice,
                newBalance: result[1].cash,
            },
        };
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
};
export const getUserProperties = async (userId) => {
    try {
        const userProperties = await propertyRepository.findUserPropertiesWithDetails(userId);
        return { data: userProperties };
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
};
export const setPrimaryProperty = async (propertyId, userId) => {
    try {
        const targetProperty = await propertyRepository.findUserPropertyByUserAndPropertyId(userId, propertyId);
        if (!targetProperty) {
            return { error: "You don't own this property" };
        }
        if (targetProperty.isPrimary) {
            return { error: "This property is already set as primary" };
        }
        const currentPrimary = await propertyRepository.findPrimaryUserProperty(userId);
        const result = await propertyRepository.executeSetPrimaryProperty(targetProperty.id, currentPrimary?.id || null);
        return { data: result[0] };
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
};
export default { getHousingList, getUserProperties, purchaseProperty, sellProperty, setPrimaryProperty };
