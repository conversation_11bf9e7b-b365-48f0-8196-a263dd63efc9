import { logger, LogErrorStack } from "../../../utils/log.js";
import * as AchievementService from "../../achievement.service.js";
import * as QuestService from "../../quest.service.js";
export const handleBountyPlacedEvent = async (payload) => {
    try {
        const { userId, targetId, amount } = payload;
        await QuestService.handleBountyPlaced(userId, amount);
        await AchievementService.UpdateUserAchievement(userId, "totalBountyPlaced", amount);
        logger.debug(`Processed bounty placed event objectives for user ${userId}, target ${targetId}, amount ${amount}`);
    }
    catch (error) {
        LogErrorStack({ message: "Error handling bounty placed event objectives", error });
    }
};
export const handleBountyCollectedEvent = async (payload) => {
    try {
        const { userId, bountyId, amount } = payload;
        await QuestService.handleBountyCollection(userId);
        await AchievementService.UpdateUserAchievement(userId, "totalBountyRewards", amount);
        logger.debug(`Processed bounty collected event objectives for user ${userId}, bounty ${bountyId}, amount ${amount}`);
    }
    catch (error) {
        LogErrorStack({ message: "Error handling bounty collected event objectives", error });
    }
};
