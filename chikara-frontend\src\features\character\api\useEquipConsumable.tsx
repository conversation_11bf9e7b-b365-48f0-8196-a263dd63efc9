import { APIROUTES } from "@/helpers/apiRoutes";
import { handlePost } from "@/helpers/axiosInstance";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

type EquipConsumableParams = {
    userItemId: number;
    slot: string;
};

type EquipConsumableResponse = {
    message: string;
};

/**
 * Custom hook to equip a consumable to a slot
 */
export const useEquipConsumable = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation<EquipConsumableResponse, Error, EquipConsumableParams>({
        mutationFn: async (params: EquipConsumableParams) => {
            const data = await handlePost<EquipConsumableResponse>(APIROUTES.USER.EQUIPCONSUMABLE, params);
            return data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: APIROUTES.USER.EQUIPPEDCONSUMABLES });
            toast.success("Consumable equipped");

            if (onSuccessCallback) {
                onSuccessCallback();
            }
        },
        onError: (error: Error) => {
            toast.error(error.message || "Failed to equip consumable");
        },
    });
};

export default useEquipConsumable;
