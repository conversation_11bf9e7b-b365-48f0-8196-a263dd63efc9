import { ENCOUNTER_LOCATIONS } from "./roguelike.constants.js";
import z from "zod";
const roguelikeValidation = {
    newRun: z.object({
        level: z.number().int().positive(),
        location: z.enum(ENCOUNTER_LOCATIONS),
    }),
    advance: z.object({
        node: z.number().int().nonnegative(),
    }),
    chooseScavengeOption: z.object({
        choice: z.number().int().min(1).max(2),
    }),
};
export default roguelikeValidation;
