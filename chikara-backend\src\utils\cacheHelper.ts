import ms, { type StringValue } from "ms";

interface Cache<T = unknown> {
    data: T;
    lastFetch: number;
    isValid: () => boolean;
    set: (data: T) => Cache<T>;
    get: () => { data: T; lastFetch: number };
}

function createCache<T = unknown>(defaultDuration: StringValue = "2h"): Cache<T> {
    return {
        data: null as T,
        lastFetch: 0,
        isValid: function () {
            const cacheDuration = ms(defaultDuration);
            return Date.now() - this.lastFetch < cacheDuration;
        },
        set: function (data: T) {
            this.data = data;
            this.lastFetch = Date.now();
            return this;
        },
        get: function () {
            return {
                data: this.data,
                lastFetch: this.lastFetch,
            };
        },
    };
}

export { createCache };
