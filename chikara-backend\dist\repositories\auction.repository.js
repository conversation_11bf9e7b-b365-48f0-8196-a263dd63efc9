import { db } from "../lib/db.js";
export const findAuctionItems = async (date) => {
    return await db.auction_item.findMany({
        where: {
            status: "in_progress",
            endsAt: { gt: date },
        },
        include: {
            item: true,
            user: {
                select: {
                    id: true,
                    username: true,
                    avatar: true,
                },
            },
        },
        orderBy: {
            endsAt: "asc",
        },
    });
};
export const createAuctionItem = async (data) => {
    return await db.auction_item.create({
        data: {
            ...data,
            status: "in_progress",
        },
    });
};
export const findActiveAuctionItem = async (auctionItemId) => {
    return await db.auction_item.findFirst({
        where: {
            id: auctionItemId,
            status: "in_progress",
        },
        include: {
            item: {
                select: {
                    id: true,
                    name: true,
                },
            },
            user: {
                select: {
                    id: true,
                    username: true,
                },
            },
        },
    });
};
export const updateAuctionItemStatus = async (auctionItemId, status) => {
    return await db.auction_item.update({
        where: { id: auctionItemId },
        data: { status },
    });
};
export const updateAuctionItemQuantity = async (auctionItemId, quantity) => {
    return await db.auction_item.update({
        where: { id: auctionItemId },
        data: { quantity },
    });
};
export const updateBuyerCashTransaction = async (prisma, buyerId, newCash) => {
    return await prisma.user.update({
        where: { id: buyerId },
        data: { cash: newCash },
    });
};
export const findSellerTransaction = async (prisma, sellerId) => {
    return await prisma.user.findUnique({
        where: { id: sellerId },
    });
};
export const updateSellerBalanceTransaction = async (prisma, sellerId, bankFunds, bankBalance, cash) => {
    return await prisma.user.update({
        where: { id: sellerId },
        data: bankFunds ? { bank_balance: bankBalance } : { cash },
    });
};
export const updateAuctionItemTransaction = async (prisma, auctionItemId, newQuantity, status) => {
    return await prisma.auction_item.update({
        where: { id: auctionItemId },
        data: {
            quantity: newQuantity,
            status,
        },
        include: {
            item: true,
            user: true,
        },
    });
};
export const updateUserBankBalanceTransaction = async (prisma, userId, bankBalance) => {
    return await prisma.user.update({
        where: { id: userId },
        data: { bank_balance: bankBalance },
    });
};
export const updateUserCashTransaction = async (prisma, userId, cash) => {
    return await prisma.user.update({
        where: { id: userId },
        data: { cash },
    });
};
export const findAuctionItemWithDetails = async (auctionItemId) => {
    return await db.auction_item.findFirst({
        where: {
            id: auctionItemId,
            status: "in_progress",
        },
        include: {
            item: {
                select: {
                    id: true,
                    name: true,
                },
            },
            user: {
                select: {
                    id: true,
                    username: true,
                },
            },
        },
    });
};
export const executeAuctionTransaction = async (prisma, buyerId, buyerNewCash, sellerId, sellerNewCash, auctionItemId, newQuantity, status, bankFunds = false, sellerBankBalance) => {
    await updateUserCashTransaction(prisma, buyerId, buyerNewCash);
    if (bankFunds && sellerBankBalance !== undefined) {
        await updateUserBankBalanceTransaction(prisma, sellerId, sellerBankBalance);
    }
    else {
        await updateUserCashTransaction(prisma, sellerId, sellerNewCash);
    }
    return updateAuctionItemTransaction(prisma, auctionItemId, newQuantity, status);
};
