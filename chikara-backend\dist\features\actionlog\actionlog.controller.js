import { getLatestPlayerLogs, getLatestUserActions } from "../../lib/actionLogger.js";
export const GetMostRecentActions = async (limit = 100, _search) => {
    const logs = await getLatestPlayerLogs(limit);
    const actions = logs.records.map((record) => ({
        id: record.id,
        userId: record.userId || 0,
        action: record.action || "",
        info: record.info,
        createdAt: record.xata.createdAt,
        logType: record.logType || "player",
        metadata: record.metadata,
    }));
    return { data: actions };
};
export const GetUserActions = async (userId, limit = 100, _search) => {
    const logs = await getLatestUserActions(userId, { limit });
    const actions = logs.records.map((record) => ({
        id: record.id,
        userId: record.userId || 0,
        action: record.action || "",
        info: record.info,
        createdAt: record.xata.createdAt,
        logType: record.logType || "player",
        metadata: record.metadata,
    }));
    return { data: actions };
};
export const GetAllActionsAffectingUser = async (userId, limit = 100, search) => {
    return { data: null };
};
