import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as InfirmaryController from "./infirmary.controller.js";
import infirmarySchema from "./infirmary.validation.js";
export const infirmaryRouter = {
    getHospitalList: isLoggedInAuth.handler(async () => {
        const response = await InfirmaryController.getHospitalList();
        return handleResponse(response);
    }),
    getInjuredList: isLoggedInAuth.handler(async ({ context }) => {
        const response = await InfirmaryController.getInjuredList(context.user.id);
        return handleResponse(response);
    }),
    revivePlayer: canMakeStateChangesAuth.input(infirmarySchema.revivePlayer).handler(async ({ input, context }) => {
        const response = await InfirmaryController.revivePlayer(context.user.id, input.targetId);
        return handleResponse(response);
    }),
    hospitalCheckIn: canMakeStateChangesAuth
        .input(infirmarySchema.hospitalCheckIn)
        .handler(async ({ input, context }) => {
        const response = await InfirmaryController.hospitalCheckIn(context.user.id, input.injuryOnly);
        return handleResponse(response);
    }),
};
