import LoadingState from "@/components/LoadingState";
import { Coins } from "lucide-react";
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import useRemoveRival from "../api/useRemoveRival";
import useUpdateRivalNote from "../api/useUpdateRivalNote";
import type { Rival } from "../types/social";
import { EditNoteButton, RemoveButton } from "./common/ActionButtons";
import { ConfirmRemoveDialog } from "./common/ConfirmRemoveDialog";
import { EmptyStateMessage } from "./common/EmptyStateMessage";
import { ListSection } from "./common/ListSection";
import { NoteDialog } from "./common/NoteDialog";
import { UserInfo } from "./common/UserInfo";

interface RivalItemProps {
    rival: Rival;
}

function RivalItem({ rival }: RivalItemProps) {
    const [noteDialogOpen, setNoteDialogOpen] = useState(false);
    const [confirmRemoveOpen, setConfirmRemoveOpen] = useState(false);

    const { mutate: removeRival, isPending: isRemovePending } = useRemoveRival();
    const { mutate: updateNote, isPending: isUpdateNotePending } = useUpdateRivalNote();

    const handleRemoveRival = () => {
        removeRival({ rivalId: rival.rivalId });
    };

    const handleUpdateNote = (note: string) => {
        updateNote({ rivalId: rival.rivalId, note });
    };

    // Get total bounty amount
    const totalBounty = rival.rival.targetedBounties?.reduce((total, bounty) => total + bounty.amount, 0) || 0;

    if (!rival) return null;

    return (
        <div className="flex items-center justify-between p-2 rounded-lg bg-gray-900/50 hover:bg-gray-900/80 transition-colors">
            <UserInfo user={rival.rival} note={rival.note} avatarType="rival" />

            <div className="flex items-center gap-2">
                {totalBounty > 0 ? (
                    <Link
                        to="/bountyboard"
                        className="p-1.5 bg-amber-700 hover:bg-amber-600 text-white rounded-lg transition-colors flex items-center gap-1.5"
                        title="View Bounty"
                    >
                        <Coins className="size-4 lg:size-5" />
                        <span className="text-xs lg:text-sm font-medium">${totalBounty.toLocaleString()}</span>
                    </Link>
                ) : null}
                <EditNoteButton onClick={() => setNoteDialogOpen(true)} />
                <RemoveButton onClick={() => setConfirmRemoveOpen(true)} />
            </div>

            {/* Note Dialog */}
            <NoteDialog
                open={noteDialogOpen}
                initialNote={rival.note || ""}
                isPending={isUpdateNotePending}
                title="Rival Note"
                onOpenChange={setNoteDialogOpen}
                onSave={handleUpdateNote}
            />

            {/* Confirm Remove Dialog */}
            <ConfirmRemoveDialog
                open={confirmRemoveOpen}
                isPending={isRemovePending}
                title="Remove Rival"
                itemType="Rival"
                username={rival.rival.username}
                onOpenChange={setConfirmRemoveOpen}
                onConfirm={handleRemoveRival}
            />
        </div>
    );
}

interface RivalsListProps {
    rivalsList: Rival[] | undefined;
    isLoading: boolean;
}

export default function RivalsList({ rivalsList, isLoading }: RivalsListProps) {
    return (
        <div className="px-2 py-1.5">
            <LoadingState isLoading={isLoading}>
                <div className="space-y-2">
                    {rivalsList?.length ? (
                        <ListSection title="Rivals" count={rivalsList.length} colorScheme="red">
                            {rivalsList.map((rival) => (
                                <RivalItem key={rival.id} rival={rival} />
                            ))}
                        </ListSection>
                    ) : (
                        <EmptyStateMessage itemType="Rivals" />
                    )}
                </div>
            </LoadingState>
        </div>
    );
}
