import { db } from "../lib/db.js";
import { logger } from "../utils/log.js";
import { ItemQuality, ItemTypes } from "@prisma/client";
export const IsItemEquippable = (item) => {
    if (!item)
        return false;
    return ["head", "chest", "hands", "legs", "feet", "finger", "offhand", "shield", "weapon", "ranged"].includes(item?.itemType);
};
const findEquippedItemsWithDetails = async (userId) => {
    return await db.equipped_item.findMany({
        where: { userId },
        include: {
            user_item: {
                include: {
                    item: true,
                },
            },
        },
    });
};
export const findEquippedItemBySlot = async (userId, slot) => {
    return await db.equipped_item.findFirst({
        where: { userId, slot },
        include: {
            user_item: {
                include: {
                    item: true,
                },
            },
        },
    });
};
export const GetEquippedItems = async (userId) => {
    const equippedItems = await db.equipped_item.findMany({
        where: { userId },
        include: {
            user_item: {
                include: {
                    item: true,
                },
            },
        },
    });
    const formattedEquippedItems = {
        [ItemTypes.head]: null,
        [ItemTypes.chest]: null,
        [ItemTypes.hands]: null,
        [ItemTypes.legs]: null,
        [ItemTypes.feet]: null,
        [ItemTypes.finger]: null,
        [ItemTypes.weapon]: null,
        [ItemTypes.ranged]: null,
        [ItemTypes.offhand]: null,
        [ItemTypes.shield]: null,
    };
    if (!equippedItems)
        return formattedEquippedItems;
    for (const equippedItem of equippedItems) {
        const userItem = equippedItem.user_item;
        if (!userItem || !userItem.item) {
            continue;
        }
        const itemData = {
            ...userItem.item,
            userItemId: userItem.id,
            upgradeLevel: userItem.upgradeLevel ?? 0,
            quality: userItem.quality ?? ItemQuality.normal,
            itemEffects: userItem.item.itemEffects ?? null,
        };
        formattedEquippedItems[equippedItem.slot] = itemData;
    }
    return formattedEquippedItems;
};
export const GetEquippedItem = async (user, itemType) => {
    const equippedItem = await findEquippedItemBySlot(user.id, itemType);
    if (!equippedItem?.user_item?.item) {
        return null;
    }
    const userItem = equippedItem.user_item;
    const upgradeLevel = userItem.upgradeLevel ?? 0;
    return upgradeLevel > 0 ? { ...userItem.item, upgradeLevel } : userItem.item;
};
export const GetTotalEquippedValue = async (user, attribute, attackType) => {
    const equippedItems = await findEquippedItemsWithDetails(Number(user.id));
    if (!equippedItems || equippedItems.length === 0)
        return 0;
    let statTotal = 0;
    const processItemStat = (equippedItem) => {
        if (!equippedItem?.user_item?.item)
            return 0;
        const item = equippedItem.user_item.item;
        const statValue = item[attribute] || 0;
        return Math.round(statValue);
    };
    for (const equippedItem of equippedItems) {
        if (attackType) {
            if (attackType === "ranged" && equippedItem.slot !== "ranged")
                continue;
            if (attackType === "melee" && !["weapon", "offhand"].includes(equippedItem.slot)) {
                continue;
            }
        }
        if (attribute === "damage" && !["weapon", "ranged", "offhand"].includes(equippedItem.slot)) {
            continue;
        }
        statTotal += processItemStat(equippedItem);
    }
    return statTotal;
};
export const deleteEquippedItem = async (id, tx) => {
    if (tx) {
        return await tx.equipped_item.delete({
            where: { id },
        });
    }
    return await db.equipped_item.delete({
        where: { id },
    });
};
export const UnequipItemFromSlot = async (userId, slot, tx) => {
    const equippedItem = await findEquippedItemBySlot(userId, slot);
    if (equippedItem) {
        const item = equippedItem.user_item?.item;
        if (!item) {
            throw `Item ${equippedItem.id} does not have an item obj for user ${userId}`;
        }
        await deleteEquippedItem(equippedItem.id, tx);
    }
};
export const CalculateItemEffectValue = (baseValue, effect) => {
    const { effectModifier, effectValue = 0 } = effect;
    switch (effectModifier) {
        case "multiply": {
            return baseValue * effectValue;
        }
        case "add": {
            return baseValue + effectValue;
        }
        case "subtract": {
            return baseValue - effectValue;
        }
        case "divide": {
            return effectValue === 0 ? baseValue : baseValue / effectValue;
        }
        case "set": {
            return effectValue;
        }
        default: {
            logger.warn(`Unknown effect modifier: ${effectModifier}. Using effectValue directly.`);
            return effectValue;
        }
    }
};
