import { isLoggedInAuth, canMakeStateChangesAuth, adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as ShopController from "./shop.controller.js";
import * as ShopAdminController from "./shop.admin.js";
import shopSchema from "./shop.validation.js";
import { z } from "zod";
const shopInfoSchema = z.object({
    shopId: z.number().int().positive(),
});
const traderRepSchema = z.object({
    shopId: z.number().int().positive(),
});
export const shopRouter = {
    shopList: isLoggedInAuth.handler(async () => {
        const response = await ShopController.shopList();
        return handleResponse(response);
    }),
    shopInfo: isLoggedInAuth.input(shopInfoSchema).handler(async ({ input }) => {
        const response = await ShopController.shopInfo(input.shopId);
        return handleResponse(response);
    }),
    getTraderRep: isLoggedInAuth.input(traderRepSchema).handler(async ({ input, context }) => {
        const response = await ShopController.getTraderRep(context.user.id, input.shopId);
        return handleResponse(response);
    }),
    purchaseItem: canMakeStateChangesAuth.input(shopSchema.purchaseItemSchema).handler(async ({ input, context }) => {
        const response = await ShopController.purchaseItem(input.id, input.amount, context.user.id);
        return handleResponse(response);
    }),
    sellItem: canMakeStateChangesAuth.input(shopSchema.sellItemSchema).handler(async ({ input, context }) => {
        const response = await ShopController.sellItem(input.userItemId, input.amount, context.user.id);
        return handleResponse(response);
    }),
    admin: {
        createShop: adminAuth.input(shopSchema.createShopSchema).handler(async ({ input }) => {
            const response = await ShopAdminController.createShop(input);
            return handleResponse(response);
        }),
        updateShop: adminAuth.input(shopSchema.editShopSchema).handler(async ({ input }) => {
            const { id, ...updateData } = input;
            const response = await ShopAdminController.editShop(id, updateData);
            return handleResponse(response);
        }),
        deleteShop: adminAuth.input(shopSchema.deleteShopSchema).handler(async ({ input }) => {
            const response = await ShopAdminController.deleteShop(input.id);
            return handleResponse(response);
        }),
        createShopListing: adminAuth.input(shopSchema.createShopListingSchema).handler(async ({ input }) => {
            const response = await ShopAdminController.createShopListing(input);
            return handleResponse(response);
        }),
        editShopListing: adminAuth.input(shopSchema.editShopListingSchema).handler(async ({ input }) => {
            const { id, ...updateData } = input;
            const response = await ShopAdminController.editShopListing(id, updateData);
            return handleResponse(response);
        }),
        deleteShopListing: adminAuth.input(shopSchema.deleteShopListingSchema).handler(async ({ input }) => {
            const response = await ShopAdminController.deleteShopListing(input.id);
            return handleResponse(response);
        }),
    },
};
