import RoundedButton from "@/components/Buttons/RoundedButton";
import { APIROUTES } from "@/helpers/apiRoutes";
import { handlePost } from "@/helpers/axiosInstance";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGameConfig from "@/hooks/useGameConfig";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

export default function RespecTalents() {
    const { data: currentUser } = useFetchCurrentUser();
    const { data: unlockedTalents } = useQuery({ queryKey: APIROUTES.TALENTS.GETUNLOCKEDTALENTS });
    const queryClient = useQueryClient();
    const { TALENTS_LEVEL_GATE, TALENT_RESPEC_BASE_COST } = useGameConfig();

    const isDisabled = currentUser?.level < TALENTS_LEVEL_GATE || unlockedTalents?.talentList?.length === 0;

    const respecCost = currentUser?.level * TALENT_RESPEC_BASE_COST || 0;

    const respecTalents = useMutation({
        mutationFn: async () => {
            return await handlePost(APIROUTES.TALENTS.RESETTALENTS);
        },
        onSuccess: () => {
            queryClient.invalidateQueries(APIROUTES.TALENTS.GETUNLOCKEDTALENTS);
            queryClient.invalidateQueries([APIROUTES.USER.CURRENTUSERINFO]);
            toast.success("Your talents were reset!");
        },
        onError: () => {},
    });

    const handleClick = () => {
        if (currentUser?.cash < respecCost) {
            toast.error("You don't have enough cash to respec!");
            return;
        }
        window.confirm("This will reset all talents and abilities, are you sure?") && respecTalents.mutate();
    };

    return (
        <RoundedButton
            disabled={isDisabled}
            className="mx-auto mr-5 mb-4 w-auto text-sm md:mr-20 md:mb-8 dark:text-black font-display"
            text={`Respec Talents`}
            textHighlight={`(¥${respecCost})`}
            onClick={() => handleClick()}
        />
    );
}
