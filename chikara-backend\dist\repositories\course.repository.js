import { CourseRewardType } from "../data/courses.js";
import { db } from "../lib/db.js";
import { getNow } from "../utils/dateHelpers.js";
export const createCompletedCourse = async (userId, courseId) => {
    return await db.user_completed_course.create({
        data: {
            userId,
            courseId,
            completedAt: getNow(),
        },
    });
};
const resetUserActiveCourse = async (userId) => {
    return await db.user.update({
        where: { id: userId },
        data: {
            activeCourseId: null,
            courseEnds: null,
        },
    });
};
export const updateUserAfterCourseCompletion = async (userId, rewardType, stat, amount, recipeId) => {
    if (rewardType === CourseRewardType.STAT && stat && amount) {
        await resetUserActiveCourse(userId);
        const combatStats = ["strength", "defence", "intelligence", "dexterity", "endurance", "vitality"];
        if (combatStats.includes(stat)) {
            await db.user_skill.upsert({
                where: {
                    userId_skillType: {
                        userId,
                        skillType: stat,
                    },
                },
                update: {
                    level: { increment: amount },
                    updatedAt: new Date(),
                },
                create: {
                    userId,
                    skillType: stat,
                    level: 1 + amount,
                    experience: 0,
                    talentPoints: 0,
                },
            });
        }
        else {
            await db.user.update({
                where: { id: userId },
                data: {
                    [stat]: { increment: amount },
                },
            });
        }
        return await db.user.findUnique({
            where: { id: userId },
        });
    }
    else if (rewardType === CourseRewardType.TALENT_POINTS && amount) {
        await resetUserActiveCourse(userId);
        return await db.user.update({
            where: { id: userId },
            data: {
                talentPoints: { increment: amount },
            },
        });
    }
    else if (rewardType === CourseRewardType.CRAFTING_RECIPE && recipeId) {
        await resetUserActiveCourse(userId);
        await db.user_recipe.upsert({
            where: {
                craftingRecipeId_userId: {
                    craftingRecipeId: recipeId,
                    userId: userId,
                },
            },
            update: {},
            create: {
                craftingRecipeId: recipeId,
                userId: userId,
            },
        });
        return await db.user.findUnique({
            where: { id: userId },
        });
    }
    else {
        return await resetUserActiveCourse(userId);
    }
};
export const findCompletedCourses = async (userId) => {
    return await db.user_completed_course.findMany({
        where: {
            userId,
        },
        select: {
            courseId: true,
        },
    });
};
export const findCompletedCourseByUserAndCourse = async (userId, courseId) => {
    return await db.user_completed_course.findFirst({
        where: {
            userId,
            courseId,
        },
    });
};
export const updateUserOnCourseStart = async (userId, courseId, courseCost, courseEndTime) => {
    return await db.user.update({
        where: { id: userId },
        data: {
            cash: { decrement: courseCost },
            activeCourseId: courseId,
            courseEnds: BigInt(courseEndTime),
        },
    });
};
