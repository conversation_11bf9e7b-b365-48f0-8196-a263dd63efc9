import * as bountyController from "./bounty.controller.js";
import { deleteBountySchema, placeBountySchema } from "./bounty.validation.js";
import * as authHelper from "../../middleware/authMiddleware.js";
import { validate } from "../../middleware/validate.js";
import { routeHandler } from "../../utils/routeHandler.js";
import express, { Router, Request } from "express";

const router: Router = express.Router();

router.get(
    "/list",
    // TODO - Check if should be admin only
    authHelper.IsLoggedIn,
    routeHandler(async () => {
        return await bountyController.bountyList();
    })
);

router.get(
    "/active",
    authHelper.IsLoggedIn,
    routeHandler(async () => {
        return await bountyController.activeBountyList();
    })
);

router.post(
    "/place",
    authHelper.IsLoggedInAndCanMakeStateChanges,
    validate(placeBountySchema),
    routeHandler(async (req) => {
        const { amount, targetId, reason } = req.body;
        return await bountyController.placeBounty({
            userId: req.user.id,
            bountyAmount: amount,
            targetId,
            reason,
        });
    })
);

router.post(
    "/delete",
    authHelper.IsAdmin,
    validate(deleteBountySchema),
    routeHandler(async (req: Request) => {
        const { id } = req.body;
        return await bountyController.deleteBounty({ id });
    })
);

export default router;
