import * as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "../../../../features/bounty/bounty.helpers.js";
import { logAction } from "../../../../lib/actionLogger.js";
import { db } from "../../../../lib/db.js";
import { getSevenDaysAgo } from "../../../../utils/dateHelpers.js";
import { logger } from "../../../../utils/log.js";
export async function placeRandomNPCBounty() {
    try {
        const placerId = 5;
        const sevenDaysAgo = getSevenDaysAgo();
        const targets = await db.user.findMany({
            where: {
                userType: {
                    not: "admin",
                },
                last_activity: {
                    lte: sevenDaysAgo,
                },
                level: {
                    gte: 5,
                },
                currentHealth: {
                    gte: 100,
                },
            },
            select: {
                id: true,
            },
        });
        if (targets.length === 0) {
            logger.info("No valid targets found for NPC bounty");
            return;
        }
        const targetId = targets[Math.floor(Math.random() * targets.length)].id;
        await BountyHelper.ApplyBounty(targetId, 100, `Slacking`, placerId);
        logAction({
            action: "NPC_BOUNTY_PLACED",
            userId: placerId,
            info: {
                targetId,
                amount: 100,
                reason: `Slacking`,
            },
        });
        logger.info(`Placed NPC bounty on user #${targetId}`);
    }
    catch (error) {
        logger.error("Error placing random NPC bounty: " + error);
    }
}
