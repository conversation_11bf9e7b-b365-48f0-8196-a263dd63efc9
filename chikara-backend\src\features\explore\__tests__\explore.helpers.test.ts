import type { ExploreNodeLocation, TravelMethod } from "@prisma/client";
import { describe, expect, it, beforeEach, vi } from "vitest";

// Import the function to test after mocking
import { validateTravelParameters } from "../explore.helpers.js";

describe("ExploreHelpers - validateTravelParameters", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("Valid parameters", () => {
        it("should validate walking to all locations", () => {
            const method: TravelMethod = "walk";
            const locations: ExploreNodeLocation[] = ["shibuya", "shinjuku", "bunkyo", "chiyoda", "minato"];

            for (const location of locations) {
                expect(() => validateTravelParameters(method, location)).not.toThrow();
            }
        });

        it("should validate bus travel to all locations", () => {
            const method: TravelMethod = "bus";
            const locations: ExploreNodeLocation[] = ["shibuya", "shinjuku", "bunkyo", "chiyoda", "minato"];

            for (const location of locations) {
                expect(() => validateTravelParameters(method, location)).not.toThrow();
            }
        });
    });

    describe("Invalid travel methods", () => {
        it("should reject invalid travel method", () => {
            const invalidMethod = "teleport" as TravelMethod;
            const location: ExploreNodeLocation = "shibuya";

            expect(() => validateTravelParameters(invalidMethod, location)).toThrow("Invalid travel method: teleport");
        });

        it("should reject undefined travel method", () => {
            const invalidMethod = undefined as any as TravelMethod;
            const location: ExploreNodeLocation = "shibuya";

            expect(() => validateTravelParameters(invalidMethod, location)).toThrow("Invalid travel method");
        });

        it("should reject null travel method", () => {
            const invalidMethod = null as any as TravelMethod;
            const location: ExploreNodeLocation = "shibuya";

            expect(() => validateTravelParameters(invalidMethod, location)).toThrow("Invalid travel method");
        });
    });

    describe("Invalid locations", () => {
        it("should reject invalid location for walking", () => {
            const method: TravelMethod = "walk";
            const invalidLocation = "mars" as ExploreNodeLocation;

            expect(() => validateTravelParameters(method, invalidLocation)).toThrow(
                "Location mars is not available for travel method walk"
            );
        });

        it("should reject invalid location for bus", () => {
            const method: TravelMethod = "bus";
            const invalidLocation = "moon" as ExploreNodeLocation;

            expect(() => validateTravelParameters(method, invalidLocation)).toThrow(
                "Location moon is not available for travel method bus"
            );
        });

        it("should reject undefined location", () => {
            const method: TravelMethod = "walk";
            const invalidLocation = undefined as any as ExploreNodeLocation;

            expect(() => validateTravelParameters(method, invalidLocation)).toThrow("Location");
        });

        it("should reject null location", () => {
            const method: TravelMethod = "bus";
            const invalidLocation = null as any as ExploreNodeLocation;

            expect(() => validateTravelParameters(method, invalidLocation)).toThrow("Location");
        });
    });

    describe("Edge cases", () => {
        it("should handle empty string method", () => {
            const invalidMethod = "" as TravelMethod;
            const location: ExploreNodeLocation = "shibuya";

            expect(() => validateTravelParameters(invalidMethod, location)).toThrow("Invalid travel method");
        });

        it("should handle empty string location", () => {
            const method: TravelMethod = "walk";
            const invalidLocation = "" as ExploreNodeLocation;

            expect(() => validateTravelParameters(method, invalidLocation)).toThrow("Location");
        });

        it("should handle both invalid method and location", () => {
            const invalidMethod = "fly" as TravelMethod;
            const invalidLocation = "space" as ExploreNodeLocation;

            // Should fail on the first validation (method)
            expect(() => validateTravelParameters(invalidMethod, invalidLocation)).toThrow(
                "Invalid travel method: fly"
            );
        });
    });
});
