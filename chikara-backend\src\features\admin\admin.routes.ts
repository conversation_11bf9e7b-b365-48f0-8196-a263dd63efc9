import { exec } from "node:child_process";
import { logger } from "../../utils/log.js";
import express from "express";
const router = express.Router();
import * as AdminService from "./admin.controller.js";
import { generateAdminRoutes } from "./admin.provider.js";
import authHelper from "../../middleware/authMiddleware.js";
import fetchIcons from "../../utils/fetchIcons.js";
import { parseMultipleImages } from "../../utils/images.js";
import { routeHandler } from "../../utils/routeHandler.js";
// const { apiReference } = require("@scalar/express-api-reference");
// const OpenApiSpecification = require("../../api.json");

const adminModels = ["item", "user", "shop"];

generateAdminRoutes(router, adminModels, authHelper.IsAdmin);
router.get(
    "/user-info",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        return await AdminService.GetFullUserInfo(Number(req.query.id));
    })
);

router.get(
    "/gameConfig",
    authHelper.IsAdmin,
    routeHandler(async () => {
        return await AdminService.GetFullGameConfig();
    })
);
router.get(
    "/equippedvalues",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        return await AdminService.GetUserEquippedValues(Number(req.query.id));
    })
);
router.get(
    "/active-users-stats",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const startDate = new Date(req.query.startDate as string);
        const endDate = new Date(req.query.endDate as string);
        return await AdminService.ActiveUsersStats(startDate, endDate);
    })
);
router.get(
    "/registration-stats",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const startDate = new Date(req.query.startDate as string);
        const endDate = new Date(req.query.endDate as string);
        return await AdminService.RegistrationStats(startDate, endDate);
    })
);
router.get(
    "/total-users",
    authHelper.IsAdmin,
    routeHandler(async () => {
        return await AdminService.TotalUsers();
    })
);
router.get(
    "/userlist",
    authHelper.IsAdmin,
    routeHandler(async () => {
        return await AdminService.GetFullUserList();
    })
);
router.get(
    "/current-active-users",
    authHelper.IsAdmin,
    routeHandler(async () => {
        return await AdminService.CurrentActiveUsers();
    })
);

router.get(
    "/circulating-yen-weekly",
    authHelper.IsAdmin,
    routeHandler(async () => {
        return await AdminService.GetCirculatingYenThisWeek();
    })
);
router.get(
    "/gang-info",
    authHelper.IsLoggedIn,
    routeHandler(async (req) => {
        return await AdminService.GetFullGangInfo(Number(req.query.id));
    })
);
router.get(
    "/get-quit-players",
    authHelper.IsAdmin,
    routeHandler(async () => {
        return await AdminService.FindQuitPlayers();
    })
);
router.get(
    "/latestLogs",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        return await AdminService.getLatestLogs(req.user.id);
    })
);
router.get(
    "/getBattles",
    authHelper.IsAdmin,
    routeHandler(async () => {
        return await AdminService.getBattlesList();
    })
);

router.post(
    "/chatban",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = req.body.userId;
        const timeToBanMS = Number.parseInt(req.body.timeMS);
        return await AdminService.ChatBanUser(userId, timeToBanMS, req.user.id);
    })
);

router.post(
    "/remove-chat-messages",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = req.body.userId;
        return await AdminService.RemoveUserChatMessages(userId, req.user.id);
    })
);

router.post(
    "/hide-single-message",
    authHelper.IsModerator,
    routeHandler(async (req) => {
        const messageId = req.body.messageId;
        return await AdminService.HideSingleChatMessage(messageId, req.user.id);
    })
);

router.post(
    "/unhide-single-message",
    authHelper.IsModerator,
    routeHandler(async (req) => {
        const messageId = req.body.messageId;
        return await AdminService.UnhideSingleChatMessage(messageId, req.user.id);
    })
);

router.post(
    "/delete-single-message",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const messageId = req.body.messageId;
        return await AdminService.DeleteSingleChatMessage(messageId, req.user.id);
    })
);

router.post(
    "/ban",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = req.body.userId;
        const timeToBanMS = Number.parseInt(req.body.timeMS);
        return await AdminService.BanUser(userId, timeToBanMS, req.user.id);
    })
);

router.post(
    "/giveitem",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const itemName = req.body.itemName;
        const amount = req.body.amount || 1;
        const userId = req.body.id || req.user.id;
        const message = req.body.message || null;
        return await AdminService.GiveItem(itemName, amount, userId, req.user.id, message);
    })
);

router.post(
    "/removeitem",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const itemName = req.body.itemName;
        const amount = req.body.amount || 1;
        const userId = req.body.id;
        return await AdminService.RemoveItem(itemName, amount, userId, req.user.id, req.body.itemId);
    })
);

router.post(
    "/revive",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = req.body.userId || req.user.id;
        return await AdminService.ReviveUser(userId, req.user.id);
    })
);

router.post(
    "/jail",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = Number.parseInt(req.body.userId);
        const timeToJailMS = Number.parseInt(req.body.timeMS);
        const jailReason = req.body.jailReason;
        return await AdminService.JailUser(userId, timeToJailMS, jailReason, req.user.id);
    })
);

router.post(
    "/bail",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = req.body.userId;
        return await AdminService.BailUser(userId, req.user.id);
    })
);

router.post(
    "/roguelike/reset",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = req.body.id;
        return await AdminService.ResetUserRoguelikeData(userId, req.user.id);
    })
);

router.post(
    "/roguelike/update",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = req.body.id;
        const roguelikeData = req.body.mapdata;
        const roguelikeLevel = Number.parseInt(req.body.level);
        return await AdminService.UpdateUserRoguelikeData(userId, roguelikeData, roguelikeLevel, req.user.id);
    })
);

router.post(
    "/roguelike/updateBuffs",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = req.body.userId;
        const strBuff = Number.parseFloat(req.body.strBuff);
        const defBuff = Number.parseFloat(req.body.defBuff);
        return await AdminService.UpdateUserRoguelikeBuffs(userId, strBuff, defBuff, req.user.id);
    })
);

router.post(
    "/update-values",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = req.body.userId;
        const type = req.body.type;
        const value = Number.parseInt(req.body.value);
        return await AdminService.UpdateUserValues(userId, type, value, req.user.id);
    })
);

router.post(
    "/update-money",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = req.body.userId;
        const method = req.body.method;
        const type = req.body.type;
        const value = Number.parseInt(req.body.value);
        return await AdminService.UpdateUserMoney(userId, method, type, value, req.user.id);
    })
);

router.post(
    "/update-stats",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = req.body.userId;
        const targetStat = req.body.targetStat;
        const value = Number.parseInt(req.body.value);
        const method = req.body.method;
        return await AdminService.UpdateUserStats(userId, targetStat, value, method, req.user.id);
    })
);

router.post(
    "/update-admin-notes",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = req.body.userId;
        const notes = req.body.notes;
        return await AdminService.UpdateAdminNotes(userId, notes, req.user.id);
    })
);

const profileUpdateFields = [
    { name: "avatar", maxCount: 1 },
    { name: "banner", maxCount: 1 },
];

router.post(
    "/update-account-details",
    authHelper.IsAdmin,
    parseMultipleImages(profileUpdateFields), // Parse multipart form data using multer middleware
    routeHandler(async (req) => {
        const files = req.files as {
            avatar?: Express.Multer.File[];
            banner?: Express.Multer.File[];
        };

        const userId = req.body.userId;
        const updates = {
            password: req.body.password,
            username: req.body.username,
            email: req.body.email,
        };
        return await AdminService.UpdateUserAccountDetails(userId, updates, files, req.user.id);
    })
);

router.post(
    "/update-avatar",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const userId = req.body.id;
        const newAvatar = req.body.avatar;
        return await AdminService.UpdateUserAvatar(userId, newAvatar, req.user.id);
    })
);

router.post(
    "/profile-details-ban",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        return await AdminService.ProfileDetailsChangeBan(req.body.id, Number.parseInt(req.body.timeMS), req.user.id);
    })
);

router.post(
    "/endLottery",
    authHelper.IsAdmin,
    routeHandler(async () => {
        return await AdminService.EndLottery();
    })
);

router.post(
    "/patchNotesNotify",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        return await AdminService.SendPatchNotesGlobalNotification(req.body.id);
    })
);

router.post(
    "/create-test-user",
    authHelper.IsAdmin,
    routeHandler(async () => {
        return await AdminService.CreateTestUser();
    })
);

router.post(
    "/create-auction-listing",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        return await AdminService.CreateAuctionListing(req.body.itemId, req.user.id);
    })
);

router.post(
    "/bulk-create-items",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        return await AdminService.bulkCreateItems(req.body.items);
    })
);

router.post(
    "/test-push-notification",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        return await AdminService.SendTestPushNotification(req.body.message, req.body.userId || req.user.id);
    })
);

router.post(
    "/reset-all-maps",
    authHelper.IsAdmin,
    routeHandler(async () => {
        return await AdminService.resetAllUserRoguelikeMaps();
    })
);

router.post(
    "/manual-gang-payout",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        return await AdminService.ManualGangPayout(req.body.gangId, req.body.amount, req.user.id);
    })
);

router.post(
    "/send-announcement-message",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        return await AdminService.CreateAnnouncementMessage(req.body.message);
    })
);

// Add the toggle maintenance mode route
router.post(
    "/toggle-maintenance",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const { enabled } = req.body;
        return await AdminService.ToggleMaintenanceMode(enabled, req.user.id);
    })
);

router.get(
    "/fetch-icons",
    authHelper.IsAdmin,
    routeHandler(async (req) => {
        const { path } = req.query;
        const icons = await fetchIcons.getAllIcons(path as string);
        return { data: icons };
    })
);

// router.use(
//     "/reference",
//     authHelper.IsAdmin,
//     apiReference({
//         theme: "kepler",
//         spec: {
//             content: OpenApiSpecification,
//         },
//     })
// );

export default router;
