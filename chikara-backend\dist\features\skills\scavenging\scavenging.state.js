import { getRedisItem, redisClient } from "../../../config/redisClient.js";
import { LogErrorStack, logger } from "../../../utils/log.js";
const REDIS_KEYS = {
    SESSION: "scavenging:session:",
    USER_SESSION: "scavenging:user:",
};
const SESSION_TTL = 60 * 60 * 2;
export const storeSession = async (sessionId, gameSession) => {
    try {
        const sessionKey = `${REDIS_KEYS.SESSION}${sessionId}`;
        const userSessionKey = `${REDIS_KEYS.USER_SESSION}${gameSession.userId}`;
        await redisClient.setEx(sessionKey, SESSION_TTL, JSON.stringify(gameSession));
        await redisClient.setEx(userSessionKey, SESSION_TTL, sessionId);
        logger.debug(`Stored scavenging session ${sessionId} for user ${gameSession.userId}`);
        return true;
    }
    catch (error) {
        LogErrorStack({ message: `Failed to store scavenging session ${sessionId}`, error });
        return false;
    }
};
export const getSession = async (sessionId) => {
    try {
        const sessionKey = `${REDIS_KEYS.SESSION}${sessionId}`;
        const sessionData = await getRedisItem(sessionKey);
        if (!sessionData) {
            return null;
        }
        const gameSession = sessionData;
        gameSession.createdAt = new Date(gameSession.createdAt);
        gameSession.lastActivity = new Date(gameSession.lastActivity);
        return gameSession;
    }
    catch (error) {
        LogErrorStack({ message: `Failed to retrieve scavenging session ${sessionId}`, error });
        return null;
    }
};
export const updateSession = async (sessionId, gameSession) => {
    try {
        const sessionKey = `${REDIS_KEYS.SESSION}${sessionId}`;
        gameSession.lastActivity = new Date();
        await redisClient.setEx(sessionKey, SESSION_TTL, JSON.stringify(gameSession));
        return true;
    }
    catch (error) {
        LogErrorStack({ message: `Failed to update scavenging session ${sessionId}`, error });
        return false;
    }
};
export const deleteSession = async (sessionId, userId) => {
    try {
        const sessionKey = `${REDIS_KEYS.SESSION}${sessionId}`;
        const keys = [sessionKey];
        if (userId) {
            const userSessionKey = `${REDIS_KEYS.USER_SESSION}${userId}`;
            keys.push(userSessionKey);
        }
        await redisClient.del(keys);
        logger.debug(`Deleted scavenging session ${sessionId}${userId ? ` for user ${userId}` : ""}`);
        return true;
    }
    catch (error) {
        LogErrorStack({ message: `Failed to delete scavenging session ${sessionId}`, error });
        return false;
    }
};
export const getUserActiveSession = async (userId) => {
    try {
        const userSessionKey = `${REDIS_KEYS.USER_SESSION}${userId}`;
        const sessionId = await getRedisItem(userSessionKey);
        if (!sessionId || typeof sessionId !== "string") {
            return null;
        }
        return await getSession(sessionId);
    }
    catch (error) {
        LogErrorStack({ message: `Failed to get active session for user ${userId}`, error });
        return null;
    }
};
export const hasActiveSession = async (userId) => {
    try {
        const userSessionKey = `${REDIS_KEYS.USER_SESSION}${userId}`;
        const sessionId = await getRedisItem(userSessionKey);
        if (!sessionId) {
            return false;
        }
        const session = await getSession(sessionId);
        return session !== null && !session.gameOver;
    }
    catch (error) {
        LogErrorStack({ message: `Failed to check active session for user ${userId}`, error });
        return false;
    }
};
export const cleanupExpiredSessions = async () => {
    try {
        const sessionKeys = await redisClient.keys(`${REDIS_KEYS.SESSION}*`);
        let cleanedCount = 0;
        for (const sessionKey of sessionKeys) {
            const sessionData = await getRedisItem(sessionKey);
            if (!sessionData) {
                continue;
            }
            const gameSession = sessionData;
            const lastActivity = new Date(gameSession.lastActivity);
            const now = new Date();
            const hoursSinceActivity = (now.getTime() - lastActivity.getTime()) / (1000 * 60 * 60);
            if (hoursSinceActivity > 0.5) {
                const sessionId = sessionKey.replace(REDIS_KEYS.SESSION, "");
                await deleteSession(sessionId, gameSession.userId);
                cleanedCount++;
            }
        }
        logger.debug(`Cleaned up ${cleanedCount} expired scavenging sessions`);
        return cleanedCount;
    }
    catch (error) {
        LogErrorStack({ message: `Failed to cleanup expired scavenging sessions`, error });
        return 0;
    }
};
