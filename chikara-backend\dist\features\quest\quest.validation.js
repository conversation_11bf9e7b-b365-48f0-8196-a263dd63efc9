import { QuestObjectiveTypes, LocationTypes } from "@prisma/client";
import z from "zod";
export const startQuestSchema = z.object({
    id: z.number().int(),
});
export const completeQuestSchema = z.object({
    id: z.number().int(),
});
export const createQuestSchema = z.object({
    name: z.string().min(1).max(255),
    description: z.string().max(255).optional(),
    questInfo: z.string().max(255).optional(),
    levelReq: z.number().int().min(0).default(0),
    disabled: z.boolean().optional().default(false),
    questChainName: z.string().max(255).optional(),
    xpReward: z.number().int().min(0).optional().default(1),
    cashReward: z.number().int().min(0).default(0),
    repReward: z.number().min(0).default(0),
    talentPointReward: z.number().int().min(0).default(0),
    shopId: z.number().int().optional(),
    requiredQuestId: z.number().int().optional(),
    isStoryQuest: z.boolean().default(false),
    chapterId: z.number().int().optional(),
    orderInChapter: z.number().int().optional(),
});
export const updateQuestSchema = createQuestSchema.partial().extend({
    id: z.number().int().positive(),
});
export const handInItemSchema = z.object({
    objectiveId: z.number().int().positive(),
    itemId: z.number().int().positive(),
});
export const createObjectiveSchema = z.object({
    questId: z.number().int().positive(),
    objectiveType: z.nativeEnum(QuestObjectiveTypes),
    description: z.string().min(1),
    order: z.number().int().min(0).default(0),
    quantity: z.number().int().min(1).default(1),
    targetId: z.number().int().optional(),
    location: z.nativeEnum(LocationTypes).optional(),
    targetAction: z.string().optional(),
});
export const updateObjectiveSchema = createObjectiveSchema.partial().extend({
    id: z.number().int().positive(),
});
export const reorderQuestsSchema = z.object({
    chapterId: z.number().int().positive(),
    questIds: z.array(z.number().int().positive()),
});
const questSchema = {
    startQuestSchema,
    completeQuestSchema,
    createQuestSchema,
    updateQuestSchema,
    handInItemSchema,
    createObjectiveSchema,
    updateObjectiveSchema,
    reorderQuestsSchema,
};
export default questSchema;
