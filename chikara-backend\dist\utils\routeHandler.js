import { ORPCError } from "@orpc/server";
import { stringify } from "./jsonHelper.js";
import { LogErrorStack } from "./log.js";
export const routeHandler = (handler) => {
    return async (req, res) => {
        try {
            const result = await handler(req, res);
            const statusCode = result.statusCode || (result.error ? 400 : 200);
            const responseObject = {
                success: !result.error,
                data: result.data ?? null,
                error: result.error ?? null,
            };
            res.status(statusCode).send(stringify(responseObject));
        }
        catch (error) {
            LogErrorStack({ error, req });
            const errorResponse = {
                success: false,
                data: null,
                error: "Unexpected server error",
            };
            res.status(500).send(stringify(errorResponse));
        }
    };
};
const getErrorType = (error) => {
    switch (error) {
        case 400: {
            return "BAD_REQUEST";
        }
        case 401: {
            return "UNAUTHORIZED";
        }
        case 404: {
            return "NOT_FOUND";
        }
        case 500: {
            return "INTERNAL_SERVER_ERROR";
        }
        default: {
            return "INTERNAL_SERVER_ERROR";
        }
    }
};
export function handleResponse(result) {
    if (result.error) {
        const errorType = getErrorType(result.statusCode);
        throw new ORPCError(errorType, { message: result.error });
    }
    if (result && !result.data) {
        return result;
    }
    return result.data;
}
export default routeHandler;
