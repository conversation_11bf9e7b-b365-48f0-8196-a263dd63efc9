import { LogErrorStack } from "../../../utils/log.js";
import { generateRandomGrid, generateSessionId, createObfuscatedGrid, createObfuscatedMultiCellResources, createEnhancedResources, } from "./scavenging.helpers.js";
import { DEFAULT_ENERGY, MAX_DIFFICULTY_TIER, MIN_DIFFICULTY_TIER, DIFFICULTY_TIERS, POTENTIAL_RESOURCES, } from "./scavenging.constants.js";
import * as ScavengingState from "./scavenging.state.js";
import * as InventoryService from "../../../core/inventory.service.js";
export const generateGrid = async (userId, difficultyTier) => {
    try {
        const tier = difficultyTier || 1;
        if (tier < MIN_DIFFICULTY_TIER || tier > MAX_DIFFICULTY_TIER) {
            return {
                error: `Difficulty tier must be between ${MIN_DIFFICULTY_TIER} and ${MAX_DIFFICULTY_TIER}`,
                statusCode: 400,
            };
        }
        const existingSession = await ScavengingState.getUserActiveSession(userId);
        if (existingSession && !existingSession.gameOver) {
            return { error: "You already have an active scavenging session", statusCode: 400 };
        }
        const difficultyConfig = DIFFICULTY_TIERS[tier];
        const { gridSize, minResources, maxResources } = difficultyConfig;
        const { grid, multiCellResources } = generateRandomGrid(gridSize, minResources, maxResources, tier);
        const initialResources = {};
        for (const potentialResource of POTENTIAL_RESOURCES) {
            initialResources[potentialResource.itemId] = 0;
        }
        const sessionId = generateSessionId();
        const gameSession = {
            sessionId,
            userId,
            fullGrid: grid,
            multiCellResources,
            energy: DEFAULT_ENERGY,
            gameOver: false,
            resources: initialResources,
            gridSize: gridSize,
            difficultyTier: tier,
            createdAt: new Date(),
            lastActivity: new Date(),
        };
        const stored = await ScavengingState.storeSession(sessionId, gameSession);
        if (!stored) {
            return { error: "Failed to create scavenging session", statusCode: 500 };
        }
        const obfuscatedGrid = createObfuscatedGrid(grid, gridSize);
        const obfuscatedMultiCellResources = createObfuscatedMultiCellResources(multiCellResources, grid);
        const enhancedResources = await createEnhancedResources(gameSession.resources);
        return {
            data: {
                success: true,
                sessionId,
                grid: obfuscatedGrid,
                multiCellResources: obfuscatedMultiCellResources,
                energy: gameSession.energy,
                gameOver: gameSession.gameOver,
                resources: enhancedResources,
                gridSize: gridSize,
                difficultyTier: tier,
                timestamp: new Date().toISOString(),
            },
        };
    }
    catch (error) {
        LogErrorStack({ message: "Error starting scavenging", error });
        return { error: "Failed to start scavenging", statusCode: 500 };
    }
};
export const revealCell = async (userId, sessionId, row, col) => {
    try {
        if (!sessionId || row === undefined || col === undefined) {
            return { error: "Missing required parameters", statusCode: 400 };
        }
        const gameSession = await ScavengingState.getSession(sessionId);
        if (!gameSession) {
            return { error: "Scavenging session not found or expired", statusCode: 404 };
        }
        if (gameSession.userId !== userId) {
            return { error: "Session does not belong to user", statusCode: 403 };
        }
        if (gameSession.gameOver || gameSession.energy <= 0) {
            return { error: "Scavenging session is over", statusCode: 400 };
        }
        if (row < 0 || row >= gameSession.gridSize || col < 0 || col >= gameSession.gridSize) {
            return { error: "Invalid cell coordinates", statusCode: 400 };
        }
        const cell = gameSession.fullGrid[row][col];
        if (cell.revealed) {
            return { error: "Cell is already revealed", statusCode: 400 };
        }
        cell.health -= 1;
        if (cell.health <= 0) {
            cell.revealed = true;
        }
        gameSession.energy -= 1;
        gameSession.gameOver = gameSession.energy <= 0;
        if (cell.revealed) {
            for (const resource of gameSession.multiCellResources) {
                const revealedCells = resource.cells.filter(({ row, col }) => gameSession.fullGrid[row][col].revealed).length;
                const wasFullyRevealed = resource.fullyRevealed;
                resource.revealedCells = revealedCells;
                resource.fullyRevealed = revealedCells === resource.cells.length;
                if (resource.fullyRevealed &&
                    !wasFullyRevealed &&
                    resource.type !== "empty" &&
                    resource.type !== "hidden" &&
                    typeof resource.type === "number") {
                    if (!(resource.type in gameSession.resources)) {
                        gameSession.resources[resource.type] = 0;
                    }
                    gameSession.resources[resource.type] += 1;
                }
            }
        }
        const updated = await ScavengingState.updateSession(sessionId, gameSession);
        if (!updated) {
            return { error: "Failed to update scavenging session", statusCode: 500 };
        }
        const obfuscatedGrid = createObfuscatedGrid(gameSession.fullGrid, gameSession.gridSize);
        const obfuscatedMultiCellResources = createObfuscatedMultiCellResources(gameSession.multiCellResources, gameSession.fullGrid);
        const enhancedResources = await createEnhancedResources(gameSession.resources);
        const revealedCellInfo = {
            row,
            col,
            resourceType: cell.revealed ? cell.resourceType : "hidden",
            resourceId: cell.revealed ? cell.resourceId : null,
            isPartOfMultiCell: cell.revealed ? cell.isPartOfMultiCell : false,
        };
        return {
            data: {
                success: true,
                grid: obfuscatedGrid,
                multiCellResources: obfuscatedMultiCellResources,
                energy: gameSession.energy,
                gameOver: gameSession.gameOver,
                resources: enhancedResources,
                revealedCell: revealedCellInfo,
                cellDamaged: !cell.revealed,
                cellHealth: cell.health,
                cellMaxHealth: cell.maxHealth,
            },
        };
    }
    catch (error) {
        LogErrorStack({ message: "Error revealing cell in scavenging", error });
        return { error: "Failed to reveal cell", statusCode: 500 };
    }
};
export const displayDevGrid = async (sessionId) => {
    try {
        if (!sessionId) {
            return { error: "Missing sessionId", statusCode: 400 };
        }
        const gameSession = await ScavengingState.getSession(sessionId);
        if (!gameSession) {
            return { error: "Session not found or expired", statusCode: 404 };
        }
        const updated = await ScavengingState.updateSession(sessionId, gameSession);
        if (!updated) {
            return { error: "Failed to update session activity", statusCode: 500 };
        }
        const devGrid = gameSession.fullGrid.map((row) => row.map((cell) => ({
            ...cell,
            devModeVisible: true,
        })));
        return {
            data: {
                success: true,
                sessionId,
                fullGrid: devGrid,
                multiCellResources: gameSession.multiCellResources,
                energy: gameSession.energy,
                gameOver: gameSession.gameOver,
                resources: gameSession.resources,
                gridSize: gameSession.gridSize,
                difficultyTier: gameSession.difficultyTier,
                message: "Full grid data for dev mode - all resources revealed",
            },
        };
    }
    catch (error) {
        LogErrorStack({ message: "Error displaying dev grid for scavenging", error });
        return { error: "Failed to display dev grid", statusCode: 500 };
    }
};
export const getActiveSession = async (userId) => {
    try {
        const gameSession = await ScavengingState.getUserActiveSession(userId);
        if (!gameSession) {
            return { error: "No active scavenging session found", statusCode: 404 };
        }
        const obfuscatedGrid = createObfuscatedGrid(gameSession.fullGrid, gameSession.gridSize);
        const obfuscatedMultiCellResources = createObfuscatedMultiCellResources(gameSession.multiCellResources, gameSession.fullGrid);
        const enhancedResources = await createEnhancedResources(gameSession.resources);
        return {
            data: {
                success: true,
                sessionId: gameSession.sessionId,
                grid: obfuscatedGrid,
                multiCellResources: obfuscatedMultiCellResources,
                energy: gameSession.energy,
                gameOver: gameSession.gameOver,
                resources: enhancedResources,
                gridSize: gameSession.gridSize,
                difficultyTier: gameSession.difficultyTier,
                timestamp: new Date().toISOString(),
            },
        };
    }
    catch (error) {
        LogErrorStack({ message: "Error getting active scavenging session", error });
        return { error: "Failed to get active session", statusCode: 500 };
    }
};
export const endSession = async (userId, sessionId) => {
    try {
        if (!sessionId) {
            return { error: "Missing sessionId", statusCode: 400 };
        }
        const gameSession = await ScavengingState.getSession(sessionId);
        if (!gameSession) {
            return { error: "Session not found or expired", statusCode: 404 };
        }
        if (gameSession.userId !== userId) {
            return { error: "Session does not belong to user", statusCode: 403 };
        }
        const itemsGiven = [];
        for (const [itemIdStr, count] of Object.entries(gameSession.resources)) {
            if (count > 0) {
                const itemId = Number.parseInt(itemIdStr, 10);
                await InventoryService.AddItemToUser({
                    userId,
                    itemId,
                    amount: count,
                    isTradeable: true,
                });
                itemsGiven.push({ itemId, count });
            }
        }
        const deleted = await ScavengingState.deleteSession(sessionId, userId);
        if (!deleted) {
            return { error: "Failed to end session", statusCode: 500 };
        }
        const enhancedResources = await createEnhancedResources(gameSession.resources);
        return {
            data: {
                success: true,
                message: "Scavenging session ended successfully",
                finalResources: enhancedResources,
                itemsGiven,
            },
        };
    }
    catch (error) {
        LogErrorStack({ message: "Error ending scavenging session", error });
        return { error: "Failed to end session", statusCode: 500 };
    }
};
export const resetGrid = async (userId, sessionId) => {
    try {
        if (!sessionId) {
            return { error: "Missing sessionId", statusCode: 400 };
        }
        const gameSession = await ScavengingState.getSession(sessionId);
        if (!gameSession) {
            return { error: "Session not found or expired", statusCode: 404 };
        }
        if (gameSession.userId !== userId) {
            return { error: "Session does not belong to user", statusCode: 403 };
        }
        for (const row of gameSession.fullGrid) {
            for (const cell of row) {
                cell.revealed = false;
                cell.health = cell.maxHealth;
            }
        }
        for (const resource of gameSession.multiCellResources) {
            resource.fullyRevealed = false;
            resource.revealedCells = 0;
        }
        gameSession.energy = DEFAULT_ENERGY;
        gameSession.gameOver = false;
        gameSession.resources = {};
        for (const potentialResource of POTENTIAL_RESOURCES) {
            gameSession.resources[potentialResource.itemId] = 0;
        }
        gameSession.lastActivity = new Date();
        const updated = await ScavengingState.updateSession(sessionId, gameSession);
        if (!updated) {
            return { error: "Failed to reset scavenging session", statusCode: 500 };
        }
        const obfuscatedGrid = createObfuscatedGrid(gameSession.fullGrid, gameSession.gridSize);
        const obfuscatedMultiCellResources = createObfuscatedMultiCellResources(gameSession.multiCellResources, gameSession.fullGrid);
        const enhancedResources = await createEnhancedResources(gameSession.resources);
        return {
            data: {
                success: true,
                message: "Scavenging session reset successfully",
                sessionId,
                grid: obfuscatedGrid,
                multiCellResources: obfuscatedMultiCellResources,
                energy: gameSession.energy,
                gameOver: gameSession.gameOver,
                resources: enhancedResources,
                gridSize: gameSession.gridSize,
                difficultyTier: gameSession.difficultyTier,
                timestamp: new Date().toISOString(),
            },
        };
    }
    catch (error) {
        LogErrorStack({ message: "Error resetting scavenging session", error });
        return { error: "Failed to reset session", statusCode: 500 };
    }
};
