import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as MissionController from "./mission.controller.js";
import missionSchema from "./mission.validation.js";
export const missionRouter = {
    getList: isLoggedInAuth.handler(async () => {
        const response = await MissionController.missionList();
        return handleResponse(response);
    }),
    getCurrent: isLoggedInAuth.handler(async ({ context }) => {
        const response = await MissionController.currentMission(context.user.currentMission || null);
        return handleResponse(response);
    }),
    start: canMakeStateChangesAuth.input(missionSchema.start).handler(async ({ input, context }) => {
        const response = await MissionController.startMission(context.user.id, input.id);
        return handleResponse(response);
    }),
    cancel: isLoggedInAuth.handler(async ({ context }) => {
        const response = await MissionController.cancelMission(context.user.id);
        return handleResponse(response);
    }),
};
