import * as CraftingRepository from "../../repositories/crafting.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import { db } from "../../lib/db.js";
import { LogErrorStack } from "../../utils/log.js";
export const createRecipe = async (recipeData) => {
    try {
        const result = await db.$transaction(async (tx) => {
            const recipe = await CraftingRepository.createRecipeWithTransaction({
                cost: recipeData.cost,
                craftTime: recipeData.craftTime,
                isUnlockable: recipeData.isUnlockable || false,
                requiredSkillType: recipeData.requiredSkillType || null,
                requiredSkillLevel: recipeData.requiredSkillLevel || 0,
            }, tx);
            if (recipeData.inputItems && Array.isArray(recipeData.inputItems)) {
                for (const inputItem of recipeData.inputItems) {
                    const item = await ItemRepository.findItemById(inputItem.id);
                    if (!item) {
                        throw new Error(`Input item with ID ${inputItem.id} not found`);
                    }
                    await CraftingRepository.addItemToRecipe(recipe, item.id, {
                        count: inputItem.amount ?? 1,
                        itemType: "input",
                        crafting_recipe: { connect: { id: recipe.id } },
                        item: { connect: { id: item.id } },
                    }, tx);
                }
            }
            if (recipeData.outputItems && Array.isArray(recipeData.outputItems)) {
                for (const outputItem of recipeData.outputItems) {
                    const item = await ItemRepository.findItemById(outputItem.id);
                    if (!item) {
                        throw new Error(`Output item with ID ${outputItem.id} not found`);
                    }
                    await CraftingRepository.addItemToRecipe(recipe, item.id, {
                        count: outputItem.amount || 1,
                        itemType: "output",
                        crafting_recipe: { connect: { id: recipe.id } },
                        item: { connect: { id: item.id } },
                    }, tx);
                }
            }
            return recipe;
        });
        return { data: result };
    }
    catch (error) {
        LogErrorStack({ message: "Error when adding items to crafting recipe:", error });
        return { error: "Invalid items", statusCode: 400 };
    }
};
export const editRecipe = async (recipeData) => {
    try {
        if (!recipeData.id) {
            return { error: "Recipe ID is required", statusCode: 400 };
        }
        const existingRecipe = await CraftingRepository.findRecipeById(recipeData.id);
        if (!existingRecipe) {
            return { error: "Recipe not found", statusCode: 404 };
        }
        const result = await db.$transaction(async (tx) => {
            await CraftingRepository.updateRecipe(existingRecipe, {
                cost: recipeData.cost ?? existingRecipe.cost,
                craftTime: recipeData.craftTime ?? existingRecipe.craftTime,
                isUnlockable: recipeData.isUnlockable ?? existingRecipe.isUnlockable,
                requiredSkillType: recipeData.requiredSkillType ?? existingRecipe.requiredSkillType,
                requiredSkillLevel: recipeData.requiredSkillLevel ?? existingRecipe.requiredSkillLevel,
            }, tx);
            if (recipeData.inputItems || recipeData.outputItems) {
                await CraftingRepository.deleteRecipeItems(existingRecipe.id, tx);
                if (recipeData.inputItems && Array.isArray(recipeData.inputItems)) {
                    for (const inputItem of recipeData.inputItems) {
                        const item = await ItemRepository.findItemById(inputItem.id);
                        if (!item) {
                            throw new Error(`Input item with ID ${inputItem.id} not found`);
                        }
                        await CraftingRepository.addItemToRecipe(existingRecipe, item.id, {
                            count: inputItem.amount || 1,
                            itemType: "input",
                            crafting_recipe: { connect: { id: existingRecipe.id } },
                            item: { connect: { id: item.id } },
                        }, tx);
                    }
                }
                if (recipeData.outputItems && Array.isArray(recipeData.outputItems)) {
                    for (const outputItem of recipeData.outputItems) {
                        const item = await ItemRepository.findItemById(outputItem.id);
                        if (!item) {
                            throw new Error(`Output item with ID ${outputItem.id} not found`);
                        }
                        await CraftingRepository.addItemToRecipe(existingRecipe, item.id, {
                            count: outputItem.amount || 1,
                            itemType: "output",
                            crafting_recipe: { connect: { id: existingRecipe.id } },
                            item: { connect: { id: item.id } },
                        }, tx);
                    }
                }
            }
            return existingRecipe;
        });
        return { data: result };
    }
    catch (error) {
        LogErrorStack({ message: "Error when editing crafting recipe:", error });
        return { error: "Invalid items", statusCode: 400 };
    }
};
export const deleteRecipe = async (recipeId) => {
    await CraftingRepository.deleteRecipeById(recipeId);
    return { data: "Recipe deleted" };
};
