import { logger, LogErrorStack } from "../../utils/log.js";
import { dispatchEvent, registerEventHandler } from "./event-dispatcher.js";
import { GameEventType } from "./event-types.js";
import { handleNPCBattleWonEvent, handlePVPBattleWonEvent } from "./handlers/battle-event-handlers.js";
import { handleItemDroppedEvent, handleItemCraftedEvent } from "./handlers/item-event-handlers.js";
import { handleBountyPlacedEvent, handleBountyCollectedEvent } from "./handlers/bounty-event-handlers.js";
import { handleSuggestionVotedEvent, handleEncounterCompletedEvent, handleMissionCompletedEvent, handleShrineDonationMadeEvent, handleStatsTrainedEvent, handleGamblingPerformedEvent, } from "./handlers/misc-event-handlers.js";
export const initializeGameEventSystem = () => {
    logger.info("Initializing game event system...");
    registerEventHandler(GameEventType.NPC_BATTLE_WON, handleNPCBattleWonEvent);
    registerEventHandler(GameEventType.PVP_BATTLE_WON, handlePVPBattleWonEvent);
    registerEventHandler(GameEventType.ITEM_DROPPED, handleItemDroppedEvent);
    registerEventHandler(GameEventType.ITEM_CRAFTED, handleItemCraftedEvent);
    registerEventHandler(GameEventType.BOUNTY_PLACED, handleBountyPlacedEvent);
    registerEventHandler(GameEventType.BOUNTY_COLLECTED, handleBountyCollectedEvent);
    registerEventHandler(GameEventType.MISSION_COMPLETED, handleMissionCompletedEvent);
    registerEventHandler(GameEventType.SHRINE_DONATION_MADE, handleShrineDonationMadeEvent);
    registerEventHandler(GameEventType.SUGGESTION_VOTED, handleSuggestionVotedEvent);
    registerEventHandler(GameEventType.ENCOUNTER_COMPLETED, handleEncounterCompletedEvent);
    registerEventHandler(GameEventType.STATS_TRAINED, handleStatsTrainedEvent);
    registerEventHandler(GameEventType.GAMBLING_PERFORMED, handleGamblingPerformedEvent);
    logger.info("Game event system initialized successfully");
};
export const emitGameEvent = async (eventType, payload) => {
    try {
        await dispatchEvent(eventType, payload);
    }
    catch (error) {
        LogErrorStack({ message: `Failed to emit game event ${eventType}`, error });
        throw error;
    }
};
export const emitNPCBattleWon = async (payload) => {
    await emitGameEvent(GameEventType.NPC_BATTLE_WON, payload);
};
export const emitPVPBattleWon = async (payload) => {
    await emitGameEvent(GameEventType.PVP_BATTLE_WON, payload);
};
export const emitItemCrafted = async (payload) => {
    await emitGameEvent(GameEventType.ITEM_CRAFTED, payload);
};
export const emitMissionCompleted = async (payload) => {
    await emitGameEvent(GameEventType.MISSION_COMPLETED, payload);
};
export const emitStatsTrained = async (payload) => {
    await emitGameEvent(GameEventType.STATS_TRAINED, payload);
};
export const emitGamblingPerformed = async (payload) => {
    await emitGameEvent(GameEventType.GAMBLING_PERFORMED, payload);
};
export const emitStoryEpisodeCompleted = async (payload) => {
    await emitGameEvent(GameEventType.STORY_EPISODE_COMPLETED, payload);
};
export const emitItemDropped = async (payload) => {
    await emitGameEvent(GameEventType.ITEM_DROPPED, payload);
};
export const emitShrineDonationMade = async (payload) => {
    await emitGameEvent(GameEventType.SHRINE_DONATION_MADE, payload);
};
export const emitBountyPlaced = async (payload) => {
    await emitGameEvent(GameEventType.BOUNTY_PLACED, payload);
};
export const emitBountyCollected = async (payload) => {
    await emitGameEvent(GameEventType.BOUNTY_COLLECTED, payload);
};
export const emitSuggestionVoted = async (payload) => {
    await emitGameEvent(GameEventType.SUGGESTION_VOTED, payload);
};
export const emitEncounterCompleted = async (payload) => {
    await emitGameEvent(GameEventType.ENCOUNTER_COMPLETED, payload);
};
export const emitRoguelikeLevelReached = async (payload) => {
    await emitGameEvent(GameEventType.ROGUELIKE_LEVEL_REACHED, payload);
};
export const emitQuestCompleted = async (payload) => {
    await emitGameEvent(GameEventType.QUEST_COMPLETED, payload);
};
export const emitDailyQuestCompleted = async (payload) => {
    await emitGameEvent(GameEventType.DAILY_QUEST_COMPLETED, payload);
};
export const emitAbilityUsed = async (payload) => {
    await emitGameEvent(GameEventType.ABILITY_USED, payload);
};
export const emitZoneCompleted = async (payload) => {
    await emitGameEvent(GameEventType.ZONE_COMPLETED, payload);
};
