import z from "zod";
export const createAuctionListingSchema = z.object({
    itemId: z.number().int().positive(),
    quantity: z.number().int().positive(),
    buyoutPrice: z.number().int().positive(),
    auctionLength: z
        .number()
        .int()
        .refine((val) => [12, 24, 48].includes(val), {
        message: "Auction length must be 12, 24, or 48 hours",
    }),
    bankFunds: z.boolean(),
});
export const buyoutAuctionListingSchema = z.object({
    auctionItemId: z.number().int().positive(),
    quantity: z.number().int().positive(),
});
export const cancelAuctionListingSchema = z.object({
    auctionItemId: z.number().int().positive(),
});
const auctionSchema = {
    createAuctionListing: createAuctionListingSchema,
    buyoutAuctionListing: buyoutAuctionListingSchema,
    cancelAuctionListing: cancelAuctionListingSchema,
};
export default auctionSchema;
