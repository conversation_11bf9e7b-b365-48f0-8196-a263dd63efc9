import { db } from "../../../../lib/db.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";
export async function processHappinessDecay() {
    try {
        logger.debug("[SCHEDULER] Processing pet happiness decay");
        const result = await db.user_pet.updateMany({
            where: {
                happiness: {
                    gt: 0,
                },
            },
            data: {
                happiness: {
                    decrement: 5,
                },
            },
        });
        await db.user_pet.updateMany({
            where: {
                happiness: {
                    lt: 0,
                },
            },
            data: {
                happiness: 0,
            },
        });
        logger.debug(`[SCHEDULER] Processed happiness decay for ${result.count} inactive pets`);
        return { processed: result.count };
    }
    catch (error) {
        LogErrorStack({ message: "[SCHEDULER] Error processing pet happiness decay:", error });
        throw error;
    }
}
export default processHappinessDecay;
