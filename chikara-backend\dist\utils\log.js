import logger from "../config/logger.js";
import { ORPCError } from "@orpc/server";
const errorCodes = {
    400: "BAD_REQUEST",
    401: "UNAUTHORIZED",
    403: "FORBIDDEN",
    404: "NOT_FOUND",
    405: "METHOD_NOT_SUPPORTED",
    406: "NOT_ACCEPTABLE",
    408: "TIMEOUT",
    409: "CONFLICT",
    412: "PRECONDITION_FAILED",
    413: "PAYLOAD_TOO_LARGE",
    415: "UNSUPPORTED_MEDIA_TYPE",
    422: "UNPROCESSABLE_CONTENT",
    429: "TOO_MANY_REQUESTS",
    499: "CLIENT_CLOSED_REQUEST",
    500: "INTERNAL_SERVER_ERROR",
    501: "NOT_IMPLEMENTED",
    502: "BAD_GATEWAY",
    503: "SERVICE_UNAVAILABLE",
    504: "GATEWAY_TIMEOUT",
};
export const handleError = (message, statusCode, options) => {
    throw new ORPCError(errorCodes[statusCode ?? 400], { message, ...options });
};
export const handleInternalError = (internalMessage, error) => {
    const detailedError = error instanceof Error
        ? new Error(`Internal validation failed: ${internalMessage} | Original: ${error.message}`)
        : new Error(`Internal validation failed: ${internalMessage}`);
    if (error instanceof Error && error.stack) {
        detailedError.stack = error.stack;
    }
    throw new ORPCError(errorCodes[500], {
        message: "Internal Server Error",
        cause: detailedError,
    });
};
export const tryCatch = async (fn) => {
    try {
        return await fn();
    }
    catch (error) {
        LogErrorStack({ error, message: "Error caught in tryCatch wrapper" });
        return undefined;
    }
};
const cleanStackTrace = (stack) => {
    if (!stack)
        return null;
    const projectRoot = process.cwd().replaceAll("\\", "/");
    return stack
        .split("\n")
        .filter((line) => !line.includes("node_modules") && !line.includes("node:internal") && !line.includes("(node:"))
        .slice(0, 8)
        .map((line) => {
        if (line.includes(projectRoot)) {
            return line.replace(projectRoot, ".");
        }
        return line;
    })
        .join("\n");
};
export const LogErrorStack = (options) => {
    const { error, message } = options;
    const errorObj = error instanceof Error ? error : new Error(String(error));
    const isDevelopment = process.env.NODE_ENV === "development";
    const errorBody = {
        error: errorObj.message,
        stack: isDevelopment ? cleanStackTrace(errorObj.stack) : errorObj.stack,
    };
    if (message) {
        errorBody.message = message;
    }
    logger.error(errorBody);
};
export { logger };
