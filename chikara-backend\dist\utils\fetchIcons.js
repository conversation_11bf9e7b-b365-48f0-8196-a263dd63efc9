import { LogErrorStack } from "./log.js";
import { GetObjectCommand, ListObjectsV2Command, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
const R2_ENDPOINT = process.env.CLOUDFLARE_R2_ENDPOINT;
const s3Client = new S3Client({
    region: "auto",
    endpoint: R2_ENDPOINT,
    credentials: {
        accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || "",
        secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || "",
    },
});
const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET_NAME || "chikara-assets";
export async function fetchIconPaths(path) {
    try {
        const command = new ListObjectsV2Command({
            Bucket: BUCKET_NAME,
            Prefix: path,
        });
        const response = await s3Client.send(command);
        if (!response.Contents) {
            LogErrorStack({ message: "No icons found in R2 bucket", error: new Error("No icons found in R2 bucket") });
            return [];
        }
        return response.Contents.filter((obj) => obj.Key && !obj.Key.endsWith("/")).map((obj) => obj.Key);
    }
    catch (error) {
        LogErrorStack({ message: "Error fetching icon paths from R2:", error });
        return [];
    }
}
export async function getIconUrl(iconPath, expiresIn = 3600) {
    try {
        const command = new GetObjectCommand({
            Bucket: BUCKET_NAME,
            Key: iconPath,
        });
        return await getSignedUrl(s3Client, command, { expiresIn });
    }
    catch (error) {
        LogErrorStack({ message: `Error generating signed URL for icon ${iconPath}:`, error });
        const cdnUrl = process.env.IMAGE_CDN_URL;
        return cdnUrl ? `${cdnUrl}/${iconPath}` : "";
    }
}
export async function getAllIcons(dir, expiresIn = 3600) {
    const fullPath = `static/${dir}`;
    const iconPaths = await fetchIconPaths(fullPath);
    const iconPromises = iconPaths.map(async (path) => ({
        path,
        url: await getIconUrl(path, expiresIn),
    }));
    return Promise.all(iconPromises);
}
export function getIconName(path) {
    const filename = path.split("/").pop() || "";
    return filename.split(".")[0];
}
export async function getIconsWithNames(path, expiresIn = 3600) {
    const icons = await getAllIcons(path, expiresIn);
    return icons.map((icon) => ({
        name: getIconName(icon.path),
        path: icon.path,
        url: icon.url,
    }));
}
export default {
    fetchIconPaths,
    getIconUrl,
    getAllIcons,
    getIconName,
    getIconsWithNames,
};
