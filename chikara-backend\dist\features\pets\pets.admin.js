import * as PetsRepository from "../../repositories/pets.repository.js";
import { db } from "../../lib/db.js";
import { LogErrorStack } from "../../utils/log.js";
export async function getAllPets() {
    try {
        const pets = await PetsRepository.getPets();
        return pets;
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
}
export async function createPet(petData) {
    try {
        const newPet = {
            name: petData.name,
            species: petData.species,
            maxLevel: petData.maxLevel || 100,
            evolution_stages: petData.evolution_stages || [],
        };
        const createdPet = await db.pet.create({
            data: newPet,
        });
        return createdPet;
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
}
export async function updatePet(petData) {
    try {
        if (!petData.id) {
            throw new Error("Pet ID is required for updates");
        }
        const existingPet = await db.pet.findUnique({
            where: { id: petData.id },
        });
        if (!existingPet) {
            throw new Error(`Pet with ID ${petData.id} not found`);
        }
        const updatedPet = await db.pet.update({
            where: { id: petData.id },
            data: {
                name: petData.name !== undefined ? petData.name : undefined,
                species: petData.species !== undefined ? petData.species : undefined,
                maxLevel: petData.maxLevel !== undefined ? petData.maxLevel : undefined,
                evolution_stages: petData.evolution_stages !== undefined ? petData.evolution_stages : undefined,
            },
        });
        return updatedPet;
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
}
