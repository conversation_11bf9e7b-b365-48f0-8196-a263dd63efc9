import actionLogRoutes from "./features/actionlog/actionlog.routes.js";
import adminRoutes from "./features/admin/admin.routes.js";
import devRoutes from "./features/dev/dev.routes.js";
import dropChanceRoutes from "./features/dropchance/dropchance.routes.js";
import skillsRoutes from "./features/skills/skills.routes.js";
import socialRoutes from "./features/social/social.routes.js";
import userRoutes from "./features/user/user.routes.js";
import express from "express";
const router = express.Router();
router.use("/user", userRoutes);
router.use("/dev", devRoutes);
router.use("/admin", actionLogRoutes);
router.use("/admin", adminRoutes);
router.use("/social", socialRoutes);
router.use("/dropchance", dropChanceRoutes);
router.use("/skills", skillsRoutes);
export default router;
