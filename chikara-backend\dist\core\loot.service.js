import * as RogueLikeHelper from "../features/roguelike/roguelike.helpers.js";
import * as ItemRepository from "../repositories/item.repository.js";
import * as InventoryService from "./inventory.service.js";
import { IsItemEquippable } from "./equipment.service.js";
import { LogErrorStack } from "../utils/log.js";
import { ItemQuality } from "@prisma/client";
import { logPlayerAction } from "../lib/actionLogger.js";
import { emitItemDropped } from "./events/index.js";
const BOSS_DROP_QUALITIES = [ItemQuality.fine, ItemQuality.superior, ItemQuality.masterwork];
const NORMAL_DROP_QUALITIES = [ItemQuality.shoddy, ItemQuality.normal, ItemQuality.fine];
export const getItemDropQuality = (item, isBossDrop) => {
    if (!IsItemEquippable(item)) {
        return undefined;
    }
    const availableQualities = isBossDrop ? BOSS_DROP_QUALITIES : NORMAL_DROP_QUALITIES;
    const randomIndex = Math.floor(Math.random() * availableQualities.length);
    return availableQualities[randomIndex];
};
export const generateNPCItemDrop = async (currentUser, isBoss) => {
    const droppedItemId = await RogueLikeHelper.GetDropId(currentUser);
    if (droppedItemId && droppedItemId > 0) {
        const itemDetails = await ItemRepository.findItemById(droppedItemId);
        if (!itemDetails) {
            LogErrorStack({
                error: new Error(`Item not found for droppedItemId: ${droppedItemId}`),
            });
            return null;
        }
        const userItem = {
            userId: currentUser.id,
            itemId: droppedItemId,
            amount: 1,
            isTradeable: true,
        };
        const itemQuality = getItemDropQuality(itemDetails, isBoss);
        if (itemQuality) {
            userItem.quality = itemQuality;
        }
        const userItemResult = await InventoryService.AddItemToUser(userItem);
        await emitItemDropped({
            userId: currentUser.id,
            itemId: droppedItemId,
            quantity: 1,
            source: "battle",
        });
        return userItemResult.item;
    }
    return null;
};
const CRATE_ITEM_ID = 279;
export const generateCrateDrop = async (currentUser) => {
    if (currentUser.gangId) {
        const crateDropped = Math.random() <= 0.15;
        if (crateDropped) {
            const crateItem = await ItemRepository.findItemById(CRATE_ITEM_ID);
            if (crateItem) {
                await InventoryService.AddItemToUser({
                    userId: currentUser.id,
                    itemId: crateItem.id,
                    amount: 1,
                    isTradeable: true,
                });
                logPlayerAction("BATTLE_CRATE_DROPPED", {
                    itemId: crateItem.id,
                    itemName: crateItem.name,
                }, currentUser.id);
                return crateItem;
            }
        }
    }
    return null;
};
