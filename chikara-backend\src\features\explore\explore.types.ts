import type { ExploreNodeLocation, ExploreNodeStatus, ExploreNodeType, TravelMethod } from "@prisma/client";

/**
 * Grid-based position for nodes in the 5x5 explore map
 * Coordinates range from 0 to 4 (inclusive) for both x and y
 */
export interface MapNodePosition {
    x: number; // 0-4 grid coordinate
    y: number; // 0-4 grid coordinate
}

export interface ReturnMapNode {
    id: number;
    nodeType: ExploreNodeType;
    title: string;
    description: string;
    position: MapNodePosition;
    metadata?: Record<string, unknown>;
    status: ExploreNodeStatus;
    isStatic: boolean;
    expiresAt?: Date | null;
    location: ExploreNodeLocation;
    shopId: number | null;
}

/**
 * Travel status information
 */
export interface TravelStatus {
    isTravel: boolean;
    travelingTo?: ExploreNodeLocation;
    travelStartTime?: Date;
    travelEndTime?: Date;
    travelMethod?: TravelMethod;
    remainingTime?: number; // in milliseconds
}

/**
 * Travel initiation request
 */
export interface TravelRequest {
    location: ExploreNodeLocation;
    method: TravelMethod;
}

/**
 * Travel initiation response
 */
export interface TravelInitiationResponse {
    travelingTo: ExploreNodeLocation;
    newCash: number;
    cost: number;
    travelTime: number; // in minutes
    method: TravelMethod;
    travelStartTime: Date;
    travelEndTime: Date;
}

/**
 * Travel completion response
 */
export interface TravelCompletionResponse {
    newLocation: ExploreNodeLocation;
    previousLocation: ExploreNodeLocation | null;
}
