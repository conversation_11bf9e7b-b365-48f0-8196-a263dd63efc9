import { db } from "../lib/db.js";
export const findChatMessages = async (roomId, limit) => {
    return await db.chat_message.findMany({
        where: { chatRoomId: roomId },
        orderBy: { createdAt: "desc" },
        take: limit,
        include: {
            chat_message: {
                include: {
                    user: {
                        select: {
                            id: true,
                            avatar: true,
                            username: true,
                        },
                    },
                },
            },
            user: {
                select: {
                    id: true,
                    avatar: true,
                    username: true,
                    createdAt: true,
                    userType: true,
                    level: true,
                },
            },
        },
    });
};
export const findAllChatRooms = async () => {
    return await db.chat_room.findMany();
};
export const findMessageWithUser = async (messageId) => {
    return await db.chat_message.findUnique({
        where: {
            id: messageId,
        },
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    avatar: true,
                },
            },
        },
    });
};
export const createChatMessageWithUser = async (data) => {
    return await db.chat_message.create({
        data,
    });
};
export const countEmoteUsage = async (emote) => {
    return await db.chat_message.count({
        where: {
            chatRoomId: 1,
            message: {
                contains: `:${emote}`,
            },
        },
    });
};
