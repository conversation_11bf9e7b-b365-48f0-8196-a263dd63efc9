import { LogErrorStack, logger } from "../utils/log.js";
import { createClient } from "redis";
const redisURL = process.env.REDIS_URL;
const redisOptions = {
    url: redisURL,
};
if (!redisOptions.url) {
    logger.warn("Redis connection configuration is incomplete");
}
const redisClient = createClient(redisOptions);
redisClient.on("error", (err) => {
    logger.error(`Error occurred while connecting to redis: ${err}`);
});
const getRedisItem = async (key) => {
    try {
        const item = await redisClient.get(key);
        if (!item || typeof item !== "string")
            return null;
        try {
            return JSON.parse(item);
        }
        catch (error) {
            LogErrorStack({ message: `Error occurred while parsing redis item`, error });
            return item;
        }
    }
    catch (error) {
        LogErrorStack({ message: `Error occurred while getting redis item`, error });
        return null;
    }
};
export { redisClient, redisOptions, getRedisItem };
