export const maintenanceState = {
    isEnabled: false,
};
const isAdmin = (user) => user.userType === "admin";
export const maintenanceCheck = (req, res, next) => {
    const bypassPaths = ["/live", "/admin/toggle-maintenance"];
    if (bypassPaths.includes(req.path)) {
        return next();
    }
    if (maintenanceState.isEnabled) {
        if (req.user && isAdmin(req.user)) {
            return next();
        }
        res.status(503).json({
            success: false,
            data: null,
            error: "Server is currently in maintenance mode. Please try again later.",
            maintenanceMode: true,
        });
        return;
    }
    next();
};
export default maintenanceCheck;
