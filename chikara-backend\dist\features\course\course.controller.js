import AchievementHelpers from "../../core/achievement.service.js";
import courses, { CourseRewardType } from "../../data/courses.js";
import * as courseRepository from "../../repositories/course.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { expiryQueue } from "../../queues/expiryValues/queue.js";
import { LogErrorStack } from "../../utils/log.js";
import * as UserRepository from "../../repositories/user.repository.js";
export const handleCourseCompletion = async (user) => {
    if (!user) {
        throw new Error("User not found");
    }
    const selectedCourse = courses.find((course) => course.id === user.activeCourseId);
    if (!selectedCourse) {
        throw new Error("Course not found");
    }
    await courseRepository.createCompletedCourse(user.id, selectedCourse.id);
    let updatedUser;
    switch (selectedCourse.rewardType) {
        case CourseRewardType.STAT: {
            if (selectedCourse.stat && selectedCourse.amount) {
                updatedUser = await courseRepository.updateUserAfterCourseCompletion(user.id, selectedCourse.rewardType, selectedCourse.stat, selectedCourse.amount);
            }
            break;
        }
        case CourseRewardType.TALENT_POINTS: {
            if (selectedCourse.amount) {
                updatedUser = await courseRepository.updateUserAfterCourseCompletion(user.id, selectedCourse.rewardType, undefined, selectedCourse.amount);
            }
            break;
        }
        case CourseRewardType.CRAFTING_RECIPE: {
            if (selectedCourse.recipeId) {
                updatedUser = await courseRepository.updateUserAfterCourseCompletion(user.id, selectedCourse.rewardType, undefined, undefined, selectedCourse.recipeId);
            }
            break;
        }
        default: {
            updatedUser = await courseRepository.updateUserAfterCourseCompletion(user.id, selectedCourse.rewardType);
        }
    }
    await AchievementHelpers.UpdateUserAchievement(user.id, "coursesCompleted");
    const rewardInfo = (() => {
        switch (selectedCourse.rewardType) {
            case CourseRewardType.STAT: {
                return { statImproved: selectedCourse.stat, amount: selectedCourse.amount };
            }
            case CourseRewardType.TALENT_POINTS: {
                return { talentPoints: selectedCourse.amount };
            }
            case CourseRewardType.CRAFTING_RECIPE: {
                return { recipeUnlocked: selectedCourse.recipeId };
            }
            default: {
                return {};
            }
        }
    })();
    logAction({
        action: "COURSE_COMPLETED",
        userId: user.id,
        info: {
            courseId: selectedCourse.id,
            courseName: selectedCourse.name,
            rewardType: selectedCourse.rewardType,
            ...rewardInfo,
        },
    });
    return updatedUser;
};
export const courseList = async (userId) => {
    try {
        const completedCourses = await courseRepository.findCompletedCourses(userId);
        const completedCourseIds = new Set(completedCourses.map((course) => course.courseId));
        const coursesWithCompletion = courses.map((course) => ({
            ...course,
            completed: completedCourseIds.has(course.id),
        }));
        return { data: coursesWithCompletion };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to get course list", statusCode: 400 };
    }
};
export const startCourse = async (userId, courseId) => {
    try {
        const selectedCourse = courses.find((course) => course.id === courseId);
        if (!selectedCourse) {
            return { error: "Invalid course", statusCode: 400 };
        }
        const currentUser = await UserRepository.getUserById(userId);
        if (!currentUser) {
            return { error: "UserType not found", statusCode: 404 };
        }
        if (currentUser.activeCourseId) {
            return { error: "Already enrolled on a course!", statusCode: 400 };
        }
        const completedCourse = await courseRepository.findCompletedCourseByUserAndCourse(currentUser.id, selectedCourse.id);
        if (completedCourse) {
            return { error: "You have already completed this course!", statusCode: 400 };
        }
        if (currentUser.cash < selectedCourse.cost) {
            return { error: "You do not have enough money for this course!", statusCode: 400 };
        }
        const courseEndTime = Date.now() + selectedCourse.time;
        const user = await courseRepository.updateUserOnCourseStart(currentUser.id, selectedCourse.id, selectedCourse.cost, courseEndTime);
        if (!user.courseEnds) {
            throw new Error("Course end time not found");
        }
        const expiryTime = user.courseEnds.toString();
        await expiryQueue.add("course-expiry", {
            userId: currentUser.id,
            action: "courseEnds",
            expiryTime,
        }, {
            delay: selectedCourse.time,
            jobId: `course-${currentUser.id}-${expiryTime}`,
            attempts: 3,
            removeOnComplete: true,
            removeOnFail: false,
        });
        logAction({
            action: "COURSE_STARTED",
            userId: currentUser.id,
            info: {
                courseId: selectedCourse.id,
                courseName: selectedCourse.name,
                rewardType: selectedCourse.rewardType,
            },
        });
        return { data: { id: selectedCourse.id } };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to start course", statusCode: 400 };
    }
};
