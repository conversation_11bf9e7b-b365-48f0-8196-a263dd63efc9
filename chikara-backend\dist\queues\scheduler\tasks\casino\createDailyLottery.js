import * as CasinoRepository from "../../../../repositories/casino.repository.js";
import * as <PERSON><PERSON><PERSON><PERSON>per from "../../../../features/chat/chat.helpers.js";
import { getToday } from "../../../../utils/dateHelpers.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";
async function announceLotteryStarted() {
    await ChatHelper.SendAnnouncementMessage("lotteryStarted", `A new lottery draw has started at the casino. The winner will be announced at 6:30PM tomorrow.`);
}
export async function createDailyLottery() {
    try {
        const today = getToday();
        const existingLottery = await CasinoRepository.findLotteryByDate(today);
        if (!existingLottery) {
            await CasinoRepository.createLottery(today);
        }
        await announceLotteryStarted();
        logger.info("Daily lottery created");
    }
    catch (error) {
        LogErrorStack({ message: "Error creating daily lottery:", error });
    }
}
