import { ExploreNodeLocation } from "@prisma/client";
import { LogErrorStack, logger } from "../../../utils/log.js";
import * as StoryService from "../../../core/story.service.js";
import * as StoryRepository from "../../../repositories/story.repository.js";
import * as ExploreRepository from "../../../repositories/explore.repository.js";
import type { MapNodePosition } from "../explore.types.js";
import type { StoryEpisodeData } from "../../story/story.types.js";

/**
 * Story node interaction result
 */
export interface StoryNodeInteractionResult {
    success: boolean;
    message: string;
    redirectToStoryPlayer?: boolean;
    episodeData?: StoryEpisodeData;
}

/**
 * Handle story node interaction
 * Now works with quest objectives instead of direct episode progress
 */
export const handleStoryNodeInteraction = async (
    userId: number,
    nodeId: number,
    location: ExploreNodeLocation,
    metadata: Record<string, unknown>
): Promise<StoryNodeInteractionResult> => {
    try {
        const episodeId = metadata.episodeId as number;

        if (!episodeId) {
            logger.error(`Story node ${nodeId} missing episodeId in metadata`);
            return {
                success: false,
                message: "Invalid story node configuration",
            };
        }

        // Get episode details
        const episode = await StoryRepository.findEpisodeById(episodeId);
        if (!episode) {
            logger.error(`Episode ${episodeId} not found for story node ${nodeId}`);
            return {
                success: false,
                message: "Story episode not found",
            };
        }

        // Note: We don't trigger quest objective completion here anymore
        // The COMPLETE_STORY_EPISODE objective will be completed when the episode is finished
        // This prevents the story node from being removed before the episode player is shown

        logger.info(
            `User ${userId} interacted with story node ${nodeId} for episode ${episodeId}, preparing to show story episode player`
        );

        return {
            success: true,
            message: `Started story episode: ${episode.name}`,
            redirectToStoryPlayer: true,
            episodeData: episode,
        };
    } catch (error) {
        LogErrorStack({ message: "Failed to handle story node interaction", error });
        return {
            success: false,
            message: "Failed to start story episode",
        };
    }
};

/**
 * Create story nodes for active episodes
 */
export const createStoryNodesForUser = async (userId: number, location: ExploreNodeLocation): Promise<void> => {
    try {
        logger.info(`Creating story nodes for user ${userId} in location ${location}`);

        // Get active story nodes for this location
        const activeStoryNodes = await StoryService.getActiveStoryNodes(userId, location);
        logger.info(`Found ${activeStoryNodes.length} active story nodes for user ${userId} in ${location}`);

        // Get existing story nodes for this user and location
        const existingNodes = await ExploreRepository.getPlayerNodesByUserAndLocation(userId, location);
        const existingStoryNodes = existingNodes.filter((node) => node.nodeType === "STORY");
        logger.info(`Found ${existingStoryNodes.length} existing story nodes for user ${userId} in ${location}`);

        // Remove inactive story nodes
        for (const existingNode of existingStoryNodes) {
            const episodeId = existingNode.metadata?.episodeId as number;
            const isStillActive = activeStoryNodes.some((node) => node.id === episodeId);

            if (!isStillActive) {
                await ExploreRepository.deletePlayerNode(existingNode.id, userId);
                logger.info(`Removed inactive story node ${existingNode.id} for episode ${episodeId}`);
            }
        }

        // Create new story nodes for episodes that don't have nodes yet
        for (const storyNode of activeStoryNodes) {
            const hasExistingNode = existingStoryNodes.some(
                (node) => (node.metadata?.episodeId as number) === storyNode.id
            );

            if (hasExistingNode) {
                logger.info(`Story node already exists for episode ${storyNode.id} in ${location}`);
            } else {
                logger.info(`Creating new story node for episode ${storyNode.id} (${storyNode.name}) in ${location}`);
                await createStoryNode(userId, storyNode, storyNode.location);
            }
        }
    } catch (error) {
        LogErrorStack({ message: "Failed to create story nodes for user", error });
    }
};

/**
 * Create a single story node
 */
const createStoryNode = async (
    userId: number,
    storyNodeData: {
        id: number;
        name: string;
        description: string | null;
        location: ExploreNodeLocation;
        episodeType: string;
        expiresAt: Date | null;
        questObjectiveId?: number;
    },
    nodeLocation: ExploreNodeLocation
): Promise<void> => {
    try {
        // Find an available position for the story node at the episode's specified location
        const position = await findAvailablePosition(userId, nodeLocation);
        if (!position) {
            logger.warn(`No available position for story node in ${nodeLocation} for user ${userId}`);
            return;
        }

        // Create the story node with reference to the episode and quest objective
        const createdNode = await ExploreRepository.createPlayerNode(
            userId,
            "STORY",
            `📖 ${storyNodeData.name}`,
            storyNodeData.description || "A story episode awaits...",
            position,
            nodeLocation,
            null,
            {
                episodeId: storyNodeData.id,
                episodeType: storyNodeData.episodeType,
                questObjectiveId: storyNodeData.questObjectiveId,
                storyNode: true,
            },
            "available"
        );

        logger.info(
            `Created story node ${createdNode.id} for episode ${storyNodeData.id} at position (${position.x}, ${position.y}) in ${nodeLocation}`
        );
    } catch (error) {
        LogErrorStack({ message: "Failed to create story node", error });
    }
};

/**
 * Find an available position for a new story node
 */
const findAvailablePosition = async (
    userId: number,
    location: ExploreNodeLocation
): Promise<MapNodePosition | null> => {
    try {
        // Get all existing nodes for this location
        const [staticNodes, playerNodes] = await Promise.all([
            ExploreRepository.getStaticNodesByLocation(location),
            ExploreRepository.getPlayerNodesByUserAndLocation(userId, location),
        ]);

        // Create a set of occupied positions
        const occupiedPositions = new Set<string>();

        for (const node of staticNodes) {
            occupiedPositions.add(`${node.position.x},${node.position.y}`);
        }

        for (const node of playerNodes) {
            occupiedPositions.add(`${node.position.x},${node.position.y}`);
        }

        // Find first available position (5x5 grid, 0-4 coordinates)
        for (let y = 0; y < 5; y++) {
            for (let x = 0; x < 5; x++) {
                const positionKey = `${x},${y}`;
                if (!occupiedPositions.has(positionKey)) {
                    return { x, y };
                }
            }
        }

        return null; // No available positions
    } catch (error) {
        LogErrorStack({ message: "Failed to find available position", error });
        return null;
    }
};

/**
 * Remove story node from explore map when episode is completed
 */
export const removeStoryNode = async (
    userId: number,
    episodeId: number,
    location: ExploreNodeLocation | null
): Promise<void> => {
    try {
        if (location) {
            // If location is provided, search only in that location
            const playerNodes = await ExploreRepository.getPlayerNodesByUserAndLocation(userId, location);
            const storyNode = playerNodes.find(
                (node) => node.nodeType === "STORY" && node.metadata?.episodeId === episodeId
            );

            if (storyNode) {
                await ExploreRepository.deletePlayerNode(storyNode.id, userId);
                logger.debug(
                    `Successfully removed story node ${storyNode.id} for episode ${episodeId} for user ${userId} at ${location}`
                );
                return;
            }
        } else {
            // If no location provided, search all locations for the story node
            const allLocations = Object.values(ExploreNodeLocation);

            for (const searchLocation of allLocations) {
                const playerNodes = await ExploreRepository.getPlayerNodesByUserAndLocation(userId, searchLocation);
                const storyNode = playerNodes.find(
                    (node) => node.nodeType === "STORY" && node.metadata?.episodeId === episodeId
                );

                if (storyNode) {
                    await ExploreRepository.deletePlayerNode(storyNode.id, userId);
                    logger.debug(
                        `Successfully removed story node ${storyNode.id} for episode ${episodeId} for user ${userId} at ${searchLocation}`
                    );
                    return;
                }
            }
        }

        logger.debug(`No story node found for episode ${episodeId} for user ${userId}`);
    } catch (error) {
        LogErrorStack({ message: `Failed to remove story node for episode ${episodeId}`, error });
    }
};
