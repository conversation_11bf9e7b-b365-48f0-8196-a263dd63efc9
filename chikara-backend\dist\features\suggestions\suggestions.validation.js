import { SuggestionStates, SuggestionVoteTypes } from "@prisma/client";
import z from "zod";
const suggestionsValidation = {
    getComments: z.object({
        id: z.string().or(z.number()).transform(Number),
    }),
    getPollResults: z.object({
        pollId: z.string().or(z.number()).transform(Number),
    }),
    postSuggestion: z.object({
        title: z.string().min(1),
        content: z.string().min(1),
    }),
    commentOnSuggestion: z.object({
        id: z.number(),
        message: z.string().max(800),
    }),
    changeSuggestionState: z.object({
        suggestionId: z.number(),
        state: z.nativeEnum(SuggestionStates),
    }),
    createPollResponse: z.object({
        pollId: z.number(),
        pollResponse: z.any(),
    }),
    suggestionVote: z.object({
        suggestionId: z.number(),
        vote: z.nativeEnum(SuggestionVoteTypes),
    }),
};
export default suggestionsValidation;
