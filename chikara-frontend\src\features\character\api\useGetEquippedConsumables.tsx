import { APIROUTES } from "@/helpers/apiRoutes";
import { handleGet } from "@/helpers/axiosInstance";
import { InventoryItem } from "@/types/item";
import { type UseQueryOptions, useQuery } from "@tanstack/react-query";

export type ConsumableItem = {
    slot: number;
    userItemId: number;
    item: InventoryItem;
    count: number;
};

type EquippedConsumablesResponse = ConsumableItem[];

/**
 * Custom hook to fetch user's equipped consumables
 */
export const useGetEquippedConsumables = (
    options: Partial<UseQueryOptions<EquippedConsumablesResponse, Error, EquippedConsumablesResponse>> = {}
) => {
    return useQuery<EquippedConsumablesResponse, Error, EquippedConsumablesResponse>({
        queryKey: APIROUTES.USER.EQUIPPEDCONSUMABLES,
        queryFn: async () => {
            const data = await handleGet<EquippedConsumablesResponse>(APIROUTES.USER.EQUIPPEDCONSUMABLES);
            return data;
        },
        ...options,
    });
};

export default useGetEquippedConsumables;
