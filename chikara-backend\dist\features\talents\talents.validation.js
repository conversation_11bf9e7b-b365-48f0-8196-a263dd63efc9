import { z } from "zod";
export const unlockTalentSchema = z.object({
    talentId: z.number().int().positive(),
});
const abilitySlotSchema = z.union([z.literal(1), z.literal(2), z.literal(3), z.literal(4)]);
export const equipAbilitySchema = z.object({
    talentId: z.number().int().positive(),
    slot: abilitySlotSchema,
});
export const unequipAbilitySchema = z.object({
    slot: abilitySlotSchema,
});
export default {
    unlockTalentSchema,
    equipAbilitySchema,
    unequipAbilitySchema,
};
