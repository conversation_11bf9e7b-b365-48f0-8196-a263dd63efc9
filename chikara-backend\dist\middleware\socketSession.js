import { auth } from "../lib/auth.js";
import { fromNodeHeaders } from "better-auth/node";
const socketSession = async (req, next) => {
    try {
        const session = await auth.api.getSession({
            headers: fromNodeHeaders(req.headers),
        });
        if (!session) {
            req.user = null;
            req.session = null;
            return next();
        }
        req.user = { id: Number.parseInt(session.user.id) };
        req.session = session.session;
        return next();
    }
    catch (error) {
        return next(error instanceof Error ? error : new Error(String(error)));
    }
};
export default socketSession;
