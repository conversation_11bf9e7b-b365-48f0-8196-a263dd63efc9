import { createPollResponse, getAvailablePolls, getPollResults } from "./polls.controller.js";
import * as SuggestionsController from "./suggestions.controller.js";
import suggestionsSchema from "./suggestions.validation.js";
import { isLoggedInAuth, adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
export const suggestionsRouter = {
    getSuggestions: isLoggedInAuth.handler(async () => {
        const result = await SuggestionsController.getSuggestions();
        return handleResponse(result);
    }),
    getVoteHistory: isLoggedInAuth.handler(async ({ context }) => {
        const result = await SuggestionsController.getVoteHistory(context.user.id);
        return handleResponse(result);
    }),
    getComments: isLoggedInAuth.input(suggestionsSchema.getComments).handler(async ({ input }) => {
        const result = await SuggestionsController.getComments(input.id);
        return handleResponse(result);
    }),
    vote: isLoggedInAuth.input(suggestionsSchema.suggestionVote).handler(async ({ input, context }) => {
        const { suggestionId, vote } = input;
        const result = await SuggestionsController.suggestionVote(context.user.id, suggestionId, vote);
        return handleResponse(result);
    }),
    comment: isLoggedInAuth.input(suggestionsSchema.commentOnSuggestion).handler(async ({ input, context }) => {
        const { id, message } = input;
        const result = await SuggestionsController.CommentOnSuggestion(context.user.id, id, message);
        return handleResponse(result);
    }),
    create: isLoggedInAuth.input(suggestionsSchema.postSuggestion).handler(async ({ input, context }) => {
        const { title, content } = input;
        const result = await SuggestionsController.postSuggestion(context.user.id, title, content);
        return handleResponse(result);
    }),
    changeState: adminAuth.input(suggestionsSchema.changeSuggestionState).handler(async ({ input, context }) => {
        const { suggestionId, state } = input;
        const result = await SuggestionsController.changeSuggestionState(context.user.id, suggestionId, state);
        return handleResponse(result);
    }),
    getAvailablePolls: isLoggedInAuth.handler(async ({ context }) => {
        const result = await getAvailablePolls(context.user.id);
        return handleResponse(result);
    }),
    getPollResults: isLoggedInAuth.input(suggestionsSchema.getPollResults).handler(async ({ input, context }) => {
        const result = await getPollResults(input.pollId, context.user.userType);
        return handleResponse(result);
    }),
    submitPollResponse: isLoggedInAuth
        .input(suggestionsSchema.createPollResponse)
        .handler(async ({ input, context }) => {
        const { pollId, pollResponse } = input;
        const result = await createPollResponse(context.user.id, pollId, pollResponse);
        return handleResponse(result);
    }),
};
export default suggestionsRouter;
