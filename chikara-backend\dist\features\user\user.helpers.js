import * as UserService from "../../core/user.service.js";
import * as TalentHelper from "../../features/talents/talents.helpers.js";
import { EquipSlots } from "@prisma/client";
import * as UserRepository from "../../repositories/user.repository.js";
import * as PetsRepository from "../../repositories/pets.repository.js";
import * as SkillsService from "../../core/skills.service.js";
import { LogErrorStack } from "../../utils/log.js";
import * as EquipmentService from "../../core/equipment.service.js";
export const GetSafeUserDict = (user) => {
    const values = Object.assign({}, user);
    const valuesToDelete = [
        "updatedAt",
        "last_activity",
        "roguelikeMap",
        "adminNotes",
        "combatLevel",
    ];
    for (const val of valuesToDelete) {
        delete values[val];
    }
    return values;
};
export const GetUserByIdWithAssociations = async (id) => {
    return await UserRepository.findUserByIdWithGang(id);
};
export const EquipItemToUser = async (user, userItem) => {
    if (!userItem || !userItem.item)
        return false;
    const item = userItem.item;
    const equipSlot = item.itemType;
    if (!Object.values(EquipSlots).includes(equipSlot)) {
        return false;
    }
    if (equipSlot === EquipSlots.shield) {
        await EquipmentService.UnequipItemFromSlot(user.id, EquipSlots.offhand);
    }
    else if (equipSlot === EquipSlots.offhand) {
        await EquipmentService.UnequipItemFromSlot(user.id, EquipSlots.shield);
    }
    await UserRepository.upsertEquippedItem(user.id, {
        slot: equipSlot,
        userItemId: userItem.id,
    });
    return true;
};
export const useConsumable = async (item, currentUser, info) => {
    const itemEffects = item.itemEffects;
    if (itemEffects) {
        info.effectRemoved = false;
    }
    if (item.health) {
        const goodStomachTalent = await TalentHelper.UserHasGoodStomachTalent(currentUser.id);
        const healAmount = goodStomachTalent ? item.health * (goodStomachTalent.modifier || 0) : item.health;
        currentUser.currentHealth = Math.min(currentUser.health, currentUser.currentHealth + healAmount);
    }
    if (item.energy) {
        currentUser.energy = Math.min(100, currentUser.energy + item.energy);
    }
    if (item.actionPoints) {
        currentUser.actionPoints = Math.min(currentUser.maxActionPoints, currentUser.actionPoints + item.actionPoints);
    }
    await UserService.updateUser(currentUser.id, {
        currentHealth: currentUser.currentHealth,
        energy: currentUser.energy,
        actionPoints: currentUser.actionPoints,
    });
    return info;
};
export const usePetItem = async (item, currentUser, info) => {
    const petUnlockId = item.petUnlockId;
    if (!petUnlockId) {
        throw new Error("Item invalid");
    }
    const petDetails = await PetsRepository.getPetById(petUnlockId);
    if (!petDetails) {
        throw new Error("Pet not found");
    }
    const defaultEvolution = petDetails.evolution_stages[0];
    const nextEvolution = petDetails.evolution_stages[1];
    const userCurrentPets = await PetsRepository.getUserPets(currentUser.id);
    const isActive = !(userCurrentPets && userCurrentPets.length > 0);
    const createPetData = {
        user: { connect: { id: currentUser.id } },
        pet: { connect: { id: petDetails.id } },
        name: petDetails.name,
        isActive,
        evolution: {
            current: defaultEvolution.stage,
            next: nextEvolution?.stage || null,
            progress: 0,
            requiredEggProgress: 100,
        },
    };
    const userPet = await PetsRepository.createUserPet(createPetData);
    if (!userPet) {
        throw new Error("Failed to create user pet");
    }
    info.petUnlocked = true;
    return info;
};
export async function getUserStatLevel(userId, stat) {
    try {
        const skillType = stat;
        return await SkillsService.getSkillLevel(userId, skillType);
    }
    catch (error) {
        LogErrorStack({ message: `Failed to fetch ${stat} stat for user ${userId}`, error });
        throw new Error("Failed to retrieve user stat level");
    }
}
export async function getAllUserStatLevels(userId) {
    const stats = ["strength", "intelligence", "dexterity", "defence", "endurance", "vitality"];
    const statLevels = {};
    for (const stat of stats) {
        statLevels[stat] = await getUserStatLevel(userId, stat);
    }
    return statLevels;
}
