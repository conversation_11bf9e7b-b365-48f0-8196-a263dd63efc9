import * as InventoryService from "../../../core/inventory.service.js";
import * as NotificationService from "../../../core/notification.service.js";
import * as StatusEffectService from "../../../core/statuseffect.service.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import { logAction } from "../../../lib/actionLogger.js";
import { db } from "../../../lib/db.js";
import { NotificationTypes } from "../../../types/notification.js";
import { mapExploreLocationToLocationTypes } from "../explore.helpers.js";
import * as ExploreRepository from "../../../repositories/explore.repository.js";
export const FORAGING_CONFIG = {
    FAIL_CHANCE: 0.2,
    INJURY_CHANCE: 0.5,
    SUCCESS_BONUS_CHANCE: 0.25,
    DEFAULT_ENERGY_COST: 2,
    FORAGING_TIMEOUT_MS: 90 * 1000,
    FORAGING_TYPES: ["herbs", "berries", "mushrooms", "flowers", "roots"],
    INJURY_TYPES: ["thorn_scratch", "poisonous_plant", "exhaustion"],
};
export const findExploreForagingDrops = async (userLevel, location, foragingType) => {
    const mappedLocation = mapExploreLocationToLocationTypes(location);
    return await db.drop_chance.findMany({
        where: {
            dropChanceType: "scavenge",
            scavengeType: foragingType,
            location: {
                in: [mappedLocation, "any"],
            },
            minLevel: {
                lte: userLevel,
            },
            maxLevel: {
                gte: userLevel,
            },
            dropRate: {
                gt: 0,
            },
        },
        include: {
            item: true,
        },
        orderBy: {
            dropRate: "asc",
        },
    });
};
export const handleForagingSuccess = async (user, location, foragingType, difficulty) => {
    const potentialDrops = await findExploreForagingDrops(user.level, location, foragingType);
    if (!potentialDrops || potentialDrops.length === 0) {
        return null;
    }
    const randomIndex = Math.floor(Math.random() * potentialDrops.length);
    const selectedItem = potentialDrops[randomIndex];
    if (selectedItem.itemId === null || !selectedItem.item) {
        return null;
    }
    const difficultyMultiplier = difficulty === "easy" ? 1 : difficulty === "medium" ? 1.3 : 1.5;
    const baseQuantity = selectedItem.quantity;
    const finalQuantity = Math.max(1, Math.floor(baseQuantity * difficultyMultiplier));
    await InventoryService.AddItemToUser({
        userId: user.id,
        itemId: selectedItem.itemId,
        amount: finalQuantity,
        isTradeable: true,
    });
    const baseExp = 10;
    const diffExp = difficulty === "easy" ? 1 : difficulty === "medium" ? 1.3 : 1.5;
    const experienceGained = Math.floor(baseExp * diffExp);
    let bonusResult = null;
    if (Math.random() <= FORAGING_CONFIG.SUCCESS_BONUS_CHANCE) {
        const bonusItem = potentialDrops[Math.floor(Math.random() * potentialDrops.length)];
        if (bonusItem.itemId && bonusItem.item) {
            const bonusQuantity = Math.max(1, Math.floor(bonusItem.quantity * 0.6));
            await InventoryService.AddItemToUser({
                userId: user.id,
                itemId: bonusItem.itemId,
                amount: bonusQuantity,
                isTradeable: true,
            });
            bonusResult = {
                bonusReward: bonusItem.item,
                bonusQuantity,
            };
        }
    }
    logAction({
        action: "EXPLORE_FORAGING_SUCCESS",
        userId: user.id,
        info: {
            itemId: selectedItem.item.id,
            itemName: selectedItem.item.name,
            quantity: finalQuantity,
            location: location,
            foragingType: foragingType,
            difficulty: difficulty,
            experienceGained,
            bonusReward: bonusResult?.bonusReward?.name || null,
        },
    });
    return {
        itemReward: selectedItem.item,
        itemQuantity: finalQuantity,
        experienceGained,
        ...bonusResult,
    };
};
export const handleForagingFailure = async (currentUser, location, foragingType, difficulty) => {
    const outcome = {};
    const injuryTypes = ["Physical", "Mental"];
    const injuryType = injuryTypes[Math.floor(Math.random() * injuryTypes.length)];
    const injury = await StatusEffectService.GetRandomInjury("Minor", injuryType);
    if (injury) {
        await StatusEffectService.ApplyStatusEffectToUser(currentUser, injury);
        outcome.injury = injury;
        NotificationService.NotifyUser(currentUser.id, NotificationTypes.injured, {
            reason: "foraging accident",
            injury: injury.name,
            injuryTier: injury.tier,
        }, true);
        logAction({
            action: "EXPLORE_FORAGING_INJURY",
            userId: currentUser.id,
            info: {
                injury: injury.name,
                injuryTier: injury.tier,
                location: location,
                foragingType: foragingType,
                difficulty: difficulty,
            },
        });
    }
    return outcome;
};
export const determineForagingDifficulty = (location, userLevel) => {
    const locationDifficulty = {
        shibuya: "medium",
        shinjuku: "hard",
        bunkyo: "easy",
        chiyoda: "medium",
        minato: "easy",
    };
    const baseDifficulty = locationDifficulty[location] || "easy";
    if (userLevel >= 25) {
        return baseDifficulty === "easy" ? "easy" : baseDifficulty === "medium" ? "medium" : "hard";
    }
    else if (userLevel >= 10) {
        return baseDifficulty === "easy" ? "easy" : baseDifficulty === "medium" ? "easy" : "medium";
    }
    return "easy";
};
export const selectForagingType = (location) => {
    const locationForagingTypes = {
        shibuya: ["herbs", "flowers"],
        shinjuku: ["herbs", "roots"],
        bunkyo: ["berries", "flowers", "herbs"],
        chiyoda: ["flowers", "herbs"],
        minato: ["berries", "mushrooms"],
    };
    const availableTypes = locationForagingTypes[location] || ["herbs"];
    return availableTypes[Math.floor(Math.random() * availableTypes.length)];
};
export const initializeForaging = async (userId, location) => {
    const user = await UserRepository.getUserById(userId);
    if (!user) {
        throw new Error("User not found");
    }
    const foragingType = selectForagingType(location);
    const difficulty = determineForagingDifficulty(location, user.level);
    const energyCost = FORAGING_CONFIG.DEFAULT_ENERGY_COST * (difficulty === "easy" ? 1 : difficulty === "medium" ? 1.2 : 1.5);
    const foragingValidUntil = Date.now() + FORAGING_CONFIG.FORAGING_TIMEOUT_MS;
    logAction({
        action: "EXPLORE_FORAGING_INITIATED",
        userId: userId,
        info: {
            location: location,
            foragingType: foragingType,
            difficulty: difficulty,
            energyCost: energyCost,
        },
    });
    return {
        foragingType,
        difficulty,
        energyCost,
        foragingValidUntil,
    };
};
export const processForaging = async (userId, nodeId, location, foragingType, difficulty) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return {
            success: false,
            message: "User not found",
        };
    }
    const goodOutcome = Math.random() > FORAGING_CONFIG.FAIL_CHANCE;
    const result = {
        success: goodOutcome,
        foragingType,
    };
    if (goodOutcome) {
        const foragingResult = await handleForagingSuccess(currentUser, location, foragingType, difficulty);
        if (foragingResult) {
            result.itemReward = foragingResult.itemReward;
            result.itemQuantity = foragingResult.itemQuantity;
            result.experienceGained = foragingResult.experienceGained;
            result.bonusReward = foragingResult.bonusReward;
            result.bonusQuantity = foragingResult.bonusQuantity;
            if (result.bonusReward) {
                result.message = `You successfully foraged ${result.itemQuantity} ${result.itemReward?.name} and found a bonus of ${result.bonusQuantity} ${result.bonusReward.name}!`;
            }
            else {
                result.message = `You successfully foraged ${result.itemQuantity} ${result.itemReward?.name}!`;
            }
        }
        else {
            result.message = "You searched the area thoroughly but didn't find any valuable plants.";
        }
    }
    else {
        const failureResult = await handleForagingFailure(currentUser, location, foragingType, difficulty);
        result.injury = failureResult.injury;
        if (failureResult.injury) {
            result.message = "A foraging accident occurred! You've been injured in the process.";
        }
        else {
            result.message = "The foraging attempt was unsuccessful.";
        }
    }
    return {
        success: true,
        message: "Foraging operation completed",
        data: result,
    };
};
export const handleForagingEncounter = async (userId, nodeId, location) => {
    try {
        const foragingOperation = await initializeForaging(userId, location);
        const metadataUpdated = await ExploreRepository.updatePlayerNodeMetadata(nodeId, userId, {
            foragingType: foragingOperation.foragingType,
            difficulty: foragingOperation.difficulty,
            energyCost: foragingOperation.energyCost,
            foragingValidUntil: foragingOperation.foragingValidUntil,
        });
        if (!metadataUpdated) {
            return {
                success: false,
                message: "Failed to initialize foraging operation",
            };
        }
        const estimatedReward = foragingOperation.difficulty === "easy"
            ? "low"
            : foragingOperation.difficulty === "medium"
                ? "medium"
                : "high";
        return {
            success: true,
            message: "Foraging area prepared. Click 'Gather Plants' to begin collection.",
            data: {
                action: "foraging_ready",
                nodeId,
                foragingType: foragingOperation.foragingType,
                difficulty: foragingOperation.difficulty,
                energyCost: foragingOperation.energyCost,
                estimatedReward,
            },
        };
    }
    catch (error) {
        logAction({
            action: "EXPLORE_FORAGING_ERROR",
            userId: userId,
            info: {
                nodeId,
                location,
                error: error instanceof Error ? error.message : "Unknown error",
            },
        });
        return {
            success: false,
            message: "Failed to prepare foraging area",
        };
    }
};
