import * as QuestRepository from "../../repositories/quest.repository.js";
import { adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import questSchema from "./quest.validation.js";
import z from "zod";
export const questAdminRouter = {
    getAllQuests: adminAuth.handler(async () => {
        const quests = await QuestRepository.getAllQuestsWithDetails();
        return handleResponse({ data: quests });
    }),
    createQuest: adminAuth.input(questSchema.createQuestSchema).handler(async ({ input }) => {
        const quest = await QuestRepository.createQuest(input);
        return handleResponse({ data: quest });
    }),
    updateQuest: adminAuth.input(questSchema.updateQuestSchema).handler(async ({ input }) => {
        const { id, ...updateData } = input;
        const quest = await QuestRepository.updateQuest(id, updateData);
        return handleResponse({ data: quest });
    }),
    deleteQuest: adminAuth.input(z.object({ id: z.number().int().positive() })).handler(async ({ input }) => {
        const result = await QuestRepository.deleteQuest(input.id);
        return handleResponse({ data: result });
    }),
    reorderQuests: adminAuth.input(questSchema.reorderQuestsSchema).handler(async ({ input }) => {
        const { chapterId, questIds } = input;
        const result = await reorderQuestsInChapter(chapterId, questIds);
        return handleResponse({ data: result });
    }),
    getAllObjectives: adminAuth.handler(async () => {
        const objectives = await QuestRepository.getAllObjectivesWithDetails();
        return handleResponse({ data: objectives });
    }),
    createObjective: adminAuth.input(questSchema.createObjectiveSchema).handler(async ({ input }) => {
        const objective = await QuestRepository.createObjective(input);
        return handleResponse({ data: objective });
    }),
    updateObjective: adminAuth.input(questSchema.updateObjectiveSchema).handler(async ({ input }) => {
        const { id, ...updateData } = input;
        const objective = await QuestRepository.updateObjective(id, updateData);
        return handleResponse({ data: objective });
    }),
    deleteObjective: adminAuth.input(z.object({ id: z.number().int().positive() })).handler(async ({ input }) => {
        const result = await QuestRepository.deleteObjective(input.id);
        return handleResponse({ data: result });
    }),
};
export default questAdminRouter;
const reorderQuestsInChapter = async (chapterId, questIds) => {
    if (!chapterId || chapterId <= 0) {
        throw new Error("Invalid chapter ID provided");
    }
    if (!questIds || !Array.isArray(questIds) || questIds.length === 0) {
        throw new Error("Invalid quest IDs array provided");
    }
    const uniqueQuestIds = new Set(questIds);
    if (uniqueQuestIds.size !== questIds.length) {
        throw new Error("Duplicate quest IDs found in the reorder request");
    }
    await QuestRepository.reorderQuestsInChapter(chapterId, questIds);
    return { success: true };
};
