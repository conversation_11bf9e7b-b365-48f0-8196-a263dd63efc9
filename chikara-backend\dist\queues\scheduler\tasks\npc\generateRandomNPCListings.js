import rareShopListings from "../../../../data/rareShopStock.js";
import { logAction } from "../../../../lib/actionLogger.js";
import { db } from "../../../../lib/db.js";
import { getEpochTime } from "../../../../utils/dateHelpers.js";
import { logger } from "../../../../utils/log.js";
import * as ItemRepository from "../../../../repositories/item.repository.js";
import ms from "ms";
const shuffleArray = (array) => {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
};
const getRandomItems = (amount) => {
    const listingItems = rareShopListings.marketListings;
    if (amount > listingItems.length) {
        throw new Error("Requested amount exceeds available items");
    }
    const shuffledItems = shuffleArray([...listingItems]);
    const selectedItems = shuffledItems.slice(0, amount);
    return selectedItems.map((item) => ({
        itemId: item.itemId,
        quantity: item.quantity,
    }));
};
export async function generateRandomNPCListings() {
    try {
        const sellerId = 6;
        const randomItems = getRandomItems(2);
        const currentTime = getEpochTime();
        const endsAt = new Date(currentTime + ms("48h"));
        for (const randomItem of randomItems) {
            const itemId = randomItem.itemId;
            const quantity = randomItem.quantity;
            const itemDetails = await ItemRepository.findItemWithCashValue(randomItem.itemId);
            if (!itemDetails || !itemDetails.cashValue || itemDetails.cashValue < 100) {
                continue;
            }
            const buyoutPrice = itemDetails.cashValue * 4 || 10_000;
            await db.auction_item.create({
                data: {
                    itemId,
                    sellerId,
                    quantity,
                    buyoutPrice,
                    endsAt,
                    deposit: 0,
                    bankFunds: true,
                    status: "in_progress",
                },
            });
            logAction({
                action: "NPC_LISTED_ITEM",
                userId: sellerId,
                info: {
                    itemId,
                    itemName: itemDetails.name,
                    quantity,
                    buyoutPrice,
                },
            });
        }
        logger.info("Generated random NPC listings");
    }
    catch (error) {
        logger.error("Error generating random NPC listings: " + error);
    }
}
