import * as RoguelikeController from "./roguelike.controller.js";
import roguelikeSchema from "./roguelike.validation.js";
import { canMakeStateChangesAuth, isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
export const roguelikeRouter = {
    getCurrentMap: isLoggedInAuth.handler(async ({ context }) => {
        const response = await RoguelikeController.getMap(context.user.id);
        return handleResponse(response);
    }),
    beginRun: canMakeStateChangesAuth
        .input(roguelikeSchema.newRun)
        .handler(async ({ input, context }) => {
        const { level, location } = input;
        const response = await RoguelikeController.newRun(context.user.id, level, location);
        return handleResponse(response);
    }),
    advance: canMakeStateChangesAuth
        .input(roguelikeSchema.advance)
        .handler(async ({ input, context }) => {
        const response = await RoguelikeController.Advance(context.user.id, input.node);
        return handleResponse(response);
    }),
    activateNode: canMakeStateChangesAuth.handler(async ({ context }) => {
        const response = await RoguelikeController.ActivateNode(context.user.id);
        return handleResponse(response);
    }),
    chooseScavengeOption: canMakeStateChangesAuth
        .input(roguelikeSchema.chooseScavengeOption)
        .handler(async ({ input, context }) => {
        const response = await RoguelikeController.ChooseScavengeOption(context.user.id, input.choice);
        return handleResponse(response);
    }),
};
