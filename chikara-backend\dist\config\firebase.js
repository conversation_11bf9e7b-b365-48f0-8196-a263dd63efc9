import { logger } from "../utils/log.js";
import * as admin from "firebase-admin";
import { cert, initializeApp } from "firebase-admin/app";
import { getMessaging } from "firebase-admin/messaging";
const appBaseUrl = process.env.APP_BASE_URL || "https://api.battleacademy.io";
let firebaseApp = null;
export const initializeFirebase = () => {
    return new Promise((resolve) => {
        if (process.env.FIREBASE_ENABLED === "true") {
            try {
                if (!firebaseApp) {
                    firebaseApp = initializeApp({
                        credential: cert({
                            projectId: "chikara-academy",
                            clientEmail: "<EMAIL>",
                            privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
                        }),
                    });
                }
                logger.info("Firebase Notifications Service initialized successfully");
            }
            catch (error) {
                logger.warn(`Failed to initialize Firebase Notifications Service - Invalid credentials: ${String(error)}`);
            }
        }
        resolve();
    });
};
export async function sendPushNotification(userId, token, message) {
    if (token && token.length > 1) {
        const payload = {
            token: token,
            notification: {
                title: "Chikara Academy MMO",
                body: message,
            },
            webpush: {
                headers: {
                    image: "https://d13cmcqz8qkryo.cloudfront.net/static/misc/vX6Dmcr.png",
                },
                fcm_options: {
                    link: appBaseUrl,
                },
            },
            android: {
                notification: {
                    imageUrl: "https://d13cmcqz8qkryo.cloudfront.net/static/misc/vX6Dmcr.png",
                },
            },
        };
        try {
            const messaging = getMessaging();
            const response = await messaging.send(payload);
            if (response) {
                logger.info(`Push notification sent for userId: ${userId}, response ID: ${response}`);
                return response;
            }
        }
        catch (error) {
            logger.debug(`Failed to send push notification: ${String(error)}`);
        }
    }
    return null;
}
export async function sendPushNotifications(tokens, message) {
    const promises = tokens.map((tokenRecord) => tokenRecord.userId && sendPushNotification(tokenRecord.userId, tokenRecord.token, message));
    await Promise.allSettled(promises);
}
export { admin };
