import gameConfig from "../../config/gameConfig.js";
import { findUserPetByIdAndOwner, updatePetEvolutionProgress, updatePetLevelAndXp, updatePetXp, } from "../../repositories/pets.repository.js";
import { getNow } from "../../utils/dateHelpers.js";
const { EGG_TIME_PER_PROGRESS_POINT } = gameConfig;
export const updateEggProgress = async (userPets) => {
    if (!userPets)
        return userPets;
    const eggs = userPets.filter((pet) => pet.evolution.current === "egg");
    if (!eggs)
        return userPets;
    const currentTime = getNow();
    for (const egg of eggs) {
        if (!egg.evolution.requiredEggProgress || egg.evolution.progress >= egg.evolution.requiredEggProgress) {
            continue;
        }
        const eggStartDateTime = egg.createdAt;
        const timeDiff = currentTime.getTime() - eggStartDateTime.getTime();
        const progressPoints = Math.floor(timeDiff / EGG_TIME_PER_PROGRESS_POINT);
        if (progressPoints > egg.evolution.progress) {
            const updatedProgress = Math.min(egg.evolution.progress + progressPoints, egg.evolution.requiredEggProgress);
            await updatePetEvolutionProgress(egg.id, {
                ...egg.evolution,
                progress: updatedProgress,
            });
            egg.evolution.progress = updatedProgress;
        }
    }
    return userPets;
};
export async function verifyPetOwnership(userId, userPetId) {
    const userPet = await findUserPetByIdAndOwner(userId, userPetId);
    if (!userPet) {
        throw new Error("Pet not found or does not belong to this user");
    }
    return userPet;
}
export function getPetEvolutionStage(pet, evolutionStage) {
    if (!pet || !pet.evolution_stages) {
        return null;
    }
    const stages = pet.evolution_stages;
    return stages.find((stage) => stage.stage === evolutionStage) || null;
}
export function calculateEnergyGain(foodType, quantity) {
    let baseGain = quantity * 10;
    switch (foodType) {
        case "premium": {
            baseGain *= 1.5;
            break;
        }
        case "basic": {
            baseGain *= 0.8;
            break;
        }
    }
    return Math.round(baseGain);
}
export function calculateHappinessGain(actionType, duration) {
    let baseGain = 0;
    switch (actionType) {
        case "play": {
            baseGain = duration * 0.5;
            break;
        }
        case "feed": {
            baseGain = duration * 5;
            break;
        }
        case "train": {
            baseGain = duration * 0.2;
            break;
        }
    }
    return Math.round(baseGain);
}
export function calculateXpGain(actionType, duration) {
    let baseGain = 0;
    switch (actionType) {
        case "play": {
            baseGain = duration * 0.2;
            break;
        }
        case "train": {
            baseGain = duration * 0.5;
            break;
        }
    }
    return Math.round(baseGain);
}
export function calculateEvolutionProgress(currentLevel, evolution) {
    const evolutionData = evolution;
    if (!evolutionData.next) {
        return evolutionData;
    }
    const progress = Math.min(Math.floor((currentLevel / evolutionData.requiredLevel) * 100), 100);
    return {
        ...evolutionData,
        progress,
    };
}
export function canPetEvolve(userPet) {
    const evolution = userPet.evolution;
    if (!userPet.evolution || evolution.next === "")
        return false;
    if (evolution.current === "egg") {
        if (!evolution.requiredEggProgress || evolution.progress < evolution.requiredEggProgress) {
            return false;
        }
    }
    else if (!evolution.requiredLevel || userPet.level < evolution.requiredLevel || userPet.happiness < 100) {
        return false;
    }
    return true;
}
export async function addPetXp(userPet, xpGain) {
    const newPetXp = userPet.xp + xpGain;
    if (newPetXp >= userPet.nextLevelXp && userPet.level < userPet.pet.maxLevel) {
        const newLevel = userPet.level + 1;
        const newNextLevelXp = userPet.nextLevelXp * 1.5;
        return await updatePetLevelAndXp(userPet.id, newLevel, 0, Math.floor(newNextLevelXp));
    }
    return await updatePetXp(userPet.id, newPetXp, userPet.nextLevelXp);
}
