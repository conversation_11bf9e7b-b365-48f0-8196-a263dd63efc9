import { db } from "../../../../lib/db.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";
export async function resetDailyEssenceLimit() {
    try {
        await db.gang.updateMany({
            data: {
                dailyEssenceGained: 0,
            },
        });
        logger.info("Daily essence limit reset completed");
    }
    catch (error) {
        LogErrorStack({ message: "Error resetting daily essence limit:", error });
    }
}
