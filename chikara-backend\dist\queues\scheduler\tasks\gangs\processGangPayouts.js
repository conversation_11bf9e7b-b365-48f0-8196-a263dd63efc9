import * as NotificationService from "../../../../core/notification.service.js";
import * as <PERSON><PERSON><PERSON><PERSON> from "../../../../features/gang/gang.helpers.js";
import { logAction } from "../../../../lib/actionLogger.js";
import { db } from "../../../../lib/db.js";
import { NotificationTypes } from "../../../../types/notification.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";
import * as UserRepository from "../../../../repositories/user.repository.js";
const gangRankingMultipliers = {
    1: 1.5,
    2: 1.3,
    3: 1.15,
};
async function resetGangWeeklyValues() {
    try {
        await db.gang.updateMany({
            data: {
                weeklyRespect: 1400,
            },
        });
        await db.gang_member.updateMany({
            data: {
                weeklyMaterials: 0,
                weeklyEssence: 0,
                weeklyRespect: 0,
                weeklyTools: 0,
            },
        });
        logger.info(`Processed weekly value reset for all gangs`);
    }
    catch (error) {
        LogErrorStack({ message: "Error resetting column values:", error });
    }
}
export async function processGangPayouts() {
    logger.profile("processGangPayouts");
    try {
        const gangs = await db.gang.findMany({
            include: {
                gang_member: true,
            },
        });
        if (!gangs || gangs.length === 0) {
            throw new Error("No gangs found.");
        }
        const gangRankings = gangs.sort((a, b) => (b.weeklyRespect ?? 0) - (a.weeklyRespect ?? 0));
        await Promise.all(gangRankings.map(async (gang, index) => {
            const rankingMultiplier = gangRankingMultipliers[index + 1] || 1;
            const gangMembersForHelper = gang.gang_member.map((member) => ({
                ...member,
                userId: member.userId ?? undefined,
            }));
            const totalPayoutPool = Math.round(GangHelper.CalculateGangWeeklyEarnings(gangMembersForHelper.map((member) => ({
                ...member,
                userId: member.userId ?? null,
            })), rankingMultiplier));
            const memberPayouts = GangHelper.CalculateGangPayoutShares(gangMembersForHelper.map((member) => ({
                ...member,
                userId: member.userId ?? null,
            })), totalPayoutPool);
            const payoutPromises = memberPayouts.map(async (member) => {
                const memberPayout = Math.round(totalPayoutPool * ((member.payoutPercentage ?? 0) / 100));
                if (memberPayout && memberPayout > 0 && member.userId) {
                    const user = await UserRepository.getUserById(member.userId);
                    if (user) {
                        try {
                            await db.user.update({
                                where: {
                                    id: user.id,
                                },
                                data: {
                                    gangCreds: {
                                        increment: memberPayout,
                                    },
                                },
                            });
                            logger.info(`Gang creds share for user #${user.id} was ${member.payoutPercentage}%`);
                            NotificationService.NotifyUser(user.id, NotificationTypes.temporary_notification, {
                                gangRank: index + 1,
                                totalAmount: totalPayoutPool,
                                amount: memberPayout,
                                payoutShare: member.payoutPercentage,
                            });
                            logAction({
                                action: "GANG_PAYOUT",
                                userId: user.id,
                                info: {
                                    amount: memberPayout,
                                    gangId: gang.id,
                                },
                            });
                        }
                        catch (error) {
                            LogErrorStack({ error });
                        }
                    }
                }
            });
            if (memberPayouts.length > 0) {
                logger.info(`Gang creds total for gang #${gang.id} was ${totalPayoutPool}`);
                await Promise.all(payoutPromises);
            }
            GangHelper.logGangAction(gang.id, "GangPayout", String(totalPayoutPool), null, null);
        }));
        await resetGangWeeklyValues();
    }
    catch (error) {
        LogErrorStack({ message: `Failed to process gang payouts`, error });
    }
    logger.profile("processGangPayouts");
}
