import { Prisma } from "@prisma/client";
const QUALITY_MODIFIERS = {
    shoddy: 0.85,
    normal: 1.0,
    fine: 1.15,
    excellent: 1.3,
    superior: 1.5,
    perfect: 1.7,
    masterwork: 2.0,
};
const WEAPON_TYPES = ["weapon", "ranged", "offhand"];
const ARMOR_TYPES = ["head", "chest", "hands", "legs", "feet", "finger", "offhand", "shield"];
export const addQualityModifiers = (value, quality) => {
    const modifier = QUALITY_MODIFIERS[quality] || QUALITY_MODIFIERS.normal;
    return Math.round(value * modifier);
};
export const addUpgradeModifiers = (value, upgradeLevel) => {
    if (upgradeLevel && upgradeLevel > 0) {
        const upgradeLevelModifier = 1 + upgradeLevel * 0.05;
        return Math.round(value * upgradeLevelModifier);
    }
    return value;
};
const isValidItem = (item) => {
    return item !== null && typeof item === "object";
};
const hasRequiredFields = (userItem) => {
    return isValidItem(userItem.item) && typeof userItem.quality === "string";
};
export const applyModifiersToItem = (item, quality, upgradeLevel = 0) => {
    if (!item)
        return item;
    const modifiedItem = { ...item };
    const itemType = item.itemType;
    if (WEAPON_TYPES.includes(itemType) &&
        typeof item.damage === "number" &&
        item.damage > 0) {
        let damage = item.damage;
        if (quality !== "normal") {
            damage = addQualityModifiers(damage, quality);
        }
        if (upgradeLevel > 0) {
            damage = addUpgradeModifiers(damage, upgradeLevel);
        }
        modifiedItem.damage = damage;
    }
    if (ARMOR_TYPES.includes(itemType) &&
        typeof item.armour === "number" &&
        item.armour > 0) {
        let armour = item.armour;
        if (quality !== "normal") {
            armour = addQualityModifiers(armour, quality);
        }
        if (upgradeLevel > 0) {
            armour = addUpgradeModifiers(armour, upgradeLevel);
        }
        modifiedItem.armour = armour;
    }
    const upgradableStats = ["health", "energy", "actionPoints", "baseAmmo"];
    for (const stat of upgradableStats) {
        if (typeof item[stat] === "number" && item[stat] > 0 && upgradeLevel > 0) {
            modifiedItem[stat] = addUpgradeModifiers(item[stat], upgradeLevel);
        }
    }
    return modifiedItem;
};
const processUserItem = (userItem) => {
    if (!hasRequiredFields(userItem)) {
        return userItem;
    }
    const upgradeLevel = typeof userItem.upgradeLevel === "number" ? userItem.upgradeLevel : 0;
    return {
        ...userItem,
        item: applyModifiersToItem(userItem.item, userItem.quality, upgradeLevel),
    };
};
const processEquippedItem = (equippedItem) => {
    const userItem = equippedItem.user_item;
    if (!isValidItem(userItem) || !hasRequiredFields(userItem)) {
        return equippedItem;
    }
    const userItemRecord = userItem;
    const upgradeLevel = typeof userItemRecord.upgradeLevel === "number" ? userItemRecord.upgradeLevel : 0;
    return {
        ...equippedItem,
        user_item: {
            ...userItemRecord,
            item: applyModifiersToItem(userItemRecord.item, userItemRecord.quality, upgradeLevel),
        },
    };
};
const processResult = (result, processor) => {
    if (Array.isArray(result)) {
        return result.map((item) => processor(item));
    }
    if (result && typeof result === "object") {
        return processor(result);
    }
    return result;
};
const createQueryInterceptors = (processor) => ({
    async findMany({ args, query }) {
        const result = await query(args);
        return processResult(result, processor);
    },
    async findFirst({ args, query }) {
        const result = await query(args);
        return processResult(result, processor);
    },
    async findUnique({ args, query }) {
        const result = await query(args);
        return processResult(result, processor);
    },
    async create({ args, query }) {
        const result = await query(args);
        return processResult(result, processor);
    },
});
export default Prisma.defineExtension((client) => {
    return client.$extends({
        query: {
            user_item: createQueryInterceptors(processUserItem),
            equipped_item: createQueryInterceptors(processEquippedItem),
        },
    });
});
