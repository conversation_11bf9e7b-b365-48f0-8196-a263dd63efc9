import actionLogRoutes from "./features/actionlog/actionlog.routes.js";
import adminRoutes from "./features/admin/admin.routes.js";
// import authRoutes from "./features/auth/auth.routes.js";
// import bankRoutes from "./features/bank/bank.routes.js";
// import battleRoutes from "./features/battle/battle.routes.js";
// import bountyRoutes from "./features/bounty/bounty.routes.js";
// import casinoRoutes from "./features/casino/casino.routes.js";
// import chatRoutes from "./features/chat/chat.routes.js";
// import courseRoutes from "./features/course/course.routes.js";
// import craftingRoutes from "./features/crafting/crafting.routes.js";
// import creatureRoutes from "./features/creature/creature.routes.js";
// import dailyQuestRoutes from "./features/dailyquest/dailyquest.routes.js";
import devRoutes from "./features/dev/dev.routes.js";
// import dropChanceRoutes from "./features/dropchance/dropchance.routes.js";
// import exploreRoutes from "./features/explore/explore.routes.js";
// import infirmaryRoutes from "./features/infirmary/infirmary.routes.js";
// import itemRoutes from "./features/item/item.routes.js";
// import jailRoutes from "./features/jail/jail.routes.js";
// import jobRoutes from "./features/job/job.routes.js";
// import leaderboardRoutes from "./features/leaderboard/leaderboard.routes.js";
// import missionRoutes from "./features/mission/mission.routes.js";
// import notificationRoutes from "./features/notification/notification.routes.js";
// import petRoutes from "./features/pets/pets.routes.js";
// import privateMessageRoutes from "./features/privatemessage/privatemessage.routes.js";
// import profileCommentRoutes from "./features/profilecomment/profilecomment.routes.js";
// import shrineRoutes from "./features/shrine/shrine.routes.js";
// import skillsRoutes from "./features/skills/skills.routes.js";
// import socialRoutes from "./features/social/social.routes.js";
// import talentsRoutes from "./features/talents/talents.routes.js";
import userRoutes from "./features/user/user.routes.js";
import express from "express";

const router = express.Router();

router.use("/user", userRoutes);
router.use("/admin", actionLogRoutes);
router.use("/admin", adminRoutes);
router.use("/dev", devRoutes);
// router.use("/registration", authRoutes);
// router.use("/bank", bankRoutes);
// router.use("/item", itemRoutes);
// router.use("/job", jobRoutes);
// router.use("/courses", courseRoutes);
// router.use("/chat", chatRoutes);
// router.use("/dms", privateMessageRoutes);
// router.use("/notifications", notificationRoutes);
// router.use("/infirmary", infirmaryRoutes);
// router.use("/jail", jailRoutes);
// router.use("/crafting", craftingRoutes);
// router.use("/creature", creatureRoutes);
// router.use("/leaderboards", leaderboardRoutes);
// router.use("/roguelike", roguelikeRoutes);
// router.use("/talents", talentsRoutes);
// router.use("/user", profileCommentRoutes);
// router.use("/casino", casinoRoutes);
// router.use("/quests", dailyQuestRoutes);
// router.use("/bounty", bountyRoutes);
// router.use("/missions", missionRoutes);
// router.use("/shrine", shrineRoutes);
// router.use("/pets", petRoutes);
// router.use("/social", socialRoutes);
// router.use("/battle", battleRoutes);
// router.use("/dropchance", dropChanceRoutes);
// router.use("/explore", exploreRoutes);
// router.use("/skills", skillsRoutes);

export default router;
