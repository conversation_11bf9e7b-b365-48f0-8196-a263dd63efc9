import { z } from "zod";
import { StoryEpisodeType, ExploreNodeLocation } from "@prisma/client";
export const getSeasonsSchema = z.object({
    includeChapters: z.boolean().optional().default(false),
    includeEpisodes: z.boolean().optional().default(false),
});
export const completeEpisodeSchema = z.object({
    episodeId: z.number().int().positive(),
    choices: z.record(z.string(), z.string()).optional(),
});
export const makeChoiceSchema = z.object({
    episodeId: z.number().int().positive(),
    choiceId: z.string().min(1),
    choiceValue: z.string().min(1),
});
export const createSeasonSchema = z.object({
    name: z.string().min(1).max(255),
    description: z.string().optional(),
    startDate: z.string().datetime(),
    requiredLevel: z.number().int().min(1).default(1),
});
export const updateSeasonSchema = createSeasonSchema.partial().extend({
    id: z.number().int().positive(),
});
export const createChapterSchema = z.object({
    seasonId: z.number().int().positive(),
    name: z.string().min(1).max(255),
    description: z.string().optional(),
    order: z.number().int().positive(),
    unlockDate: z.string().datetime(),
    requiredLevel: z.number().int().min(1).default(1),
    requiredChapterIds: z.array(z.number().int().positive()).optional(),
});
export const updateChapterSchema = createChapterSchema.partial().extend({
    id: z.number().int().positive(),
});
export const createEpisodeSchema = z.object({
    objectiveId: z.number().int().positive(),
    name: z.string().min(1).max(255),
    description: z.string().optional(),
    episodeType: z.nativeEnum(StoryEpisodeType),
    content: z.object({
        scenes: z.array(z.object({
            id: z.number().int(),
            type: z.enum(["dialogue", "narration", "choice", "action", "transition"]),
            speaker: z.string().optional(),
            character: z.string().optional(),
            text: z.string().min(1),
            background: z.string().optional(),
            effects: z.array(z.string()).optional(),
            choices: z
                .array(z.object({
                id: z.string(),
                text: z.string(),
                consequence: z.string().optional(),
                nextSceneId: z.number().int().optional(),
                requirements: z
                    .object({
                    level: z.number().int().optional(),
                })
                    .optional(),
            }))
                .optional(),
        })),
        metadata: z
            .object({
            estimatedDuration: z.number().int().optional(),
            defaultBackground: z.string().optional(),
        })
            .optional(),
    }),
    choices: z
        .record(z.string(), z.object({
        text: z.string(),
        consequences: z.object({
            nextEpisodeId: z.number().int().optional(),
        }),
    }))
        .optional(),
    exploreLocation: z.nativeEnum(ExploreNodeLocation).optional(),
});
export const updateEpisodeSchema = createEpisodeSchema.partial().extend({
    id: z.number().int().positive(),
});
const storySchema = {
    getSeasons: getSeasonsSchema,
    completeEpisode: completeEpisodeSchema,
    makeChoice: makeChoiceSchema,
    createSeason: createSeasonSchema,
    updateSeason: updateSeasonSchema,
    createChapter: createChapterSchema,
    updateChapter: updateChapterSchema,
    createEpisode: createEpisodeSchema,
    updateEpisode: updateEpisodeSchema,
};
export default storySchema;
