import { db } from "../lib/db.js";
import { getTomorrow } from "../utils/dateHelpers.js";
import { DropChanceTypes, QuestProgressStatus } from "@prisma/client";
export const findDailyQuestsByUserId = async (userId, questDate) => {
    return await db.daily_quest.findMany({
        where: {
            userId: userId,
            createdAt: {
                gte: questDate,
                lt: getTomorrow(),
            },
        },
        include: {
            item: true,
        },
    });
};
export const findDailyQuestById = async (questId) => {
    return await db.daily_quest.findUnique({
        where: { id: questId },
    });
};
export const updateDailyQuest = async (id, updateData) => {
    return await db.daily_quest.update({
        where: { id },
        data: updateData,
    });
};
export const countCompletedDailyQuests = async (userId, questDate) => {
    return await db.daily_quest.count({
        where: {
            userId: userId,
            createdAt: {
                gte: questDate,
                lt: getTomorrow(),
            },
            questStatus: QuestProgressStatus.complete,
        },
    });
};
export const findSimilarLevelUser = async (userId, combatLevel, level, range = 5) => {
    return await db.user.findFirst({
        where: {
            combatLevel: {
                lte: combatLevel,
            },
            level: {
                gte: level - range,
                lte: level + range,
            },
            id: {
                not: userId,
            },
            userType: {
                not: "admin",
            },
        },
    });
};
export const findPotentialDrops = async (minLevel, maxLevel) => {
    return await db.drop_chance.findMany({
        where: {
            dropChanceType: DropChanceTypes.roguelike,
            location: "any",
            minLevel: {
                lte: minLevel,
            },
            maxLevel: {
                gte: maxLevel,
            },
            dropRate: {
                gt: 0,
            },
        },
        include: {
            item: true,
        },
        orderBy: {
            dropRate: "asc",
        },
    });
};
export const destroyOldDailyQuests = async (userId, today) => {
    return await db.daily_quest.deleteMany({
        where: {
            userId: userId,
            createdAt: {
                lt: today,
            },
        },
    });
};
export const countDailyQuests = async (userId, questDate) => {
    return await db.daily_quest.count({
        where: {
            userId: userId,
            createdAt: {
                gte: questDate,
                lt: getTomorrow(),
            },
        },
    });
};
export const createDailyQuest = async (questData) => {
    return await db.daily_quest.create({
        data: questData,
    });
};
export const findDailyQuestProgress = async (userId, objectiveType, today, target = null, targetAction = null) => {
    const questFilter = {
        userId,
        objectiveType,
        createdAt: {
            gte: today,
            lt: getTomorrow(),
        },
        questStatus: QuestProgressStatus.in_progress,
    };
    if (target) {
        questFilter.target = target;
    }
    if (targetAction) {
        questFilter.targetAction = targetAction;
    }
    return await db.daily_quest.findFirst({
        where: questFilter,
    });
};
export const findDailyQuestByIdWithRewards = async (questId) => {
    return await db.daily_quest.findUnique({
        where: { id: questId },
    });
};
