import { redisOptions } from "../../config/redisClient.js";
import { handleCourseCompletion } from "../../features/course/course.controller.js";
import { db } from "../../lib/db.js";
import { LogErrorStack, logger } from "../../utils/log.js";
import { Queue, Worker } from "bullmq";
import * as UserRepository from "../../repositories/user.repository.js";
export const expiryQueue = new Queue("user-expiry-queue", { connection: redisOptions });
const worker = new Worker("user-expiry-queue", async (job) => {
    const { userId, action, expiryTime } = job.data;
    logger.info(`Processing ${job.name} for user ${userId}, action: ${action}`);
    try {
        switch (action) {
            case "courseEnds": {
                const user = await UserRepository.getUserById(userId);
                if (user && user.courseEnds && expiryTime !== user.courseEnds.toString()) {
                    LogErrorStack({
                        error: `Course expiry time mismatch for user ${userId}, expected ${expiryTime}, got ${user.courseEnds}`,
                    });
                    return { success: false, userId, action };
                }
                await handleCourseCompletion(user);
                break;
            }
            case "jailedUntil": {
                const user = await UserRepository.getUserById(userId);
                if (user && user.jailedUntil && expiryTime !== user.jailedUntil.toString()) {
                    LogErrorStack({
                        error: `Jail expiry time mismatch for user ${userId}, expected ${expiryTime}, got ${user.jailedUntil}`,
                    });
                    return { success: false, userId, action };
                }
                await db.user.update({
                    where: { id: userId },
                    data: { jailedUntil: null, jailReason: null },
                });
                break;
            }
        }
        return { success: true, userId, action };
    }
    catch (error) {
        LogErrorStack({ message: `Error processing job for user ${userId}`, error });
        throw error;
    }
}, { connection: redisOptions });
worker.on("completed", (job) => {
    logger.debug(`Job ${job.id} completed successfully`);
});
worker.on("failed", (job, err) => {
    LogErrorStack({ message: `Job ${job?.id} failed with error: ${err.message}`, error: err });
});
const closeQueues = async () => {
    logger.info("Closing queue connections...");
    await worker.close();
    await expiryQueue.close();
};
export default {
    expiryQueue,
    worker,
    closeQueues,
};
