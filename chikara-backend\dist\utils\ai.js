import OpenAI from "openai";
import { logAction } from "../lib/actionLogger.js";
const openai = new OpenAI();
const ACTIVE_LLM_API = process.env.ACTIVE_LLM_API || "groq";
const MODEL_MAP = {
    openai: "gpt-4o-mini",
    groq: "llama-3.1-70b-versatile",
    gemini: "gemini-2.0-flash",
};
const getModel = (model) => MODEL_MAP[model] || MODEL_MAP.groq;
const sendAIRequest = async (_messageStructure) => {
    return { success: false, error: "DISABLED" };
};
export const sendAIMessage = async (message) => {
    if (!message || typeof message !== "string")
        return false;
    const messageStructure = {
        model: getModel(ACTIVE_LLM_API),
        messages: [
            { role: "system", content: "You are a helpful assistant." },
            { role: "user", content: message },
        ],
    };
    return await sendAIRequest(messageStructure);
};
export const sendAIMessages = async (messages) => {
    if (!messages)
        return false;
    const messageStructure = {
        model: getModel(ACTIVE_LLM_API),
        messages,
    };
    return await sendAIRequest(messageStructure);
};
export const FlagHarmfulMessages = async (message, user) => {
    if (user.userType === "admin")
        return;
    const moderation = await openai.moderations.create({ model: "omni-moderation-latest", input: message });
    if (moderation.results[0].flagged) {
        logAction({
            action: "FLAGGED_MESSAGE",
            userId: user.id,
            info: {
                message,
            },
        });
    }
};
