import * as EquipmentService from "../../core/equipment.service.js";
import { creatureBaseStats } from "../../data/creatureBaseStats.js";
import { initiateBattle } from "../battle/logic/battle.initiation.js";
import * as CreatureRepository from "../../repositories/creature.repository.js";
import { getAllUserStatLevels } from "../user/user.helpers.js";
const uniqueCreatureStats = {
    "Practice Dummy": { strength: 1, defence: 3, weaponDamage: -6 },
    "Super Practice Dummy": { strength: 1, defence: 5, weaponDamage: -4 },
    "Mysterious Thief": { strength: 65, defence: 60, weaponDamage: 12 },
};
function getZoneScalingMultiplier(enemyLevel, playerLevel) {
    if (enemyLevel === playerLevel) {
        return 1;
    }
    else if (enemyLevel > playerLevel) {
        return Math.pow(1.2, enemyLevel - playerLevel);
    }
    return Math.pow(0.9, playerLevel - enemyLevel);
}
function getEnemyTypeScalingMultiplier(enemyType, playerLevel) {
    if (enemyType === "boss") {
        if (playerLevel < 5) {
            return 0.5;
        }
        if (playerLevel < 10) {
            return 0.6;
        }
        if (playerLevel < 15) {
            return 0.7;
        }
        return 0.8;
    }
    if (enemyType === "normal") {
        if (playerLevel < 5) {
            return 0.4;
        }
        if (playerLevel < 10) {
            return 0.45;
        }
        if (playerLevel < 15) {
            return 0.55;
        }
        return 0.65;
    }
    return 0.65;
}
const ApplyStatsToCreature = (npc, battleType, creatureLevel, currentUser, playerArmour, playerDamage, userStats) => {
    const creatureStats = {
        ...creatureBaseStats[battleType][npc.statType],
    };
    const healthIncrement = creatureBaseStats[battleType]["healthIncrement"];
    const uniqueCreature = uniqueCreatureStats[npc.name];
    creatureStats["health"] = Math.round(creatureStats["health"] + healthIncrement * (creatureLevel - 1));
    if (uniqueCreature) {
        creatureStats["strength"] = uniqueCreature.strength;
        creatureStats["defence"] = uniqueCreature.defence;
        creatureStats["weaponDamage"] = uniqueCreature.weaponDamage;
    }
    else {
        const zoneScalingMultiplier = getZoneScalingMultiplier(creatureLevel, currentUser.level);
        const enemyTypeScalingMultiplier = getEnemyTypeScalingMultiplier(npc.boss ? "boss" : "normal", currentUser.level);
        const creatureStrength = (userStats.strength + userStats.dexterity + userStats.endurance + userStats.intelligence) / 4;
        creatureStats["strength"] = Math.round(creatureStrength * enemyTypeScalingMultiplier * zoneScalingMultiplier);
        creatureStats["defence"] = Math.round(userStats.defence * enemyTypeScalingMultiplier * zoneScalingMultiplier);
        creatureStats["weaponDamage"] = Math.round(playerDamage * enemyTypeScalingMultiplier * zoneScalingMultiplier);
        const armour = Math.round(playerArmour * enemyTypeScalingMultiplier * zoneScalingMultiplier);
        creatureStats["defence"] += armour;
    }
    npc.currentHealth = creatureStats.health;
    npc.level = creatureLevel;
    npc.battleStatusEffects = {};
    return { ...npc, ...creatureStats };
};
const SelectEnemy = async (floor, location, battleType, user) => {
    const isBoss = battleType === "boss";
    const questEnemy = await CreatureRepository.findQuestEnemy(user.id, location, floor, isBoss);
    if (questEnemy) {
        const chanceToEncounterQuestEnemy = isBoss ? 1 : 0.4;
        if (Math.random() <= chanceToEncounterQuestEnemy) {
            return questEnemy;
        }
    }
    return await CreatureRepository.findRandomEnemy(floor, location, isBoss);
};
const ApplyLevelToCreature = (battleType, level) => {
    if (level === 1) {
        return 1;
    }
    if (battleType === "boss") {
        return level;
    }
    return Math.max(1, Math.floor(Math.random() * 3) + level - 1);
};
export const startRoguelikeNPCBattle = async (currentUser, battleType, location, level) => {
    const npc = await SelectEnemy(level, location, battleType, currentUser);
    if (!npc) {
        throw new Error("No enemy found for the specified criteria");
    }
    const creatureLevel = ApplyLevelToCreature(battleType, level);
    const userStats = await getAllUserStatLevels(currentUser.id);
    const playerAttackType = userStats.strength > userStats.dexterity ? "melee" : "ranged";
    const playerArmour = (await EquipmentService.GetTotalEquippedValue(currentUser, "armour")) || 1;
    const playerDamage = (await EquipmentService.GetTotalEquippedValue(currentUser, "damage", playerAttackType)) || 1;
    const npcDetails = ApplyStatsToCreature(npc, battleType, creatureLevel, currentUser, playerArmour, playerDamage, userStats);
    const result = await initiateBattle(currentUser, npcDetails, "pve");
    return result.error || null;
};
export default {
    startRoguelikeNPCBattle,
};
