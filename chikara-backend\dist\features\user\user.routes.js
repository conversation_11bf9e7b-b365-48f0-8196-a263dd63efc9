import * as UserController from "./user.controller.js";
import userSchema from "./user.validation.js";
import authHelper from "../../middleware/authMiddleware.js";
import validate from "../../middleware/validate.js";
import { parseMultipleImages } from "../../utils/images.js";
import routeHandler from "../../utils/routeHandler.js";
import express from "express";
import { checkMissionCompletion, checkDailyFatigueReset } from "../../middleware/backgroundTasksMiddleware.js";
const router = express.Router();
router.get("/current-user-info", authHelper.IsLoggedIn, checkDailyFatigueReset, checkMissionCompletion, routeHandler(async (req) => {
    return await UserController.getCurrentUserInfo(req.user.id);
}));
router.get("/user-info", authHelper.IsLoggedIn, validate(userSchema.userInfoSchema), routeHandler(async (req) => {
    const userId = Number.parseInt(String(req.query.id));
    return await UserController.getUserInfo(userId);
}));
router.get("/inventory", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await UserController.getInventory(req.user.id);
}));
router.get("/equipped-items", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await UserController.getEquippedItems(req.user.id);
}));
router.get("/tradeable-inventory", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await UserController.getTradeableInventory(req.user.id);
}));
router.get("/status-effects", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await UserController.getStatusEffects(req.user.id);
}));
router.get("/skills", authHelper.IsLoggedIn, routeHandler(async (req) => {
    const { skills } = req.query;
    let skillsArray = [];
    if (typeof skills === "string") {
        skillsArray = [skills];
    }
    else if (Array.isArray(skills)) {
        skillsArray = skills;
    }
    return await UserController.getAllUserSkills(req.user.id, skillsArray);
}));
router.get("/userlist", authHelper.IsLoggedIn, routeHandler(async () => {
    return await UserController.userList();
}));
const profileUpdateFields = [
    { name: "avatar", maxCount: 1 },
    { name: "banner", maxCount: 1 },
];
router.post("/update-profile-details", authHelper.IsLoggedIn, parseMultipleImages(profileUpdateFields), routeHandler(async (req) => {
    const files = req.files;
    return await UserController.UpdateProfileDetails(req.user.id, req.body.about, req.body.username, files);
}));
router.post("/equip", authHelper.IsLoggedInAndCanMakeStateChanges, validate(userSchema.equipItemSchema), routeHandler(async (req) => {
    return await UserController.equipItem(req.user.id, req.body.userItemId);
}));
router.post("/unequip", authHelper.IsLoggedInAndCanMakeStateChanges, validate(userSchema.unequipItemSchema), routeHandler(async (req) => {
    return await UserController.unequipItem(req.user.id, req.body.slot);
}));
router.post("/useItem", authHelper.IsLoggedInAndCanMakeStateChanges, validate(userSchema.useItemSchema), routeHandler(async (req) => {
    return await UserController.useItem(req.user.id, req.body.userItemId);
}));
router.get("/gameconfig", routeHandler(async () => {
    return await UserController.getGameConfig();
}));
router.post("/link-discord", authHelper.IsLoggedIn, validate(userSchema.linkDiscordSchema), routeHandler(async (req) => {
    return await UserController.linkDiscord(req.body.token, req.user.id);
}));
router.post("/set-news-id", authHelper.IsLoggedIn, validate(userSchema.setLastNewsIDReadSchema), routeHandler(async (req) => {
    return await UserController.setLastNewsIDRead(req.user.id, req.body.id);
}));
export default router;
