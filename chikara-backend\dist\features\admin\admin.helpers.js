import * as UserService from "../../core/user.service.js";
import * as adminRepository from "../../repositories/admin.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import * as imagesHelper from "../../utils/images.js";
import { logger } from "../../utils/log.js";
import * as UserRepository from "../../repositories/user.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";
export const getUsersInDateRange = async (type, startDate, endDate) => {
    if (!(startDate instanceof Date) || Number.isNaN(startDate.getTime())) {
        throw new TypeError("Invalid startDate");
    }
    if (!(endDate instanceof Date) || Number.isNaN(endDate.getTime())) {
        throw new TypeError("Invalid endDate");
    }
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);
    return await adminRepository.countUsersInDateRange(type, startDate, endDate);
};
export const logAdminAction = async (adminId, action, actionInfo = {}) => {
    logAction({
        action: "ADMIN_" + action,
        userId: adminId,
        logType: "admin",
        info: {
            ...actionInfo,
        },
    });
};
export const findAndValidateUser = async (userId) => {
    const user = await UserRepository.getUserById(userId);
    if (!user) {
        throw new Error("User not found");
    }
    return user;
};
export const getActiveUsersInTimeWindow = async (minutesAgo) => {
    const timeWindow = new Date(Date.now() - minutesAgo * 60 * 1000);
    return await adminRepository.countActiveUsersInTimeWindow(timeWindow);
};
export const getCirculatingYen = async (daysAgo) => {
    const timeWindow = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000);
    const { bankYen, cashYen } = await adminRepository.getCirculatingYenAmounts(timeWindow);
    return bankYen + cashYen;
};
export const findInactivePlayers = async (daysInactive, minLevel) => {
    const cutoffDate = new Date(Date.now() - daysInactive * 24 * 60 * 60 * 1000);
    return await adminRepository.findInactiveHighLevelPlayers(cutoffDate, minLevel);
};
export const isValidUserType = (type) => {
    return ["admin", "prefect", "student"].includes(type);
};
export const updateUserBan = async (user, adminId, banField, banDuration, banType) => {
    const banEndTime = Date.now() + banDuration;
    logger.info(`${banType} banning ${user.username} (${user.id}) until ${new Date(banEndTime)}`);
    await UserService.updateUser(user.id, {
        [banField]: BigInt(banEndTime),
    });
    await logAdminAction(adminId, `${banType} banned ${user.username} (${user.id}) until ${new Date(banEndTime)}`);
};
export const findItem = async (itemName, itemId) => {
    if (itemName) {
        return await ItemRepository.findItemByName(itemName);
    }
    if (itemId) {
        return await ItemRepository.findItemById(itemId);
    }
    return null;
};
export const reviveUser = async (user) => {
    await adminRepository.destroyUserDebuffs(user.id);
    await UserService.updateUser(user.id, {
        hospitalisedUntil: null,
        hospitalisedReason: null,
        hospitalisedHealingType: null,
        currentHealth: user.health,
    });
};
export const updateUserAccountDetails = async (user, updates, files) => {
    const updateData = {};
    if (updates.username && updates.username !== user.username) {
        const existingUser = await UserRepository.findUserByUsername(updates.username);
        if (existingUser) {
            throw new Error("Username is taken");
        }
        updateData.username = updates.username;
    }
    if (updates.email && updates.email !== user.email) {
        const existingUser = await adminRepository.findUserByEmail(updates.email);
        if (existingUser) {
            throw new Error("Email is taken");
        }
        updateData.email = updates.email;
    }
    if (files.avatar) {
        const avatar = files.avatar[0];
        if (avatar) {
            const savedAvatar = await imagesHelper.saveAsWebp(avatar, imagesHelper.UploadType.AVATAR, user.avatar || undefined);
            if (savedAvatar) {
                updateData.avatar = savedAvatar;
            }
        }
    }
    if (Object.keys(updateData).length > 0) {
        await UserService.updateUser(user.id, updateData);
    }
};
