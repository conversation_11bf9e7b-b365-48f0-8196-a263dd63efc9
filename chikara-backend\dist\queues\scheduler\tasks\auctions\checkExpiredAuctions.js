import * as InventoryService from "../../../../core/inventory.service.js";
import * as NotificationService from "../../../../core/notification.service.js";
import { logAction } from "../../../../lib/actionLogger.js";
import { db } from "../../../../lib/db.js";
import { NotificationTypes } from "../../../../types/notification.js";
import { getNow } from "../../../../utils/dateHelpers.js";
import { logger } from "../../../../utils/log.js";
export async function checkExpiredAuctions() {
    try {
        const now = getNow();
        const expiredItems = await db.auction_item.findMany({
            where: {
                endsAt: { lt: now },
                status: "in_progress",
            },
            include: {
                item: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
        });
        if (expiredItems.length > 0) {
            await db.auction_item.updateMany({
                where: {
                    id: {
                        in: expiredItems.map((item) => item.id),
                    },
                },
                data: {
                    status: "expired",
                },
            });
            for (const auctionItem of expiredItems) {
                const { sellerId, item } = auctionItem;
                if (sellerId == null || item == null) {
                    logger.warn(`Skipping expired auction ${auctionItem.id} due to missing ${sellerId == null ? "sellerId" : "item relation"}.`);
                    continue;
                }
                await InventoryService.AddItemToUser({
                    userId: sellerId,
                    itemId: item.id,
                    amount: auctionItem.quantity,
                    isTradeable: true,
                });
                await NotificationService.NotifyUser(sellerId, NotificationTypes.auction_item_expired, {
                    itemName: item.name,
                    quantity: auctionItem.quantity,
                });
                logAction({
                    action: "AUCTION_EXPIRED",
                    userId: sellerId,
                    info: {
                        auctionId: auctionItem.id,
                        itemId: item.id,
                        itemName: item.name,
                        quantity: auctionItem.quantity,
                    },
                });
            }
            logger.info(`Expired ${expiredItems.length} auctions.`);
        }
    }
    catch (error) {
        logger.error("Error checking for expired auctions: " + error);
    }
}
