import { logger, handleError, tryCatch } from "../../utils/log.js";
import { BASE_NODE_CONFIG, DISABLED_NODE_TYPES, GRID_CONFIG, LOCATION_NODE_COUNTS, LOCATION_NODE_WEIGHTS, PLAYER_NODE_CONFIG, STATIC_NODE_RESERVED_POSITIONS, TRAVEL_COSTS, TRAVEL_TIMES, } from "./explore.constants.js";
import * as ExploreRepository from "../../repositories/explore.repository.js";
import { handleBattleEncounter } from "./nodes/battle.node.js";
import { handleCharacterEncounter } from "./nodes/character_encounter.node.js";
import { handleForagingEncounter } from "./nodes/foraging.node.js";
import { handleMiningEncounter } from "./nodes/mining.node.js";
import { handleScavengingEncounter } from "./nodes/scavenging.node.js";
import { handleStoryNodeInteraction, createStoryNodesForUser } from "./nodes/story.node.js";
export const mapExploreLocationToLocationTypes = (location) => {
    switch (location) {
        case "shibuya": {
            return "alley";
        }
        case "shinjuku": {
            return "mall";
        }
        case "bunkyo": {
            return "school";
        }
        case "chiyoda": {
            return "shrine";
        }
        case "minato": {
            return "any";
        }
        default: {
            return "any";
        }
    }
};
const calculateManhattanDistance = (pos1, pos2) => {
    return Math.abs(pos1.x - pos2.x) + Math.abs(pos1.y - pos2.y);
};
const generateAllGridPositions = () => {
    const positions = [];
    for (let x = GRID_CONFIG.MIN_COORD; x <= GRID_CONFIG.MAX_COORD; x++) {
        for (let y = GRID_CONFIG.MIN_COORD; y <= GRID_CONFIG.MAX_COORD; y++) {
            positions.push({ x, y });
        }
    }
    return positions;
};
const isPositionReserved = (position, location) => {
    const reservedPositions = STATIC_NODE_RESERVED_POSITIONS[location];
    return reservedPositions.some((reserved) => reserved.x === position.x && reserved.y === position.y);
};
export const getStaticNodeReservedPositions = (location) => {
    return [...STATIC_NODE_RESERVED_POSITIONS[location]];
};
export const isValidGridPosition = (position) => {
    return (position.x >= GRID_CONFIG.MIN_COORD &&
        position.x <= GRID_CONFIG.MAX_COORD &&
        position.y >= GRID_CONFIG.MIN_COORD &&
        position.y <= GRID_CONFIG.MAX_COORD);
};
export const validateTravelParameters = (method, location) => {
    if (!(method in TRAVEL_COSTS)) {
        return handleError(`Invalid travel method: ${method}`, 400);
    }
    if (!(method in TRAVEL_TIMES)) {
        return handleError(`Travel method ${method} has no time configuration`, 400);
    }
    if (!(location in TRAVEL_COSTS[method])) {
        return handleError(`Location ${location} is not available for travel method ${method}`, 400);
    }
    if (!(location in TRAVEL_TIMES[method])) {
        return handleError(`Location ${location} has no travel time configured for method ${method}`, 400);
    }
    const cost = TRAVEL_COSTS[method][location];
    const travelTime = TRAVEL_TIMES[method][location];
    if (cost === undefined) {
        return handleError(`Travel cost is undefined for ${location} via ${method}`, 400);
    }
    if (travelTime === undefined) {
        return handleError(`Travel time is undefined for ${location} via ${method}`, 400);
    }
};
export const getNextStaticNodePosition = async (location) => {
    const reservedPositions = getStaticNodeReservedPositions(location);
    const existingStaticNodes = await ExploreRepository.getStaticNodesByLocation(location);
    const occupiedPositions = existingStaticNodes.map((node) => node.position);
    for (const reservedPos of reservedPositions) {
        const isOccupied = occupiedPositions.some((occupied) => occupied.x === reservedPos.x && occupied.y === reservedPos.y);
        if (!isOccupied) {
            return reservedPos;
        }
    }
    return null;
};
export const generateRandomPosition = async (userId, location) => {
    const [existingPlayerNodes, existingStaticNodes] = await Promise.all([
        ExploreRepository.getActivePlayerNodesByLocation(userId, location),
        ExploreRepository.getStaticNodesByLocation(location),
    ]);
    const existingPositions = [
        ...existingPlayerNodes.map((node) => node.position),
        ...existingStaticNodes.map((node) => node.position),
    ];
    const allPositions = generateAllGridPositions();
    const availablePositions = allPositions.filter((position) => {
        const isOccupied = existingPositions.some((existing) => existing.x === position.x && existing.y === position.y);
        const isReserved = isPositionReserved(position, location);
        return !isOccupied && !isReserved;
    });
    if (availablePositions.length === 0) {
        return null;
    }
    if (availablePositions.length === 1) {
        return availablePositions[0];
    }
    const positionsWithDistances = availablePositions.map((position) => {
        let minDistance = Infinity;
        for (const existingPos of existingPositions) {
            const distance = calculateManhattanDistance(position, existingPos);
            minDistance = Math.min(minDistance, distance);
        }
        return {
            position,
            minDistance: existingPositions.length === 0 ? Infinity : minDistance,
        };
    });
    const maxMinDistance = Math.max(...positionsWithDistances.map((p) => p.minDistance));
    const bestPositions = positionsWithDistances.filter((p) => p.minDistance === maxMinDistance).map((p) => p.position);
    const randomIndex = Math.floor(Math.random() * bestPositions.length);
    return bestPositions[randomIndex];
};
export const selectRandomNodeType = (location) => {
    const locationWeights = LOCATION_NODE_WEIGHTS[location];
    if (!locationWeights) {
        logger.warn(`No weights configured for location ${location}, using fallback`);
        return "CHARACTER_ENCOUNTER";
    }
    const enabledWeights = {};
    for (const [nodeType, weight] of Object.entries(locationWeights)) {
        if (!DISABLED_NODE_TYPES[nodeType]) {
            enabledWeights[nodeType] = weight;
        }
    }
    if (Object.keys(enabledWeights).length === 0) {
        logger.warn(`All node types are disabled for location ${location}, using CHARACTER_ENCOUNTER fallback`);
        return "CHARACTER_ENCOUNTER";
    }
    const totalWeight = Object.values(enabledWeights).reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;
    for (const [nodeType, weight] of Object.entries(enabledWeights)) {
        random -= weight;
        if (random <= 0) {
            return nodeType;
        }
    }
    return Object.keys(enabledWeights)[0];
};
export const generateNodeContent = (nodeType) => {
    const config = BASE_NODE_CONFIG[nodeType];
    const title = config.titles[Math.floor(Math.random() * config.titles.length)];
    const description = config.descriptions[Math.floor(Math.random() * config.descriptions.length)];
    let metadata = {};
    switch (nodeType) {
        case "BATTLE": {
            const isMiniboss = Math.random() < 0.05;
            metadata = {
                creatureLevel: Math.floor(Math.random() * 10) + 1,
                isMiniboss,
            };
            break;
        }
        case "CHARACTER_ENCOUNTER": {
            break;
        }
        case "MINING_NODE": {
            const miningDifficulties = ["easy", "medium", "hard"];
            const difficulty = miningDifficulties[Math.floor(Math.random() * miningDifficulties.length)];
            let estimatedReward;
            switch (difficulty) {
                case "easy": {
                    estimatedReward = "low";
                    break;
                }
                case "medium": {
                    estimatedReward = "medium";
                    break;
                }
                case "hard": {
                    estimatedReward = "high";
                    break;
                }
            }
            metadata = {
                difficulty,
                estimatedReward,
            };
            break;
        }
    }
    return { title, description, metadata };
};
export const generatePlayerNodesIfNeeded = async (userId, location) => {
    const targetNodeCount = LOCATION_NODE_COUNTS[location];
    if (!targetNodeCount) {
        logger.warn(`No node count configured for location ${location}`);
        return;
    }
    if (!hasEnabledNodeTypesForLocation(location)) {
        logger.warn(`All node types are disabled for location ${location}, skipping node generation`);
        return;
    }
    const currentNodeCount = await ExploreRepository.countPlayerNodesByLocation(userId, location);
    if (currentNodeCount < targetNodeCount) {
        const nodesToGenerate = targetNodeCount - currentNodeCount;
        logger.info(`Generating ${nodesToGenerate} new nodes for user ${userId} in ${location} (current: ${currentNodeCount}, target: ${targetNodeCount})`);
        for (let i = 0; i < nodesToGenerate; i++) {
            const nodeType = selectRandomNodeType(location);
            const { title, description, metadata } = generateNodeContent(nodeType);
            const position = await generateRandomPosition(userId, location);
            if (!position) {
                logger.warn(`No available grid positions for user ${userId} in ${location}, skipping node generation`);
                continue;
            }
            await ExploreRepository.createPlayerNode(userId, nodeType, title, description, position, location, PLAYER_NODE_CONFIG.DEFAULT_EXPIRATION_HOURS, metadata);
        }
    }
    await tryCatch(async () => {
        await createStoryNodesForUser(userId, location);
        logger.info(`Successfully processed story nodes for user ${userId} in ${location}`);
    });
};
export const handleNodeInteraction = async (nodeType, metadata, userId, nodeId, isStatic, location) => {
    logger.info(`User ${userId} interacting with ${nodeType} node ${nodeId} (static: ${isStatic}) at ${location}`);
    switch (nodeType) {
        case "BATTLE": {
            const battleResult = await handleBattleEncounter(userId, metadata, location);
            if (battleResult && "error" in battleResult) {
                return {
                    success: false,
                    message: battleResult.error,
                };
            }
            return {
                success: true,
                message: "Battle initiated!",
                data: {
                    action: "battle",
                    creatureLevel: metadata?.creatureLevel || 1,
                    isMiniboss: metadata?.isMiniboss || false,
                },
            };
        }
        case "CHARACTER_ENCOUNTER": {
            const encounterResult = await handleCharacterEncounter(userId, nodeId, location);
            if (!encounterResult.success) {
                return {
                    success: false,
                    message: encounterResult.message || "Character encounter failed",
                };
            }
            const encounterData = encounterResult.data;
            await ExploreRepository.updatePlayerNodeMetadata(nodeId, userId, {
                ...encounterData,
                action: undefined,
            });
            return {
                success: true,
                message: encounterResult.message || "Character encounter completed",
                data: encounterResult.data,
            };
        }
        case "ACTION": {
            return {
                success: true,
                message: "You discovered something interesting!",
                data: {
                    action: "discovery",
                    type: "action",
                },
            };
        }
        case "SCAVENGE_NODE": {
            const scavengeResult = await handleScavengingEncounter(userId, nodeId, location);
            if (!scavengeResult.success) {
                return {
                    success: false,
                    message: scavengeResult.message || "Scavenging failed",
                };
            }
            await ExploreRepository.updatePlayerNodeStatus(nodeId, userId, "current");
            return {
                success: true,
                message: scavengeResult.message || "Scavenging opportunity found",
                data: scavengeResult.data,
            };
        }
        case "MINING_NODE": {
            const miningResult = await handleMiningEncounter(userId, nodeId, location);
            if (!miningResult.success) {
                return {
                    success: false,
                    message: miningResult.message || "Mining failed",
                };
            }
            await ExploreRepository.updatePlayerNodeStatus(nodeId, userId, "current");
            return {
                success: true,
                message: miningResult.message || "Mining site prepared",
                data: miningResult.data,
            };
        }
        case "FORAGING_NODE": {
            const foragingResult = await handleForagingEncounter(userId, nodeId, location);
            if (!foragingResult.success) {
                return {
                    success: false,
                    message: foragingResult.message || "Foraging failed",
                };
            }
            await ExploreRepository.updatePlayerNodeStatus(nodeId, userId, "current");
            return {
                success: true,
                message: foragingResult.message || "Foraging area prepared",
                data: foragingResult.data,
            };
        }
        case "STORY": {
            const storyResult = await handleStoryNodeInteraction(userId, nodeId, location, metadata || {});
            if (!storyResult.success) {
                return {
                    success: false,
                    message: storyResult.message,
                };
            }
            await ExploreRepository.updatePlayerNodeStatus(nodeId, userId, "current");
            await ExploreRepository.updatePlayerNodeMetadata(nodeId, userId, {
                ...metadata,
                episodeData: storyResult.episodeData,
            });
            return {
                success: true,
                message: storyResult.message,
                data: {
                    action: "story",
                    redirectToStoryPlayer: storyResult.redirectToStoryPlayer,
                    episodeData: storyResult.episodeData,
                },
            };
        }
        default: {
            return {
                success: false,
                message: "Unknown node type.",
            };
        }
    }
};
export const isNodeTypeDisabled = (nodeType) => {
    return DISABLED_NODE_TYPES[nodeType] || false;
};
export const hasEnabledNodeTypesForLocation = (location) => {
    const locationWeights = LOCATION_NODE_WEIGHTS[location];
    if (!locationWeights)
        return false;
    return Object.keys(locationWeights).some((nodeType) => !DISABLED_NODE_TYPES[nodeType]);
};
