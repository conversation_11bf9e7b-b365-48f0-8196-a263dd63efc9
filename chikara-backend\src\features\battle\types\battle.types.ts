import { BATTLE_STATE } from "../helpers/battle.constants.js";
import { ItemModel } from "../../../lib/db.js";

export interface CombatLogEntry {
    id: number; // Sequential ID unique within the battle
    timestamp: number; // Timestamp of the action in milliseconds since epoch
    round: number; // The round number in which the action occurred
    actorId: string; // ID of the actor (the player or NPC performing the action)
    targetId: string; // ID of the target (the player or NPC being acted upon)
    action: string; // The action taken (e.g., "attack", "heal", "lifesteal")
    damage?: number; // Damage dealt (if applicable)
    healing?: number; // Healing done (if applicable)
    details?: {
        attackType?: string; // Type of attack (e.g., "melee", "ranged")
        statusEffects?: string[]; // List of active status effects on the target
    };
    remainingHealth?: {
        actor: number | null; // Remaining health of the actor after the action
        target: number | null; // Remaining health of the target after the action
    };
}

export type BattleType = "pve" | "pvp" | "pve-rooftop" | "pve-explore";

export interface EquippedAbility {
    name: string;
    staminaCost: number;
    currentModifier: number | null; // The active modifier based on user's talent level
    secondaryModifier: number | null;
}

export interface EquippedItem {
    id: number;
    name: string;
    itemType: string;
    rarity: string;
    level: number;
    upgradeLevel: number;
    cashValue: number | null;
    damage: number | null;
    armour: number | null;
    health: number | null;
    energy: number | null;
    actionPoints: number | null;
    baseAmmo: number | null;
    itemEffects: PrismaJson.ItemEffects | null;
}

// export interface AttackActionTypes {
//     attack: 'attack' | 'ranged';
// }

export interface PlayerEquipment {
    weapon: EquippedItem | null;
    ranged: EquippedItem | null;
    head: EquippedItem | null;
    chest: EquippedItem | null;
    hands: EquippedItem | null;
    legs: EquippedItem | null;
    feet: EquippedItem | null;
    finger: EquippedItem | null;
    offhand: EquippedItem | null;
    shield: EquippedItem | null;
}

interface StatusEffectData {
    turns?: number;
    value?: number;
    name?: string;
}
export type BattleStatusEffects = Record<string, StatusEffectData>;

export interface BattleBuffs {
    roguelikeStrBuff?: number;
    roguelikeDefBuff?: number;
    roguelikeDexBuff?: number;
}

export interface BattlePlayer {
    id: string;
    username: string;
    userType: "player" | "npc" | "rooftop_npc";
    avatar: string;
    level: number;
    currentHealth: number;
    maxHealth: number;
    currentStamina: number;
    maxStamina: number;
    attributes: {
        strength: number;
        defence: number;
        dexterity: number;
        intelligence: number;
        vitality: number;
        endurance: number;
    };
    ammo: number;
    damageTaken: number;
    currentTurn: number;
    weaponDamage?: number;
    isBoss: boolean;
    statusEffects: BattleStatusEffects;
    buffs?: BattleBuffs;
    abilities?: EquippedAbility[];
    equipment: PlayerEquipment | null;
}

export interface BattleState {
    id: string;
    state: (typeof BATTLE_STATE)[keyof typeof BATTLE_STATE];
    battleType: BattleType;
    startTime: number;
    validUntil: number;
    currentRound: number;
    aggressorId: string;
    firstAttackerId: string;
    combatLog: CombatLogEntry[];
    players: Record<string, BattlePlayer>;
}

export interface BattleSummary {
    id: string;
    battleType: BattleType;
    startTime: number;
    validUntil: number;
    currentRound: number;
    aggressorId: string;
    players: {
        id: string;
        username: string;
        currentHealth: number;
        maxHealth: number;
        userType: "player" | "npc" | "rooftop_npc";
    }[];
}

export interface NPCUser {
    id: number;
    name: string;
    image: string;
    battleStatusEffects: BattleStatusEffects;
    currentHealth: number;
    health: number;
    level: number;
    strength: number;
    defence: number;
    weaponDamage: number;
    boss?: boolean;
}

export interface RooftopNPCUser extends NPCUser {
    yenReward: number;
    itemRewardId: number;
    itemRewardQuantity: number;
    rank: string;
    combatLevel: number;
    initialDebuff?: BattleStatusEffects;
}

// Sanitized player data sent to frontend
export interface SanitizedBattlePlayer {
    id: string;
    username: string;
    userType: "player" | "npc" | "rooftop_npc";
    avatar: string;
    level: number;
    currentHealth: number;
    maxHealth: number;
    currentStamina: number;
    maxStamina: number;
    currentTurn: number;
    isBoss: boolean;
    statusEffects: BattleStatusEffects;
    equipment: PlayerEquipment | null;
}

export interface SanitizedBattleState {
    id: string;
    state: (typeof BATTLE_STATE)[keyof typeof BATTLE_STATE];
    battleType: BattleType;
    startTime: number;
    validUntil: number;
    currentRound: number;
    aggressorId: string;
    firstAttackerId: string;
    combatLog: CombatLogEntry[];
    players: Record<string, SanitizedBattlePlayer>;
}
