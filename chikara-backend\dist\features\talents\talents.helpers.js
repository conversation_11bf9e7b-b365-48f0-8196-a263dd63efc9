import { levelGatesConfig } from "../../config/gameConfig.js";
import * as UserService from "../../core/user.service.js";
import * as talentsRepository from "../../repositories/talents.repository.js";
import { TALENT_NAMES } from "./talents.types.js";
import * as TalentCache from "../../lib/cache/talentCache.js";
import { handleInternalError, LogErrorStack, logger } from "../../utils/log.js";
const { TALENTS_LEVEL_GATE } = levelGatesConfig.public;
export const GetAllTalents = async () => {
    return await TalentCache.getAllTalents();
};
export const GetTalentById = async (talentId) => {
    const talent = await TalentCache.getTalentById(talentId);
    if (!talent) {
        LogErrorStack({
            error: new Error(`Talent with ID ${talentId} does not exist.`),
        });
        return null;
    }
    return talent;
};
export const GetTalentByName = async (talentName) => {
    const talents = await TalentCache.getAllTalents();
    const talent = talents.find((tal) => tal.name === talentName);
    if (!talent) {
        LogErrorStack({
            error: new Error(`Talent with name ${talentName} does not exist.`),
        });
        return null;
    }
    return talent;
};
export const GetUserTalents = async (userId) => {
    const userTalents = {
        treePoints: {},
    };
    userTalents.talentList = await talentsRepository.findUserTalents(userId);
    const talents = await TalentCache.getAllTalents();
    const talentsMap = new Map(talents.map((talent) => [talent.id, talent]));
    for (const talent of userTalents.talentList) {
        const talentInfo = talentsMap.get(talent.talentId);
        if (!talentInfo) {
            LogErrorStack({
                error: new Error(`Talent with ID ${talent.talentId} not found in cache.`),
            });
            continue;
        }
        talent.talentInfo = talentInfo;
        const tree = talentInfo.tree;
        if (!userTalents.treePoints[tree]) {
            userTalents.treePoints[tree] = 0;
        }
        userTalents.treePoints[tree] += talent.level ?? 0;
    }
    return userTalents;
};
export const LevelUpTalent = async (userId, talentId) => {
    const existingTalent = await talentsRepository.findUserTalent(userId, talentId);
    if (existingTalent && existingTalent.level) {
        return await talentsRepository.updateUserTalentLevel(userId, talentId, existingTalent.level + 1);
    }
    return await talentsRepository.createUserTalent(userId, talentId);
};
export const GetAllTalentModifiers = async (talentName) => {
    const talent = await GetTalentByName(talentName);
    if (!talent) {
        return handleInternalError(`Talent does not exist: ${talentName}`);
    }
    const modifiers = [talent.tier1Modifier, talent.tier2Modifier, talent.tier3Modifier];
    return modifiers;
};
export const GetTalentSecondaryModifier = async (talentName) => {
    const talent = await GetTalentByName(talentName);
    if (!talent) {
        return handleInternalError(`Talent does not exist: ${talentName}`);
    }
    return talent.secondaryModifier;
};
export const GetTalentModifierByLevel = (talent, level) => {
    if (level === 1) {
        return talent.tier1Modifier;
    }
    if (level === 2) {
        return talent.tier2Modifier;
    }
    if (level === 3) {
        return talent.tier3Modifier;
    }
    LogErrorStack({ error: new Error("Invalid talent level: " + level) });
    return null;
};
export const UserHasTalent = async (userId, talentName) => {
    const talent = await GetTalentByName(talentName);
    if (!talent) {
        return null;
    }
    const userTalent = await talentsRepository.findUserTalent(userId, talent.id);
    if (!userTalent || userTalent.level === null) {
        return null;
    }
    const newUserTalent = userTalent;
    const talentModifier = GetTalentModifierByLevel(talent, userTalent.level);
    if (!talentModifier) {
        LogErrorStack({ error: new Error("Invalid talent modifier for talent: " + talent.name) });
        return null;
    }
    newUserTalent.modifier = talentModifier;
    newUserTalent.secondaryModifier = talent.secondaryModifier ?? undefined;
    return newUserTalent;
};
const createTalentCheckFunction = (talentName) => {
    return async (userId) => {
        return await UserHasTalent(userId, talentName);
    };
};
export const UserHasMeleeDamageIncreaseTalent = createTalentCheckFunction(TALENT_NAMES.MELEE_DAMAGE_INCREASE);
export const UserHasBerserkerTalent = createTalentCheckFunction(TALENT_NAMES.BERSERKER);
export const UserHasBullyTalent = createTalentCheckFunction(TALENT_NAMES.BULLY);
export const UserHasOffensiveOffhandsTalent = createTalentCheckFunction(TALENT_NAMES.OFFENSIVE_OFFHANDS);
export const UserHasCombatRegenerationTalent = createTalentCheckFunction(TALENT_NAMES.COMBAT_REGENERATION);
export const UserHasActiveDefenceTalent = createTalentCheckFunction(TALENT_NAMES.ACTIVE_DEFENCE);
export const UserHasStrongBonesTalent = createTalentCheckFunction(TALENT_NAMES.STRONG_BONES);
export const UserHasGoodStomachTalent = createTalentCheckFunction(TALENT_NAMES.GOOD_STOMACH);
export const UserHasShieldBearerTalent = createTalentCheckFunction(TALENT_NAMES.SHIELDBEARER);
export const UserHasMitigationTalent = createTalentCheckFunction(TALENT_NAMES.MITIGATION);
export const UserHasDeflectDamageTalent = createTalentCheckFunction(TALENT_NAMES.DEFLECT_DAMAGE);
export const UserHasHealthyCasterTalent = createTalentCheckFunction(TALENT_NAMES.HEALTHY_CASTER);
export const UserHasCunningRatTalent = createTalentCheckFunction(TALENT_NAMES.CUNNING_RAT);
export const UserHasSpeedCrafterTalent = createTalentCheckFunction(TALENT_NAMES.SPEED_CRAFTER);
export const UserHasMultiTaskerTalent = createTalentCheckFunction(TALENT_NAMES.MULTITASKER);
export const UserHasInvestorTalent = createTalentCheckFunction(TALENT_NAMES.INVESTOR);
export const UserHasLearnerTalent = createTalentCheckFunction(TALENT_NAMES.LEARNER);
export const UserHasReviveTalent = createTalentCheckFunction(TALENT_NAMES.REVIVE);
export const UserHasEnergeticTalent = createTalentCheckFunction(TALENT_NAMES.ENERGETIC);
export const UserHasBuiltTalent = createTalentCheckFunction(TALENT_NAMES.BUILT);
export const UserHasMuggerTalent = createTalentCheckFunction(TALENT_NAMES.MUGGER);
export const UserHasCowardTalent = createTalentCheckFunction(TALENT_NAMES.COWARD);
export const UserHasOutOfCombatRegenerationTalent = createTalentCheckFunction(TALENT_NAMES.OUTSIDE_COMBAT_REGENERATION);
export const UserHasRecoveryTalent = createTalentCheckFunction(TALENT_NAMES.RECOVERY);
export const UserHasAbilityEfficiencyTalent = createTalentCheckFunction(TALENT_NAMES.ABILITY_EFFICIENCY);
export const UserHasFreeMovementTalent = createTalentCheckFunction(TALENT_NAMES.FREE_MOVEMENT);
export const UserHasRejuvenationTalent = createTalentCheckFunction(TALENT_NAMES.REJUVENATION);
export const UserHasRangerTalent = createTalentCheckFunction(TALENT_NAMES.RANGER);
export const UserHasEscapeArtistTalent = createTalentCheckFunction(TALENT_NAMES.ESCAPE_ARTIST);
export const UserHasQuiverTalent = createTalentCheckFunction(TALENT_NAMES.QUIVER);
export const UserHasQuickTurnTakerTalent = createTalentCheckFunction(TALENT_NAMES.QUICK_TURN_TAKER);
export const UserHasShadowStepTalent = createTalentCheckFunction(TALENT_NAMES.SHADOW_STEP);
export const ResetTalents = async (userId, currentUser) => {
    try {
        await talentsRepository.resetUserEquippedAbilities(userId);
        const userTalents = await GetUserTalents(userId);
        const spentTalentPoints = Object.values(userTalents.treePoints).reduce((acc, points) => acc + points, 0);
        const baseTalentPoints = currentUser.level - TALENTS_LEVEL_GATE;
        const questRewardedTalentPoints = (currentUser.talentPoints || 0) + spentTalentPoints - baseTalentPoints;
        const updateValues = {};
        updateValues.talentPoints = baseTalentPoints + Math.max(0, questRewardedTalentPoints);
        const energeticTalent = await UserHasEnergeticTalent(userId);
        if (energeticTalent && typeof energeticTalent !== "boolean") {
            let decrement = 0;
            if (energeticTalent.level === 1) {
                decrement = 1;
            }
            if (energeticTalent.level === 2) {
                decrement = 3;
            }
            if (energeticTalent.level === 3) {
                decrement = 5;
            }
            updateValues.maxActionPoints = { decrement };
        }
        const builtTalent = await UserHasBuiltTalent(userId);
        if (builtTalent && typeof builtTalent !== "boolean" && builtTalent.modifier) {
            const decrement = builtTalent.modifier;
            updateValues.health = { decrement };
            updateValues.currentHealth = { decrement };
        }
        await talentsRepository.deleteAllUserTalents(userId);
        await UserService.updateUser(currentUser.id, updateValues);
        logger.debug(`Talents reset for user with ID: ${userId}`);
    }
    catch (error) {
        LogErrorStack({ message: "Failed to reset user talents", error });
    }
};
export const HealthyCasterTalentActiveForUser = async (user) => {
    const healthyCasterTalent = await UserHasHealthyCasterTalent(user.id);
    if (healthyCasterTalent && typeof healthyCasterTalent !== "boolean" && healthyCasterTalent.modifier) {
        const userPercentHpRemaining = (user.currentHealth / user.maxHealth) * 100;
        return userPercentHpRemaining >= healthyCasterTalent.modifier;
    }
    return false;
};
export const GetEquippedAbilities = async (user) => {
    try {
        const userEquipped = await talentsRepository.findUserEquippedAbilities(user.id);
        if (!userEquipped) {
            return [];
        }
        const equippedAbilities = [];
        if (userEquipped?.equippedAbility1Id) {
            equippedAbilities.push(userEquipped.equippedAbility1Id);
        }
        if (userEquipped?.equippedAbility2Id) {
            equippedAbilities.push(userEquipped.equippedAbility2Id);
        }
        if (userEquipped?.equippedAbility3Id) {
            equippedAbilities.push(userEquipped.equippedAbility3Id);
        }
        if (userEquipped?.equippedAbility4Id) {
            equippedAbilities.push(userEquipped.equippedAbility4Id);
        }
        if (equippedAbilities.length === 0) {
            return [];
        }
        const abilityPromises = equippedAbilities.map(async (abilityId) => {
            const talent = await GetTalentById(abilityId);
            if (!talent)
                return null;
            const userTalent = await talentsRepository.findUserTalent(user.id, abilityId);
            const userLevel = userTalent?.level || 1;
            const currentModifier = GetTalentModifierByLevel(talent, userLevel);
            return {
                name: talent.name,
                staminaCost: talent.staminaCost || 0,
                currentModifier,
                secondaryModifier: talent.secondaryModifier,
            };
        });
        const abilities = await Promise.all(abilityPromises);
        return abilities.filter((ability) => ability !== null);
    }
    catch (error) {
        LogErrorStack({ message: "Failed to fetch equipped abilities", error });
        return [];
    }
};
