import { handleError, LogErrorStack, logger } from "../../utils/log.js";
import * as StoryRepository from "../../repositories/story.repository.js";
import * as StoryHelpers from "./story.helpers.js";
import * as QuestService from "../../core/quest.service.js";
import * as StoryNodeHelpers from "../explore/nodes/story.node.js";
export const getSeasons = async (userId, userLevel, includeChapters = false, includeEpisodes = false) => {
    const seasons = await StoryRepository.findAllSeasons();
    const seasonsWithUnlock = [];
    for (const season of seasons) {
        const isUnlocked = StoryHelpers.checkSeasonUnlock(season, userLevel);
        const seasonWithUnlock = { ...season, isUnlocked };
        if (includeChapters) {
            seasonWithUnlock.chapters = await getChapters(userId, userLevel, season.id, includeEpisodes);
        }
        seasonsWithUnlock.push(seasonWithUnlock);
    }
    return seasonsWithUnlock;
};
export const getChapters = async (userId, userLevel, seasonId, includeEpisodes = false) => {
    const chapters = await StoryRepository.findChaptersBySeasonId(seasonId);
    const userProgress = null;
    const chaptersWithUnlock = [];
    for (const chapter of chapters) {
        const isUnlocked = StoryHelpers.checkChapterUnlock(chapter, userLevel, userProgress);
        const chapterWithUnlock = { ...chapter, isUnlocked };
        if (includeEpisodes) {
            chapterWithUnlock.episodes = await StoryRepository.findEpisodesByChapterId(chapter.id);
        }
        chaptersWithUnlock.push(chapterWithUnlock);
    }
    return chaptersWithUnlock;
};
export const completeEpisode = async (userId, request) => {
    const { episodeId, choices } = request;
    const episode = await StoryRepository.findEpisodeById(episodeId);
    if (!episode) {
        return handleError("Episode not found", 400);
    }
    if (choices && !StoryHelpers.validateEpisodeChoices(episode, choices)) {
        return handleError("Invalid episode choices", 400);
    }
    await QuestService.completeStoryQuestObjective(userId, episode.objectiveId, episodeId);
    if (choices) {
        for (const choiceValue of Object.values(choices)) {
            QuestService.handleStoryChoiceMade(userId, choiceValue);
        }
    }
    try {
        await StoryNodeHelpers.removeStoryNode(userId, episodeId, null);
        logger.info(`Successfully removed story node for episode ${episodeId} for user ${userId}`);
    }
    catch (error) {
        LogErrorStack({ message: `Failed to remove story node for episode ${episodeId}`, error });
    }
    return {
        data: "success",
    };
};
