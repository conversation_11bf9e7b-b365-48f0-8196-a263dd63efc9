import { APIROUTES } from "@/helpers/apiRoutes";
import { useQuery, type UseQueryOptions } from "@tanstack/react-query";
import type { UnlockedTalents } from "../types/talents";

const useGetUnlockedTalents = (options: Partial<UseQueryOptions<UnlockedTalents, Error, UnlockedTalents>> = {}) => {
    return useQuery<UnlockedTalents, Error, UnlockedTalents>({
        queryKey: APIROUTES.TALENTS.GETUNLOCKEDTALENTS,
        ...options,
    });
};

export default useGetUnlockedTalents;
