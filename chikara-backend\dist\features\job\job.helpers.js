import { LogErrorStack } from "../../utils/log.js";
import { evaluate } from "mathjs";
import { getAllUserStatLevels } from "../user/user.helpers.js";
function roundToMultipleOfFive(num) {
    return Math.round(num / 5) * 5;
}
export const PaymentForJobLevel = (job, level) => {
    const scope = { n: level };
    if (!job.payFormula) {
        LogErrorStack({ error: new Error(`Job ${job.id} has no pay formula defined`) });
        return 0;
    }
    return roundToMultipleOfFive(evaluate(job.payFormula, scope));
};
export const AttributeRequirementsForJobLevel = (job, level) => {
    const requirements = {
        strength: 0,
        intelligence: 0,
        dexterity: 0,
        defence: 0,
        endurance: 0,
        vitality: 0,
        payment: 0,
    };
    const scope = { n: level };
    if (job.strengthFormula) {
        requirements.strength = roundToMultipleOfFive(evaluate(job.strengthFormula, scope));
    }
    if (job.intelligenceFormula) {
        requirements.intelligence = roundToMultipleOfFive(evaluate(job.intelligenceFormula, scope));
    }
    if (job.dexterityFormula) {
        requirements.dexterity = roundToMultipleOfFive(evaluate(job.dexterityFormula, scope));
    }
    if (job.defenceFormula) {
        requirements.defence = roundToMultipleOfFive(evaluate(job.defenceFormula, scope));
    }
    if (job.enduranceFormula) {
        requirements.endurance = roundToMultipleOfFive(evaluate(job.enduranceFormula, scope));
    }
    requirements.payment = PaymentForJobLevel(job, level);
    return requirements;
};
export const UserQualifiesForJobLevel = async (user, job, level) => {
    try {
        const requirements = AttributeRequirementsForJobLevel(job, level);
        const userStats = await getAllUserStatLevels(user.id);
        return (userStats.strength >= requirements.strength &&
            userStats.intelligence >= requirements.intelligence &&
            userStats.dexterity >= requirements.dexterity &&
            userStats.defence >= requirements.defence &&
            userStats.endurance >= requirements.endurance &&
            userStats.vitality >= requirements.vitality);
    }
    catch (error) {
        LogErrorStack({ error });
        return false;
    }
};
