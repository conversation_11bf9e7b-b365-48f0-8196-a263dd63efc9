import * as EquipmentService from "../../../core/equipment.service.js";
import { creatureBaseStats } from "../../../data/creatureBaseStats.js";
import { initiateBattle } from "../../battle/logic/battle.initiation.js";
import * as CreatureRepository from "../../../repositories/creature.repository.js";
import { mapExploreLocationToLocationTypes } from "../explore.helpers.js";
import { getAllUserStatLevels } from "../../user/user.helpers.js";
import * as UserRepository from "../../../repositories/user.repository.js";
const uniqueCreatureStats = {
    "Practice Dummy": { strength: 1, defence: 3, weaponDamage: -6 },
    "Super Practice Dummy": { strength: 1, defence: 5, weaponDamage: -4 },
    "Mysterious Thief": { strength: 65, defence: 60, weaponDamage: 12 },
};
function getZoneScalingMultiplier(enemyLevel, playerLevel) {
    if (enemyLevel === playerLevel) {
        return 1;
    }
    else if (enemyLevel > playerLevel) {
        return Math.pow(1.2, enemyLevel - playerLevel);
    }
    return Math.pow(0.9, playerLevel - enemyLevel);
}
function getEnemyTypeScalingMultiplier(enemyType, playerLevel) {
    if (enemyType === "boss") {
        if (playerLevel < 5) {
            return 0.5;
        }
        if (playerLevel < 10) {
            return 0.6;
        }
        if (playerLevel < 15) {
            return 0.7;
        }
        return 0.8;
    }
    if (enemyType === "normal") {
        if (playerLevel < 5) {
            return 0.4;
        }
        if (playerLevel < 10) {
            return 0.45;
        }
        if (playerLevel < 15) {
            return 0.55;
        }
        return 0.65;
    }
    return 0.65;
}
const generateNPCStats = async (npc, battleType, creatureLevel, currentUser) => {
    const userStats = await getAllUserStatLevels(currentUser.id);
    const playerAttackType = userStats.strength > userStats.dexterity ? "melee" : "ranged";
    const playerArmour = (await EquipmentService.GetTotalEquippedValue(currentUser, "armour")) || 1;
    const playerDamage = (await EquipmentService.GetTotalEquippedValue(currentUser, "damage", playerAttackType)) || 1;
    const creatureStats = {
        ...creatureBaseStats[battleType][npc.statType],
    };
    const healthIncrement = creatureBaseStats[battleType]["healthIncrement"];
    const uniqueCreature = uniqueCreatureStats[npc.name];
    creatureStats["health"] = Math.round(creatureStats["health"] + healthIncrement * (creatureLevel - 1));
    if (uniqueCreature) {
        creatureStats["strength"] = uniqueCreature.strength;
        creatureStats["defence"] = uniqueCreature.defence;
        creatureStats["weaponDamage"] = uniqueCreature.weaponDamage;
    }
    else {
        const zoneScalingMultiplier = getZoneScalingMultiplier(creatureLevel, currentUser.level);
        const enemyTypeScalingMultiplier = getEnemyTypeScalingMultiplier(npc.boss ? "boss" : "normal", currentUser.level);
        const creatureStrength = (userStats.strength + userStats.dexterity + userStats.endurance + userStats.intelligence) / 4;
        creatureStats["strength"] = Math.round(creatureStrength * enemyTypeScalingMultiplier * zoneScalingMultiplier);
        creatureStats["defence"] = Math.round(userStats.defence * enemyTypeScalingMultiplier * zoneScalingMultiplier);
        creatureStats["weaponDamage"] = Math.round(playerDamage * enemyTypeScalingMultiplier * zoneScalingMultiplier);
        const armour = Math.round(playerArmour * enemyTypeScalingMultiplier * zoneScalingMultiplier);
        creatureStats["defence"] += armour;
    }
    npc.currentHealth = creatureStats.health;
    npc.level = creatureLevel;
    npc.battleStatusEffects = {};
    return { ...npc, ...creatureStats };
};
const SelectEnemy = async (floor, location, battleType, userId) => {
    const mappedLocation = mapExploreLocationToLocationTypes(location);
    const isBoss = battleType === "boss";
    const questEnemy = await CreatureRepository.findQuestEnemy(userId, mappedLocation, floor, isBoss);
    if (questEnemy) {
        const chanceToEncounterQuestEnemy = isBoss ? 1 : 0.4;
        if (Math.random() <= chanceToEncounterQuestEnemy) {
            return questEnemy;
        }
    }
    return await CreatureRepository.findRandomEnemy(floor, mappedLocation, isBoss);
};
export const startExploreBattle = async (currentUser, npcType, location, npcLevel) => {
    const npc = await SelectEnemy(npcLevel, location, npcType, currentUser.id);
    if (!npc) {
        return { error: "No enemy found" };
    }
    const npcDetails = await generateNPCStats(npc, npcType, npcLevel, currentUser);
    const result = await initiateBattle(currentUser, npcDetails, "pve-explore");
    if (result.error) {
        return result;
    }
    return { data: "battle_started" };
};
export const handleBattleEncounter = async (userId, npc, location) => {
    if (!npc) {
        return {
            error: "No enemy found",
        };
    }
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return {
            error: "User not found",
        };
    }
    const npcType = npc.isMiniboss ? "boss" : "normal";
    return await startExploreBattle(currentUser, npcType, location, npc.creatureLevel);
};
