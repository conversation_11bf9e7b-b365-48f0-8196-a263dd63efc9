import * as CreatureController from "./creature.controller.js";
import {
    createCreatureSchema,
    deleteCreatureSchema,
    updateCreatureSchema,
} from "./creature.validation.js";
import authHelper from "../../middleware/authMiddleware.js";
import validate from "../../middleware/validate.js";
import { routeHandler } from "../../utils/routeHandler.js";
import { Request, Router } from "express";

const router = Router();

// admin routes
router.get(
    "/list",
    authHelper.IsAdmin,
    routeHandler(async () => {
        return await CreatureController.creatureList();
    })
);

router.post(
    "/create",
    authHelper.IsAdmin,
    validate(createCreatureSchema),
    routeHandler(async (req: Request) => {
        return await CreatureController.createCreature(req.body);
    })
);

router.post(
    "/update",
    authHelper.IsAdmin,
    validate(updateCreatureSchema),
    routeHandler(async (req: Request) => {
        return await CreatureController.editCreature(req.body.id, req.body);
    })
);

router.post(
    "/delete",
    authHelper.IsAdmin,
    validate(deleteCreatureSchema),
    routeHandler(async (req: Request) => {
        return await CreatureController.deleteCreature(req.body.id);
    })
);

export default router;
