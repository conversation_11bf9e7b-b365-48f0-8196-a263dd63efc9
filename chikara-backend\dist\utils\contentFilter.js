const BANNED_WORDS = [
    "coon",
    "gook",
    "negro",
    "nig-nog",
    "nigga",
    "nigger",
    "nigguh",
    "n1gger",
    "paki",
    "tranny",
];
export const containsBlacklistedWords = (message) => {
    if (!message || typeof message !== "string") {
        return false;
    }
    const lowerCaseMessage = message.toLowerCase();
    return BANNED_WORDS.some((word) => lowerCaseMessage.includes(word.toLowerCase()));
};
export const getRejectedMessageError = () => {
    return "Your message contains inappropriate content";
};
export const getRejectedNameError = () => {
    return "Your name contains inappropriate content";
};
