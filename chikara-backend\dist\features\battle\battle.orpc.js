import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as BattleController from "./battle.controller.js";
import * as BattleResolver from "./logic/battle.resolver.js";
import battleSchema from "./battle.validation.js";
export const battleRouter = {
    getStatus: isLoggedInAuth.handler(async ({ context }) => {
        const response = await BattleController.getBattleStatus(context.user.id);
        return handleResponse(response);
    }),
    begin: canMakeStateChangesAuth.input(battleSchema.beginBattle).handler(async ({ input, context }) => {
        if (input.battleOpponentId === context.user.id) {
            return handleResponse({ error: "Stop hitting yourself", statusCode: 400 });
        }
        const response = await BattleController.initiatePVPBattle(context.user.id, input.battleOpponentId);
        return handleResponse(response);
    }),
    attack: isLoggedInAuth.input(battleSchema.attack).handler(async ({ input, context }) => {
        const response = await BattleController.processAttack(context.user.id, input.action);
        return handleResponse(response);
    }),
    rooftopList: isLoggedInAuth.handler(async ({ context }) => {
        const response = await BattleController.listRooftopBattles(context.user.id);
        return handleResponse(response);
    }),
    beginRooftopBattle: canMakeStateChangesAuth
        .input(battleSchema.beginRooftopBattle)
        .handler(async ({ input, context }) => {
        const response = await BattleController.initiateRooftopBattle(context.user.id, input.battleOpponentId);
        return handleResponse(response);
    }),
    postBattleAction: isLoggedInAuth.input(battleSchema.postBattleAction).handler(async ({ input, context }) => {
        const response = await BattleResolver.processVictoryAction(context.user.id, input.action);
        return handleResponse(response);
    }),
};
export default battleRouter;
