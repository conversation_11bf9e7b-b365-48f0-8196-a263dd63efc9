import { db } from "../lib/db.js";
import { Prisma } from "@prisma/client";
import * as UserRepository from "./user.repository.js";
export const countUsersInDateRange = async (field, startDate, endDate) => {
    return await db.user.count({
        where: {
            [field]: {
                gte: startDate,
                lte: endDate,
            },
        },
    });
};
export const countActiveUsersInTimeWindow = async (timeWindow) => {
    return await db.user.count({
        where: {
            last_activity: {
                gte: timeWindow,
            },
        },
    });
};
export const getCirculatingYenAmounts = async (timeWindow) => {
    const users = await db.user.findMany({
        where: {
            userType: {
                not: "admin",
            },
            last_activity: {
                gte: timeWindow,
            },
        },
        select: {
            bank_balance: true,
            cash: true,
        },
    });
    const totalYenInBanks = users.reduce((sum, user) => sum + (user.bank_balance || 0), 0);
    const totalYenInCash = users.reduce((sum, user) => sum + (user.cash || 0), 0);
    return { bankYen: totalYenInBanks, cashYen: totalYenInCash };
};
export const findInactiveHighLevelPlayers = async (cutoffDate, minLevel) => {
    const users = await db.user.findMany({
        where: {
            last_activity: {
                lte: cutoffDate,
            },
            level: {
                gte: minLevel,
            },
        },
    });
    return users;
};
export const destroyUserDebuffs = async (userId) => {
    await db.user_status_effect.deleteMany({ where: { userId, effect: { effectType: "DEBUFF" } } });
};
export const findUserByEmail = async (email) => {
    const user = await UserRepository.findUserByEmail(email);
    return user;
};
export const updateChatMessageVisibility = async (messageId, hidden) => {
    await db.chat_message.update({
        where: { id: messageId },
        data: { hidden },
    });
};
export const deleteChatMessage = async (messageId) => {
    await db.chat_message.delete({ where: { id: messageId } });
};
export const hideAllUserChatMessages = async (userId) => {
    await db.chat_message.updateMany({
        where: { userId },
        data: { hidden: true },
    });
};
export const getFullGangInfo = async (gangId) => {
    return await db.gang.findUnique({
        where: { id: gangId },
        include: {
            gang_member: {
                include: {
                    user: {
                        select: {
                            id: true,
                            username: true,
                            avatar: true,
                        },
                    },
                },
            },
            chat_room: {
                select: {
                    id: true,
                    name: true,
                },
            },
        },
    });
};
export const countTotalUsers = async () => {
    return await db.user.count();
};
export const createTestUser = async (userData) => {
    return await db.user.create({ data: userData });
};
export const getAllUsersWithAttributes = async (attributes) => {
    return await db.user.findMany({
        select: Object.fromEntries(attributes.map((attr) => [attr, true])),
    });
};
export const createAuctionItem = async (auctionData) => {
    return await db.auction_item.create({ data: auctionData });
};
export const resetAllUserRoguelikeMaps = async () => {
    await db.user.updateMany({
        data: {
            roguelikeMap: Prisma.DbNull,
            roguelikeLevel: 1,
        },
    });
};
export const findGangsWithMembers = async (gangId) => {
    return await db.gang.findMany({
        where: {
            id: gangId,
        },
        include: {
            gang_member: true,
        },
    });
};
export const incrementUserGangCredits = async (userId, amount) => {
    await db.user.update({
        where: { id: userId },
        data: {
            gangCreds: { increment: amount },
        },
    });
};
