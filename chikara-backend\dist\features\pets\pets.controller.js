import { addPetXp, calculateEnergyGain, calculateHappinessGain, calculateXpGain, canPetEvolve, updateEggProgress, verifyPetOwnership, } from "./pets.helpers.js";
import { evolveUserPet, getUserPetsWithPetData, setActivePet, updatePetBuffs, updatePetEnergyAndHappiness, updatePetHappiness, updatePetName, } from "../../repositories/pets.repository.js";
import { LogErrorStack } from "../../utils/log.js";
export async function getPets(userId) {
    try {
        const userPets = await getUserPetsWithPetData(userId);
        const updatedPets = await updateEggProgress(userPets);
        return updatedPets;
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
}
export async function feedPet(userId, userPetId, foodType, quantity) {
    try {
        const userPet = await verifyPetOwnership(userId, userPetId);
        const energyGain = calculateEnergyGain(foodType, quantity);
        const happinessGain = calculateHappinessGain("feed", quantity);
        const updatedUserPet = await updatePetEnergyAndHappiness(userPetId, userPet.energy + energyGain, userPet.happiness + happinessGain);
        return updatedUserPet;
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
}
export async function playPet(userId, userPetId, gameType, duration) {
    try {
        const userPet = await verifyPetOwnership(userId, userPetId);
        const happinessGain = calculateHappinessGain("play", duration);
        const xpGain = calculateXpGain("play", duration);
        await updatePetHappiness(userPetId, userPet.happiness + happinessGain);
        const updatedPet = await addPetXp(userPet, xpGain);
        return {
            pet: updatedPet,
            xpGained: xpGain,
        };
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
}
export async function trainPet(userId, userPetId, trainingType, duration) {
    try {
        const userPet = await verifyPetOwnership(userId, userPetId);
        const xpGain = calculateXpGain("train", duration);
        const skillGain = duration * 0.1;
        const updatedUserPet = await addPetXp(userPet, xpGain);
        return {
            pet: updatedUserPet,
            xpGained: xpGain,
            skillGained: skillGain,
        };
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
}
export async function customizePet(userId, userPetId, customization) {
    try {
        await verifyPetOwnership(userId, userPetId);
        let updatedUserPet;
        if (customization.name) {
            updatedUserPet = await updatePetName(userPetId, customization.name);
        }
        return updatedUserPet;
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
}
export async function evolvePet(userId, userPetId) {
    try {
        const userPet = await verifyPetOwnership(userId, userPetId);
        if (!userPet.pet) {
            return { error: "Base pet information not found" };
        }
        if (!canPetEvolve(userPet)) {
            return { error: "Pet does not meet evolution requirements." };
        }
        const evolution = userPet.evolution;
        const currentStageIndex = userPet.pet.evolution_stages.findIndex((stage) => stage.stage === evolution.current);
        const nextStage = userPet.pet.evolution_stages[currentStageIndex + 1];
        const nextNextStage = userPet.pet.evolution_stages[currentStageIndex + 2];
        if (!nextStage) {
            return { error: "No next stage found for evolution" };
        }
        const evolutionData = {
            current: nextStage.stage,
            next: nextNextStage?.stage || null,
            progress: 0,
            requiredLevel: evolution.requiredLevel ? evolution.requiredLevel + 10 : 10,
        };
        const updatedUserPet = await evolveUserPet(userPetId, evolutionData);
        return updatedUserPet;
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
}
export async function setActivePetStatus(userId, userPetId) {
    try {
        await verifyPetOwnership(userId, userPetId);
        const activeUserPet = await setActivePet(userId, userPetId);
        return activeUserPet;
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
}
export async function updatePetBuffsForPet(userId, userPetId, buffs) {
    try {
        const userPet = await verifyPetOwnership(userId, userPetId);
        if (!userPet.pet) {
            throw new Error("Base pet information not found");
        }
        const updatedPet = await updatePetBuffs(userPet.petId, buffs);
        return {
            ...userPet,
            pet: updatedPet,
        };
    }
    catch (error) {
        LogErrorStack({ error });
        throw error;
    }
}
export default {
    getPets,
    feedPet,
    playPet,
    trainPet,
    customizePet,
    evolvePet,
    setActivePetStatus,
    updatePetBuffsForPet,
};
