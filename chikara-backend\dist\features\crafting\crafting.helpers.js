import * as InventoryService from "../../core/inventory.service.js";
import * as CraftingRepository from "../../repositories/crafting.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";
export async function GetOutputs(recipeId) {
    const outputItems = await CraftingRepository.findRecipeItemsByRecipeId(recipeId, "output");
    return outputItems;
}
export async function GetInputs(recipeId) {
    const inputItems = await CraftingRepository.findRecipeItemsByRecipeId(recipeId, "input");
    return inputItems;
}
export async function ApplyCraftCompletion(userId, recipeId) {
    const outputItems = await GetOutputs(recipeId);
    for (const outputItem of outputItems) {
        await InventoryService.AddItemToUser({
            userId,
            itemId: outputItem.itemId,
            amount: outputItem.count ?? 1,
            isTradeable: true,
        });
    }
}
export async function ReturnCraftingItems(userId, recipeId) {
    const inputItems = await GetInputs(recipeId);
    for (const inputItem of inputItems) {
        await InventoryService.AddItemToUser({
            userId,
            itemId: inputItem.itemId,
            amount: inputItem.count ?? 1,
            isTradeable: false,
        });
    }
}
const inputItemIds = [215, 208, 216];
const itemQuantities = [2, 3, 3];
export const fetchItemsWithQuantities = async () => {
    const items = await ItemRepository.findItemsByIds(inputItemIds);
    return items.map((item) => ({
        ...item,
        quantity: itemQuantities[inputItemIds.indexOf(item.id)],
    }));
};
