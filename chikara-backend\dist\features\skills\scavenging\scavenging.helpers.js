import { POTENTIAL_RESOURCES } from "./scavenging.constants.js";
import { LogErrorStack } from "../../../utils/log.js";
import * as ItemRepository from "../../../repositories/item.repository.js";
export function generateId() {
    return Math.random().toString(36).slice(2, 11);
}
export function generateSessionId() {
    return Math.random().toString(36).slice(2, 18) + Date.now().toString(36);
}
export function generateCellHealth(difficultyTier) {
    const maxHealth = 4;
    switch (difficultyTier) {
        case 1: {
            return 1;
        }
        case 2: {
            return Math.random() < 0.8 ? 1 : 2;
        }
        case 3: {
            return Math.random() < 0.6 ? 1 : 2;
        }
        case 4: {
            return Math.random() < 0.4 ? 1 : 2;
        }
        case 5: {
            return Math.random() < 0.2 ? 1 : 2;
        }
        case 6: {
            return 2;
        }
        case 7: {
            return Math.random() < 0.8 ? 2 : 3;
        }
        case 8: {
            return Math.random() < 0.6 ? 2 : 3;
        }
        case 9: {
            return Math.random() < 0.4 ? 2 : 3;
        }
        case 10: {
            return Math.random() < 0.2 ? 2 : 3;
        }
        case 11: {
            return Math.random() < 0.8 ? 3 : 4;
        }
        case 12: {
            return Math.random() < 0.6 ? 3 : 4;
        }
        default: {
            if (difficultyTier > 12) {
                return maxHealth;
            }
            return 1;
        }
    }
}
export function createObfuscatedGrid(fullGrid, size) {
    const obfuscatedGrid = [];
    for (let row = 0; row < size; row++) {
        const gridRow = [];
        for (let col = 0; col < size; col++) {
            const cell = fullGrid[row][col];
            gridRow.push({
                id: cell.id,
                resourceType: cell.revealed ? cell.resourceType : "hidden",
                revealed: cell.revealed,
                row: cell.row,
                col: cell.col,
                resourceId: cell.revealed ? cell.resourceId : null,
                isPartOfMultiCell: cell.revealed ? cell.isPartOfMultiCell : false,
                health: cell.health,
                maxHealth: cell.maxHealth,
            });
        }
        obfuscatedGrid.push(gridRow);
    }
    return obfuscatedGrid;
}
export function createObfuscatedMultiCellResources(multiCellResources, fullGrid) {
    return multiCellResources
        .map((resource) => {
        const revealedCells = resource.cells.filter(({ row, col }) => fullGrid[row][col].revealed).length;
        const fullyRevealed = revealedCells === resource.cells.length;
        return {
            id: resource.id,
            type: fullyRevealed || revealedCells > 0 ? resource.type : "hidden",
            cells: resource.cells.map(({ row, col }) => ({
                row,
                col,
                revealed: fullGrid[row][col].revealed,
            })),
            shape: fullyRevealed ? resource.shape : { width: 0, height: 0 },
            fullyRevealed,
            revealedCells,
        };
    })
        .filter((resource) => resource.revealedCells > 0);
}
export function createObfuscatedResources(resources) {
    const discoveredResources = {};
    for (const [itemIdStr, count] of Object.entries(resources)) {
        if (count > 0) {
            const itemId = Number.parseInt(itemIdStr, 10);
            discoveredResources[itemId] = count;
        }
    }
    return discoveredResources;
}
export async function createEnhancedResources(resources) {
    const enhancedResources = {};
    const discoveredItemIds = [];
    for (const [itemIdStr, count] of Object.entries(resources)) {
        if (count > 0) {
            discoveredItemIds.push(Number.parseInt(itemIdStr, 10));
        }
    }
    if (discoveredItemIds.length === 0) {
        return enhancedResources;
    }
    try {
        const items = await ItemRepository.findItemsByIdsForScavenging(discoveredItemIds);
        for (const item of items) {
            const count = resources[item.id];
            if (count > 0) {
                enhancedResources[item.id] = {
                    count,
                    item: {
                        id: item.id,
                        name: item.name,
                        image: item.image,
                        itemType: item.itemType,
                        rarity: item.rarity,
                        cashValue: item.cashValue,
                    },
                };
            }
        }
        return enhancedResources;
    }
    catch (error) {
        LogErrorStack({ message: `Failed to fetch item details for scavenging resources: ${error}`, error });
        return {};
    }
}
export function canPlaceShape(grid, row, col, shape, size) {
    if (row + shape.height > size || col + shape.width > size) {
        return false;
    }
    for (let r = row; r < row + shape.height; r++) {
        for (let c = col; c < col + shape.width; c++) {
            if (grid[r][c].resourceType !== "empty") {
                return false;
            }
        }
    }
    return true;
}
export function placeResource(grid, row, col, resourceType, shape, resourceId) {
    const cells = [];
    for (let r = row; r < row + shape.height; r++) {
        for (let c = col; c < col + shape.width; c++) {
            grid[r][c].resourceType = resourceType;
            grid[r][c].resourceId = resourceId;
            grid[r][c].isPartOfMultiCell = shape.width > 1 || shape.height > 1;
            cells.push({ row: r, col: c });
        }
    }
    return {
        id: resourceId,
        type: resourceType,
        cells,
        shape,
        fullyRevealed: false,
        revealedCells: 0,
    };
}
export function generateRandomGrid(size = 5, minResources = 3, maxResources = 6, difficultyTier = 1) {
    const grid = [];
    for (let row = 0; row < size; row++) {
        const gridRow = [];
        for (let col = 0; col < size; col++) {
            const cellHealth = generateCellHealth(difficultyTier);
            gridRow.push({
                id: `${row}-${col}`,
                resourceType: "empty",
                revealed: false,
                row,
                col,
                resourceId: null,
                isPartOfMultiCell: false,
                health: cellHealth,
                maxHealth: cellHealth,
            });
        }
        grid.push(gridRow);
    }
    const multiCellResources = [];
    const resourceCounts = {};
    const placedResourceIds = new Set();
    for (const potentialResource of POTENTIAL_RESOURCES) {
        resourceCounts[potentialResource.itemId] = 0;
    }
    const minResourceCount = minResources;
    const maxResourceCount = maxResources;
    let placedResources = 0;
    let attempts = 0;
    const maxAttempts = minResourceCount * 20;
    while (placedResources < minResourceCount && attempts < maxAttempts) {
        const availableResources = POTENTIAL_RESOURCES.filter((resource) => !placedResourceIds.has(resource.itemId));
        if (availableResources.length === 0) {
            break;
        }
        const totalWeight = availableResources.reduce((sum, resource) => sum + resource.weight, 0);
        let random = Math.random() * totalWeight;
        let selectedResource = null;
        for (const potentialResource of availableResources) {
            random -= potentialResource.weight;
            if (random <= 0) {
                selectedResource = potentialResource;
                break;
            }
        }
        if (selectedResource) {
            const shape = { width: selectedResource.width, height: selectedResource.height };
            const row = Math.floor(Math.random() * size);
            const col = Math.floor(Math.random() * size);
            if (canPlaceShape(grid, row, col, shape, size)) {
                const resourceId = generateId();
                const resource = placeResource(grid, row, col, selectedResource.itemId, shape, resourceId);
                multiCellResources.push(resource);
                resourceCounts[selectedResource.itemId]++;
                placedResourceIds.add(selectedResource.itemId);
                placedResources++;
            }
        }
        attempts++;
    }
    attempts = 0;
    const maxAdditionalAttempts = (maxResourceCount - placedResources) * 50;
    while (placedResources < maxResourceCount && attempts < maxAdditionalAttempts) {
        const availableResources = POTENTIAL_RESOURCES.filter((resource) => !placedResourceIds.has(resource.itemId));
        if (availableResources.length === 0) {
            break;
        }
        const totalWeight = availableResources.reduce((sum, resource) => sum + resource.weight, 0);
        let random = Math.random() * totalWeight;
        let selectedResource = null;
        for (const potentialResource of availableResources) {
            random -= potentialResource.weight;
            if (random <= 0) {
                selectedResource = potentialResource;
                break;
            }
        }
        if (selectedResource) {
            let placed = false;
            let placementAttempts = 0;
            while (!placed && placementAttempts < 50) {
                const shape = { width: selectedResource.width, height: selectedResource.height };
                const row = Math.floor(Math.random() * size);
                const col = Math.floor(Math.random() * size);
                if (canPlaceShape(grid, row, col, shape, size)) {
                    const resourceId = generateId();
                    const resource = placeResource(grid, row, col, selectedResource.itemId, shape, resourceId);
                    multiCellResources.push(resource);
                    resourceCounts[selectedResource.itemId]++;
                    placedResourceIds.add(selectedResource.itemId);
                    placedResources++;
                    placed = true;
                }
                placementAttempts++;
            }
        }
        attempts++;
    }
    return { grid, multiCellResources, resourceCounts };
}
