import { logger, LogErrorStack } from "../../utils/log.js";
const eventHandlers = {};
export const registerEventHandler = (eventType, handler) => {
    if (!eventHandlers[eventType]) {
        eventHandlers[eventType] = [];
    }
    eventHandlers[eventType].push(handler);
    logger.debug(`Registered event handler for ${eventType}`);
};
export const unregisterEventHandler = (eventType, handler) => {
    const handlers = eventHandlers[eventType];
    if (handlers) {
        const index = handlers.indexOf(handler);
        if (index !== -1) {
            handlers.splice(index, 1);
            logger.debug(`Unregistered event handler for ${eventType}`);
        }
    }
};
export const dispatchEvent = async (eventType, payload) => {
    const handlers = eventHandlers[eventType];
    if (!handlers || handlers.length === 0) {
        logger.debug(`No handlers registered for event type: ${eventType}`);
        return;
    }
    logger.debug(`Dispatching event ${eventType} to ${handlers.length} handler(s)`);
    const promises = handlers.map(async (handler) => {
        try {
            await handler(payload);
        }
        catch (error) {
            LogErrorStack({ message: `Error in event handler for ${eventType}`, error });
        }
    });
    await Promise.allSettled(promises);
};
export const getHandlerCount = (eventType) => {
    return eventHandlers[eventType]?.length ?? 0;
};
export const clearEventHandlers = (eventType) => {
    delete eventHandlers[eventType];
    logger.debug(`Cleared all handlers for event type: ${eventType}`);
};
export const clearAllEventHandlers = () => {
    for (const eventType of Object.keys(eventHandlers)) {
        delete eventHandlers[eventType];
    }
    logger.debug("Cleared all event handlers");
};
