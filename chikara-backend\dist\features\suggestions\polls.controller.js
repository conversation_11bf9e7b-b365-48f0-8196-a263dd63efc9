import { createPollResponse as createPollResponseDB, findAllPolls, findAvailablePolls, findPollById, findPollResponses, findPollResponsesByUserId, findPreviousPollResponse, } from "../../repositories/polls.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { LogErrorStack } from "../../utils/log.js";
export const getPolls = async () => {
    const polls = await findAllPolls();
    return { data: polls };
};
export const getAvailablePolls = async (userId) => {
    try {
        const pollResponses = await findPollResponsesByUserId(userId);
        const completedPollIds = pollResponses.map((response) => response.pollId).filter(Boolean);
        const availablePolls = await findAvailablePolls(completedPollIds);
        return { data: availablePolls };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to fetch available polls:", error });
        return { error: "Failed to fetch available polls", statusCode: 500 };
    }
};
export const getPollResults = async (pollId, userType) => {
    try {
        if (!pollId) {
            return { error: "Missing poll id!", statusCode: 400 };
        }
        const poll = await findPollById(pollId);
        if (!poll) {
            return { error: "Poll not found!", statusCode: 400 };
        }
        if (!poll.showResults && userType !== "admin") {
            return { error: "Results not yet available!", statusCode: 400 };
        }
        const pollResponses = await findPollResponses(pollId);
        if (!pollResponses) {
            return { error: "Poll responses not found!", statusCode: 400 };
        }
        return { data: pollResponses };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to fetch poll results:", error });
        return { error: "Failed to fetch poll results", statusCode: 500 };
    }
};
export const createPollResponse = async (userId, pollId, pollResponse) => {
    try {
        if (!pollId) {
            return { error: "Missing poll id!", statusCode: 400 };
        }
        if (!pollResponse) {
            return { error: "Missing response!", statusCode: 400 };
        }
        const parsedUserId = userId;
        const parsedPollId = pollId;
        const poll = await findPollById(parsedPollId);
        if (!poll) {
            return { error: "Poll not found!", statusCode: 400 };
        }
        const previousResponse = await findPreviousPollResponse(parsedUserId, parsedPollId);
        if (previousResponse) {
            return { error: "Already responded to poll!", statusCode: 400 };
        }
        await createPollResponseDB(parsedUserId, parsedPollId, pollResponse);
        logAction({
            action: "POLl_RESPONSE",
            userId,
            info: {
                pollId,
            },
        });
        return { data: "Poll completed!" };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to create poll response:", error });
        return { error: "Failed to create poll response", statusCode: 400 };
    }
};
