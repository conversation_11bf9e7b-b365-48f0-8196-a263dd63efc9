import * as EquipmentService from "../../core/equipment.service.js";
import { creatureBaseStats } from "../../data/creatureBaseStats.js";
import { initiateBattle } from "../battle/logic/battle.initiation.js";
import { NPCUser } from "../battle/types/battle.types.js";
import * as CreatureRepository from "../../repositories/creature.repository.js";
import type { CreatureModel, ExtUserModel } from "../../lib/db.js";
import { LocationTypes } from "@prisma/client";
import { getAllUserStatLevels } from "../user/user.helpers.js";

type BattleType = "normal" | "boss";

const uniqueCreatureStats = {
    "Practice Dummy": { strength: 1, defence: 3, weaponDamage: -6 },
    "Super Practice Dummy": { strength: 1, defence: 5, weaponDamage: -4 },
    "Mysterious Thief": { strength: 65, defence: 60, weaponDamage: 12 },
};

function getZoneScalingMultiplier(enemyLevel: number, playerLevel: number) {
    if (enemyLevel === playerLevel) {
        return 1;
    } else if (enemyLevel > playerLevel) {
        return Math.pow(1.2, enemyLevel - playerLevel);
    }
    return Math.pow(0.9, playerLevel - enemyLevel);
}

function getEnemyTypeScalingMultiplier(enemyType: BattleType, playerLevel: number) {
    if (enemyType === "boss") {
        if (playerLevel < 5) {
            return 0.5;
        }
        if (playerLevel < 10) {
            return 0.6;
        }
        if (playerLevel < 15) {
            return 0.7;
        }
        return 0.8;
    }
    if (enemyType === "normal") {
        if (playerLevel < 5) {
            return 0.4;
        }
        if (playerLevel < 10) {
            return 0.45;
        }
        if (playerLevel < 15) {
            return 0.55;
        }
        return 0.65;
    }
    return 0.65;
}

interface CreatureNPC extends CreatureModel {
    level?: number;
    battleStatusEffects?: Record<string, number>;
}

const ApplyStatsToCreature = (
    npc: CreatureNPC,
    battleType: BattleType,
    creatureLevel: number,
    currentUser: ExtUserModel,
    playerArmour: number,
    playerDamage: number,
    userStats: { strength: number; intelligence: number; dexterity: number; defence: number; endurance: number }
) => {
    const creatureStats: { health: number; strength: number; defence: number; weaponDamage: number } = {
        ...creatureBaseStats[battleType][npc.statType],
    };
    const healthIncrement = creatureBaseStats[battleType]["healthIncrement"];
    const uniqueCreature = uniqueCreatureStats[npc.name as keyof typeof uniqueCreatureStats];

    creatureStats["health"] = Math.round(creatureStats["health"] + healthIncrement * (creatureLevel - 1));

    if (uniqueCreature) {
        creatureStats["strength"] = uniqueCreature.strength;
        creatureStats["defence"] = uniqueCreature.defence;
        creatureStats["weaponDamage"] = uniqueCreature.weaponDamage;
    } else {
        const zoneScalingMultiplier = getZoneScalingMultiplier(creatureLevel, currentUser.level);
        const enemyTypeScalingMultiplier = getEnemyTypeScalingMultiplier(
            npc.boss ? "boss" : "normal",
            currentUser.level
        );
        const creatureStrength =
            (userStats.strength + userStats.dexterity + userStats.endurance + userStats.intelligence) / 4;
        creatureStats["strength"] = Math.round(creatureStrength * enemyTypeScalingMultiplier * zoneScalingMultiplier);
        creatureStats["defence"] = Math.round(userStats.defence * enemyTypeScalingMultiplier * zoneScalingMultiplier);
        creatureStats["weaponDamage"] = Math.round(playerDamage * enemyTypeScalingMultiplier * zoneScalingMultiplier);
        const armour = Math.round(playerArmour * enemyTypeScalingMultiplier * zoneScalingMultiplier);
        creatureStats["defence"] += armour;
    }

    npc.currentHealth = creatureStats.health;
    npc.level = creatureLevel;
    npc.battleStatusEffects = {};

    return { ...npc, ...creatureStats };
};

const SelectEnemy = async (floor: number, location: LocationTypes, battleType: BattleType, user: ExtUserModel) => {
    const isBoss = battleType === "boss";
    const questEnemy = await CreatureRepository.findQuestEnemy(user.id, location, floor, isBoss);

    if (questEnemy) {
        const chanceToEncounterQuestEnemy = isBoss ? 1 : 0.4; // 100% for quest bosses, 40% for normal quest enemies
        if (Math.random() <= chanceToEncounterQuestEnemy) {
            return questEnemy;
        }
    }

    return await CreatureRepository.findRandomEnemy(floor, location, isBoss);
};

const ApplyLevelToCreature = (battleType: BattleType, level: number) => {
    if (level === 1) {
        return 1;
    }
    if (battleType === "boss") {
        return level;
    }
    return Math.max(1, Math.floor(Math.random() * 3) + level - 1); // Slightly random normal creature levels
};

export const startRoguelikeNPCBattle = async (
    currentUser: ExtUserModel,
    battleType: BattleType,
    location: LocationTypes,
    level: number
) => {
    const npc = await SelectEnemy(level, location, battleType, currentUser);

    if (!npc) {
        throw new Error("No enemy found for the specified criteria");
    }

    const creatureLevel = ApplyLevelToCreature(battleType, level);
    const userStats = await getAllUserStatLevels(currentUser.id);
    const playerAttackType = userStats.strength > userStats.dexterity ? "melee" : "ranged";
    const playerArmour = (await EquipmentService.GetTotalEquippedValue(currentUser, "armour")) || 1;
    const playerDamage = (await EquipmentService.GetTotalEquippedValue(currentUser, "damage", playerAttackType)) || 1;
    const npcDetails = ApplyStatsToCreature(
        npc,
        battleType,
        creatureLevel,
        currentUser,
        playerArmour,
        playerDamage,
        userStats
    );

    const result = await initiateBattle(currentUser, npcDetails as NPCUser, "pve");

    return result.error || null;
};

export default {
    startRoguelikeNPCBattle,
};
