import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from "../../../../features/chat/chat.helpers.js";
import { db } from "../../../../lib/db.js";
import { LogErrorStack } from "../../../../utils/log.js";
async function announceShopOpen() {
    await ChatHelper.SendAnnouncementMessage("shopNowOpen", "The Sunday shop is now open for trading!");
}
export async function openSundayShop() {
    try {
        await db.shop.update({
            where: { id: 6 },
            data: { disabled: false },
        });
        await announceShopOpen();
    }
    catch (error) {
        LogErrorStack({ message: "Failed to open Sunday shop:", error });
    }
}
export default openSundayShop;
