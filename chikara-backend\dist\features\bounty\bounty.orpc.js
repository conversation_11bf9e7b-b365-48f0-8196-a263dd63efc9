import { adminAuth, canMakeStateChangesAuth, isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as BountyController from "./bounty.controller.js";
import { deleteBountySchema, placeBountySchema } from "./bounty.validation.js";
export const bountyRouter = {
    getActiveBountyList: isLoggedInAuth.handler(async () => {
        const response = await BountyController.activeBountyList();
        return handleResponse(response);
    }),
    placeBounty: canMakeStateChangesAuth.input(placeBountySchema).handler(async ({ input, context }) => {
        const response = await BountyController.placeBounty({
            userId: context.user.id,
            bountyAmount: input.amount,
            targetId: input.targetId,
            reason: input.reason,
        });
        return handleResponse(response);
    }),
    getBountyList: adminAuth.handler(async () => {
        const response = await BountyController.bountyList();
        return handleResponse(response);
    }),
    deleteBounty: adminAuth.input(deleteBountySchema).handler(async ({ input }) => {
        const response = await BountyController.deleteBounty({
            id: input.id,
        });
        return handleResponse(response);
    }),
};
export default bountyRouter;
