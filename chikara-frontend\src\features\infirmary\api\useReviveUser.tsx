import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

export interface ReviveUserRequest {
    targetId: number;
}

/**
 * Hook to revive another user (requires revive talent)
 */
export const useReviveUser = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.infirmary.revivePlayer.mutationOptions({
            onSuccess: () => {
                toast.success("Revived user successfully!");
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.INFIRMARY.INFIRMARYLIST,
                });
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.INFIRMARY.INJUREDLIST,
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );
};

export default useReviveUser;
