import { BATTLE_STATE } from "../helpers/battle.constants.js";
import * as BattleHelpers from "../helpers/battle.helpers.js";
import { ProcessAction } from "../helpers/battle.action.js";
import { addCombatLogEntry, logActionResult } from "../helpers/battle.logging.js";
import { LogErrorStack, logger } from "../../../utils/log.js";
import * as BattleResolver from "../logic/battle.resolver.js";
import BattleAI from "../ai/battle.ai.js";
import * as TalentHelper from "../../talents/talents.helpers.js";
import { battleConfig } from "../../../config/gameConfig.js";
import { getStaminaRegenModifier } from "../helpers/battle.scaling.js";
import * as UserRepository from "../../../repositories/user.repository.js";
const { BASE_STAMINA_REGEN } = battleConfig.hidden;
const executeActorTurn = async (actor, target, attackerState, targetState, playerAction) => {
    const isHuman = actor.id === attackerState.id && attackerState.userType === "player";
    logger.debug(`Choosing action for ${actor.username} (${actor.userType}), isHuman: ${isHuman}`);
    const chosenAction = isHuman && playerAction
        ? playerAction
        : await (async () => {
            const aiAction = await BattleAI.chooseAction(actor, target);
            logger.debug(`AI chose action ${aiAction} for ${actor.userType} ${actor.username}`);
            return aiAction;
        })();
    logger.debug(`Processing action ${chosenAction} for ${actor.username}`);
    let rawResult;
    try {
        rawResult = await ProcessAction(actor, target, chosenAction);
        logger.debug(`Action ${chosenAction} completed for ${actor.username}`);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.debug(`Action ${chosenAction} failed for ${actor.userType} ${actor.username}: ${errorMessage}`);
        rawResult = 0;
    }
    const damage = Math.round(typeof rawResult === "number" ? rawResult : rawResult.damage);
    const fleeAttempt = typeof rawResult === "number" ? undefined : rawResult.metadata?.fleeAttempt;
    let normalizedActionType = chosenAction;
    if (chosenAction === "flee" && fleeAttempt !== undefined) {
        normalizedActionType = fleeAttempt.success ? "flee_success" : "flee_failed";
    }
    const stateRef = actor.id === attackerState.id ? attackerState : targetState;
    const lifeSteal = BattleHelpers.ApplyLifeSteal(stateRef, chosenAction, damage);
    const bleedAmount = BattleHelpers.ApplyBleed(stateRef);
    return { actionType: normalizedActionType, damage, lifeSteal, bleedAmount, fleeAttempt };
};
const processPlayerStaminaRegeneration = async (player) => {
    logger.debug(`Processing stamina regeneration for player: ${player.username} (${player.userType})`);
    const recoveryTalent = player.userType === "player" ? await TalentHelper.UserHasRecoveryTalent(player.id) : null;
    let baseStaminaRegen = BASE_STAMINA_REGEN;
    if (player.userType === "player") {
        const intelligenceModifier = getStaminaRegenModifier(player.attributes.intelligence);
        baseStaminaRegen = Math.round(BASE_STAMINA_REGEN * intelligenceModifier);
    }
    let staminaRegenAmount = recoveryTalent ? baseStaminaRegen * (recoveryTalent.modifier || 1) : baseStaminaRegen;
    if (player.statusEffects["stamina_regen_debuff"]) {
        staminaRegenAmount = 0;
    }
    player.currentStamina = Math.min(player.currentStamina + staminaRegenAmount, player.maxStamina);
};
const applyCombatRegeneration = async (attackerState, targetState, victory, enemyVictory) => {
    logger.debug(`Applying combat regeneration - victory: ${victory}, enemyVictory: ${enemyVictory}`);
    const battleOver = victory || enemyVictory;
    const attackerCombatRegen = attackerState.userType === "player"
        ? await TalentHelper.UserHasCombatRegenerationTalent(attackerState.id)
        : null;
    const targetCombatRegen = targetState.userType === "player" ? await TalentHelper.UserHasCombatRegenerationTalent(targetState.id) : null;
    if (!attackerCombatRegen && !targetCombatRegen) {
        return;
    }
    if (battleOver) {
        if (victory && attackerCombatRegen) {
            BattleHelpers.ApplyCombatRegen(attackerState, attackerCombatRegen.modifier);
        }
        if (enemyVictory && targetCombatRegen) {
            BattleHelpers.ApplyCombatRegen(targetState, targetCombatRegen.modifier);
        }
    }
    else {
        if (attackerCombatRegen) {
            BattleHelpers.ApplyCombatRegen(attackerState, attackerCombatRegen.modifier);
        }
        if (targetCombatRegen) {
            BattleHelpers.ApplyCombatRegen(targetState, targetCombatRegen.modifier);
        }
    }
};
export const processBattleRound = async (battleState, attackerState, targetState, action) => {
    logger.debug(`processBattleRound started for battle: ${battleState.id}`);
    const firstActor = battleState.firstAttackerId === attackerState.id ? attackerState : targetState;
    const secondActor = battleState.firstAttackerId === attackerState.id ? targetState : attackerState;
    logger.debug(`Executing first actor turn: ${firstActor.username} (${firstActor.userType})`);
    const firstTurnResult = await executeActorTurn(firstActor, secondActor, attackerState, targetState, action);
    if (firstTurnResult.fleeAttempt && firstTurnResult.fleeAttempt.success) {
        if (firstActor.userType === "player") {
            const currentUser = await UserRepository.getUserById(Number(firstActor.id));
            if (currentUser) {
                await BattleResolver.handleSuccessfulFlee(battleState, firstActor.id, firstActor, secondActor, currentUser);
            }
            else {
                battleState.state = BATTLE_STATE.FINISHED;
            }
        }
        else {
            battleState.state = BATTLE_STATE.FINISHED;
        }
        const fleeingAction = { damage: 0, type: "flee_success", lifeSteal: 0, bleedAmount: 0 };
        const emptyAction = { damage: 0, type: "", lifeSteal: 0, bleedAmount: 0 };
        logActionResult(battleState, firstActor, secondActor, {
            actionType: firstTurnResult.actionType,
            damage: firstTurnResult.damage,
            lifeSteal: firstTurnResult.lifeSteal,
            bleedAmount: firstTurnResult.bleedAmount,
        });
        const isPlayerFlee = firstActor.id === attackerState.id;
        return {
            battleState,
            attackedFirst: battleState.firstAttackerId,
            playerAction: isPlayerFlee ? fleeingAction : emptyAction,
            enemyAction: isPlayerFlee ? emptyAction : fleeingAction,
            flee: "success",
        };
    }
    if (!firstTurnResult.fleeAttempt?.success) {
        secondActor.currentHealth = Math.max(0, secondActor.currentHealth - firstTurnResult.damage);
    }
    const secondActorDefeated = secondActor.currentHealth <= 0;
    const battleResult = { victory: false, enemyVictory: false };
    let secondTurnResult = {
        actionType: "",
        damage: 0,
        lifeSteal: 0,
        bleedAmount: 0,
        fleeAttempt: undefined,
    };
    if (secondActorDefeated) {
        battleResult.victory = true;
    }
    else {
        logger.debug(`Executing second actor turn: ${secondActor.username} (${secondActor.userType})`);
        secondTurnResult = await executeActorTurn(secondActor, firstActor, attackerState, targetState, action);
        if (secondTurnResult.fleeAttempt && secondTurnResult.fleeAttempt.success) {
            if (secondActor.userType === "player") {
                const currentUser = await UserRepository.getUserById(Number(secondActor.id));
                if (currentUser) {
                    await BattleResolver.handleSuccessfulFlee(battleState, secondActor.id, secondActor, firstActor, currentUser);
                }
                else {
                    battleState.state = BATTLE_STATE.FINISHED;
                }
            }
            else {
                battleState.state = BATTLE_STATE.FINISHED;
            }
            const fleeingAction = { damage: 0, type: "flee_success", lifeSteal: 0, bleedAmount: 0 };
            const firstActorAction = {
                damage: firstTurnResult.damage,
                type: firstTurnResult.actionType,
                lifeSteal: firstTurnResult.lifeSteal,
                bleedAmount: firstTurnResult.bleedAmount,
            };
            logActionResult(battleState, firstActor, secondActor, {
                actionType: firstTurnResult.actionType,
                damage: firstTurnResult.damage,
                lifeSteal: firstTurnResult.lifeSteal,
                bleedAmount: firstTurnResult.bleedAmount,
            });
            logActionResult(battleState, secondActor, firstActor, {
                actionType: secondTurnResult.actionType,
                damage: secondTurnResult.damage,
                lifeSteal: secondTurnResult.lifeSteal,
                bleedAmount: secondTurnResult.bleedAmount,
            });
            const playerFled = secondActor.id === attackerState.id;
            return {
                battleState,
                attackedFirst: battleState.firstAttackerId,
                playerAction: playerFled ? fleeingAction : firstActorAction,
                enemyAction: playerFled ? firstActorAction : fleeingAction,
                flee: "success",
            };
        }
        if (!secondTurnResult.fleeAttempt?.success) {
            firstActor.currentHealth = Math.max(0, firstActor.currentHealth - secondTurnResult.damage);
        }
        const firstActorDefeated = firstActor.currentHealth <= 0;
        if (firstActorDefeated) {
            if (secondActor.id === attackerState.id) {
                battleResult.victory = true;
            }
            else {
                battleResult.enemyVictory = true;
            }
        }
        else {
            battleResult.victory = targetState.currentHealth <= 0;
            battleResult.enemyVictory = attackerState.currentHealth <= 0;
        }
    }
    logger.debug(`Processing stamina regeneration for battle: ${battleState.id}`);
    await processPlayerStaminaRegeneration(attackerState);
    await processPlayerStaminaRegeneration(targetState);
    logger.debug(`Updating status effects for battle: ${battleState.id}`);
    BattleHelpers.SubtractTurnFromStatusEffects(attackerState);
    BattleHelpers.SubtractTurnFromStatusEffects(targetState);
    if (targetState.userType !== "player") {
        attackerState.damageTaken +=
            battleState.firstAttackerId === attackerState.id ? secondTurnResult.damage : firstTurnResult.damage;
    }
    const rewardInfo = {};
    if (battleResult.enemyVictory) {
        logger.debug(`Enemy victory - handling player defeat for ${attackerState.username}`);
        const currentUser = await UserRepository.getUserById(Number.parseInt(attackerState.id));
        if (!currentUser) {
            LogErrorStack({
                message: `User not found for attackerState: ${attackerState.id}`,
                error: new Error(`User not found for attackerState: ${attackerState.id}`),
            });
            return { error: "User not found" };
        }
        await BattleResolver.handlePlayerDefeat(battleState, attackerState, targetState, currentUser, "normal_loss", false);
        logger.debug(`Player defeat handled for ${attackerState.username}`);
    }
    if (battleResult.victory) {
        logger.debug(`Player victory - handling NPC defeat for ${targetState.username}`);
        battleState.state = BATTLE_STATE.FINISHED;
        if (battleState.battleType === "pve" ||
            battleState.battleType === "pve-rooftop" ||
            battleState.battleType === "pve-explore") {
            await BattleResolver.handleVictoryNPC(battleState, attackerState, targetState, rewardInfo);
        }
        logger.debug(`NPC defeat handled for ${targetState.username}`);
    }
    logActionResult(battleState, firstActor, secondActor, firstTurnResult);
    if (!secondActorDefeated) {
        logActionResult(battleState, secondActor, firstActor, secondTurnResult);
    }
    await applyCombatRegeneration(attackerState, targetState, battleResult.victory, battleResult.enemyVictory);
    battleState.currentRound++;
    if (battleResult.victory) {
        addCombatLogEntry("battle_win", battleState, attackerState.id, targetState.id);
    }
    if (battleResult.enemyVictory) {
        addCombatLogEntry("battle_win", battleState, targetState.id, attackerState.id);
    }
    const playerFailedFlee = (firstTurnResult.fleeAttempt && !firstTurnResult.fleeAttempt.success) ||
        (secondTurnResult.fleeAttempt && !secondTurnResult.fleeAttempt.success);
    logger.debug(`processBattleRound completed for battle: ${battleState.id}`);
    return {
        battleState,
        attackedFirst: battleState.firstAttackerId,
        playerAction: {
            damage: firstActor.id === attackerState.id ? firstTurnResult.damage : secondTurnResult.damage,
            type: firstActor.id === attackerState.id ? firstTurnResult.actionType : secondTurnResult.actionType,
            userLifeSteal: firstActor.id === attackerState.id ? firstTurnResult.lifeSteal : secondTurnResult.lifeSteal,
            userBleedAmount: firstActor.id === attackerState.id ? firstTurnResult.bleedAmount : secondTurnResult.bleedAmount,
        },
        enemyAction: {
            damage: firstActor.id === targetState.id ? firstTurnResult.damage : secondTurnResult.damage,
            type: firstActor.id === targetState.id ? firstTurnResult.actionType : secondTurnResult.actionType,
            targetLifeSteal: firstActor.id === targetState.id ? firstTurnResult.lifeSteal : secondTurnResult.lifeSteal,
            targetBleedAmount: firstActor.id === targetState.id ? firstTurnResult.bleedAmount : secondTurnResult.bleedAmount,
        },
        ...(playerFailedFlee && { flee: "failed" }),
        ...rewardInfo,
    };
};
