import { ExploreNodeLocation } from "@prisma/client";
import { LogErrorStack, logger } from "../../../utils/log.js";
import * as StoryService from "../../../core/story.service.js";
import * as StoryRepository from "../../../repositories/story.repository.js";
import * as ExploreRepository from "../../../repositories/explore.repository.js";
export const handleStoryNodeInteraction = async (userId, nodeId, location, metadata) => {
    try {
        const episodeId = metadata.episodeId;
        if (!episodeId) {
            logger.error(`Story node ${nodeId} missing episodeId in metadata`);
            return {
                success: false,
                message: "Invalid story node configuration",
            };
        }
        const episode = await StoryRepository.findEpisodeById(episodeId);
        if (!episode) {
            logger.error(`Episode ${episodeId} not found for story node ${nodeId}`);
            return {
                success: false,
                message: "Story episode not found",
            };
        }
        logger.info(`User ${userId} interacted with story node ${nodeId} for episode ${episodeId}, preparing to show story episode player`);
        return {
            success: true,
            message: `Started story episode: ${episode.name}`,
            redirectToStoryPlayer: true,
            episodeData: episode,
        };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to handle story node interaction", error });
        return {
            success: false,
            message: "Failed to start story episode",
        };
    }
};
export const createStoryNodesForUser = async (userId, location) => {
    try {
        logger.info(`Creating story nodes for user ${userId} in location ${location}`);
        const activeStoryNodes = await StoryService.getActiveStoryNodes(userId, location);
        logger.info(`Found ${activeStoryNodes.length} active story nodes for user ${userId} in ${location}`);
        const existingNodes = await ExploreRepository.getPlayerNodesByUserAndLocation(userId, location);
        const existingStoryNodes = existingNodes.filter((node) => node.nodeType === "STORY");
        logger.info(`Found ${existingStoryNodes.length} existing story nodes for user ${userId} in ${location}`);
        for (const existingNode of existingStoryNodes) {
            const episodeId = existingNode.metadata?.episodeId;
            const isStillActive = activeStoryNodes.some((node) => node.id === episodeId);
            if (!isStillActive) {
                await ExploreRepository.deletePlayerNode(existingNode.id, userId);
                logger.info(`Removed inactive story node ${existingNode.id} for episode ${episodeId}`);
            }
        }
        for (const storyNode of activeStoryNodes) {
            const hasExistingNode = existingStoryNodes.some((node) => node.metadata?.episodeId === storyNode.id);
            if (hasExistingNode) {
                logger.info(`Story node already exists for episode ${storyNode.id} in ${location}`);
            }
            else {
                logger.info(`Creating new story node for episode ${storyNode.id} (${storyNode.name}) in ${location}`);
                await createStoryNode(userId, storyNode, storyNode.location);
            }
        }
    }
    catch (error) {
        LogErrorStack({ message: "Failed to create story nodes for user", error });
    }
};
const createStoryNode = async (userId, storyNodeData, nodeLocation) => {
    try {
        const position = await findAvailablePosition(userId, nodeLocation);
        if (!position) {
            logger.warn(`No available position for story node in ${nodeLocation} for user ${userId}`);
            return;
        }
        const createdNode = await ExploreRepository.createPlayerNode(userId, "STORY", `📖 ${storyNodeData.name}`, storyNodeData.description || "A story episode awaits...", position, nodeLocation, null, {
            episodeId: storyNodeData.id,
            episodeType: storyNodeData.episodeType,
            questObjectiveId: storyNodeData.questObjectiveId,
            storyNode: true,
        }, "available");
        logger.info(`Created story node ${createdNode.id} for episode ${storyNodeData.id} at position (${position.x}, ${position.y}) in ${nodeLocation}`);
    }
    catch (error) {
        LogErrorStack({ message: "Failed to create story node", error });
    }
};
const findAvailablePosition = async (userId, location) => {
    try {
        const [staticNodes, playerNodes] = await Promise.all([
            ExploreRepository.getStaticNodesByLocation(location),
            ExploreRepository.getPlayerNodesByUserAndLocation(userId, location),
        ]);
        const occupiedPositions = new Set();
        for (const node of staticNodes) {
            occupiedPositions.add(`${node.position.x},${node.position.y}`);
        }
        for (const node of playerNodes) {
            occupiedPositions.add(`${node.position.x},${node.position.y}`);
        }
        for (let y = 0; y < 5; y++) {
            for (let x = 0; x < 5; x++) {
                const positionKey = `${x},${y}`;
                if (!occupiedPositions.has(positionKey)) {
                    return { x, y };
                }
            }
        }
        return null;
    }
    catch (error) {
        LogErrorStack({ message: "Failed to find available position", error });
        return null;
    }
};
export const removeStoryNode = async (userId, episodeId, location) => {
    try {
        if (location) {
            const playerNodes = await ExploreRepository.getPlayerNodesByUserAndLocation(userId, location);
            const storyNode = playerNodes.find((node) => node.nodeType === "STORY" && node.metadata?.episodeId === episodeId);
            if (storyNode) {
                await ExploreRepository.deletePlayerNode(storyNode.id, userId);
                logger.debug(`Successfully removed story node ${storyNode.id} for episode ${episodeId} for user ${userId} at ${location}`);
                return;
            }
        }
        else {
            const allLocations = Object.values(ExploreNodeLocation);
            for (const searchLocation of allLocations) {
                const playerNodes = await ExploreRepository.getPlayerNodesByUserAndLocation(userId, searchLocation);
                const storyNode = playerNodes.find((node) => node.nodeType === "STORY" && node.metadata?.episodeId === episodeId);
                if (storyNode) {
                    await ExploreRepository.deletePlayerNode(storyNode.id, userId);
                    logger.debug(`Successfully removed story node ${storyNode.id} for episode ${episodeId} for user ${userId} at ${searchLocation}`);
                    return;
                }
            }
        }
        logger.debug(`No story node found for episode ${episodeId} for user ${userId}`);
    }
    catch (error) {
        LogErrorStack({ message: `Failed to remove story node for episode ${episodeId}`, error });
    }
};
