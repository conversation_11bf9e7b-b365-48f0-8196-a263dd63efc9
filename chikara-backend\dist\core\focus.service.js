import { db } from "../lib/db.js";
import gameConfig from "../config/gameConfig.js";
import { logger } from "../utils/log.js";
const { FOCUS_PER_BATTLE_WIN, FOCUS_PER_QUEST_COMPLETE, FOCUS_PER_DAILY_QUEST, FOCUS_PER_MISSION_HOUR, } = gameConfig;
export async function addFocus(userId, baseFocus, source) {
    try {
        const updatedUser = await db.user.update({
            where: { id: userId },
            data: {
                focus: {
                    increment: baseFocus,
                },
            },
            select: { focus: true },
        });
        logger.info(`User ${userId} gained ${baseFocus} focus from ${source}. New focus: ${updatedUser.focus}`);
        return baseFocus;
    }
    catch (error) {
        logger.error(`Error adding focus to user ${userId}:`, error);
        return 0;
    }
}
export async function addBattleWinFocus(userId) {
    return await addFocus(userId, FOCUS_PER_BATTLE_WIN, "battle_win");
}
export async function addQuestCompleteFocus(userId) {
    return await addFocus(userId, FOCUS_PER_QUEST_COMPLETE, "quest_complete");
}
export async function addDailyQuestFocus(userId) {
    return await addFocus(userId, FOCUS_PER_DAILY_QUEST, "daily_quest");
}
export async function addMissionHourFocus(userId, hours) {
    return await addFocus(userId, FOCUS_PER_MISSION_HOUR * hours, `mission_${hours}_hours`);
}
