import * as <PERSON><PERSON><PERSON><PERSON> from "./job.helpers.js";
import * as JobRepository from "../../repositories/job.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { LogErrorStack } from "../../utils/log.js";
import * as UserRepository from "../../repositories/user.repository.js";
export const jobList = async () => {
    const jobs = await JobRepository.findAllJobs();
    const response = [];
    for (const job of jobs) {
        response.push({
            id: job.id,
            name: job.name,
            description: job.description,
            avatar: job.avatar,
            baseRequirements: JobHelper.AttributeRequirementsForJobLevel(job, 1),
        });
    }
    return { data: response };
};
export const fullJobList = async () => {
    const jobs = await JobRepository.findAllJobs();
    return { data: jobs };
};
export const applyForJob = async (userId, jobId) => {
    const job = await JobRepository.findJobById(jobId);
    if (!job) {
        return { error: "Invalid job", statusCode: 400 };
    }
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    if (!(await JobHelper.UserQualifiesForJobLevel(currentUser, job, 1))) {
        return { error: "You do not qualify", statusCode: 400 };
    }
    await JobRepository.updateUserJob(currentUser, jobId, 1);
    logAction({
        action: "JOB_STARTED",
        userId: currentUser.id,
        info: {
            jobId: job.id,
            jobName: job.name,
        },
    });
    return { data: "Success" };
};
export const applyForPromotion = async (userId) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    if (!currentUser.jobId) {
        return { error: "You do not have a job", statusCode: 400 };
    }
    const job = await JobRepository.findJobById(currentUser.jobId);
    if (!job) {
        LogErrorStack({
            message: `Job assigned to user ${currentUser.id} but not found: ${currentUser.jobId}`,
            error: new Error(`Job assigned to user ${currentUser.id} but not found: ${currentUser.jobId}`),
        });
        return { error: "invalid job state", statusCode: 500 };
    }
    let low = (currentUser.jobLevel || 0) + 1;
    let high = 20;
    let bestLevel = currentUser.jobLevel || 0;
    while (low <= high) {
        const mid = Math.floor((low + high) / 2);
        if (await JobHelper.UserQualifiesForJobLevel(currentUser, job, mid)) {
            bestLevel = mid;
            low = mid + 1;
        }
        else {
            high = mid - 1;
        }
    }
    await JobRepository.updateUserJobLevel(currentUser, bestLevel);
    logAction({
        action: "JOB_PROMOTED",
        userId: currentUser.id,
        info: {
            jobId: job.id,
            jobName: job.name,
            level: bestLevel,
        },
    });
    return { data: "Success" };
};
export const GetJobLevelRequirement = async (level, jobId) => {
    const job = await JobRepository.findJobById(jobId);
    if (!job) {
        return { error: "Job not found", statusCode: 404 };
    }
    return { data: JobHelper.AttributeRequirementsForJobLevel(job, level) };
};
export const getCurrentJobInfo = async (userId) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    if (!currentUser.jobId) {
        return { error: "Job not found", statusCode: 404 };
    }
    const job = await JobRepository.findJobById(currentUser.jobId);
    if (!job) {
        return { error: "Job not found", statusCode: 404 };
    }
    const paymentAmount = JobHelper.PaymentForJobLevel(job, currentUser.jobLevel || 0);
    return {
        data: {
            id: job.id,
            name: job.name,
            description: job.description,
            avatar: job.avatar,
            salary: paymentAmount,
            level: currentUser.jobLevel,
        },
    };
};
export const changeJobPayoutTime = async (userId, newTime) => {
    if (!newTime) {
        return { error: "No time selected", statusCode: 400 };
    }
    const times = [2, 8, 14, 20];
    if (!times.includes(newTime)) {
        return { error: "Invalid time selected", statusCode: 400 };
    }
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    if (currentUser.blockNextJobPayout) {
        return { error: "Can't change payout time till after the next payout!", statusCode: 400 };
    }
    const oldHour = currentUser.jobPayoutHour;
    const blockNextPayout = !!oldHour;
    await JobRepository.updateUserJobPayoutTime(currentUser, newTime, blockNextPayout);
    logAction({
        action: "JOB_PAYOUT_TIME_CHANGED",
        userId: currentUser.id,
        info: {
            oldHour: oldHour,
            newHour: newTime,
        },
    });
    return { data: "Success" };
};
