import { stringify } from "../utils/jsonHelper.js";
import { LogErrorStack } from "../utils/log.js";
import { ZodError } from "zod";
export const validate = (schema, source) => {
    return (req, res, next) => {
        const dataSource = source || (req.method === "GET" ? "query" : "body");
        try {
            const data = schema.parse(req[dataSource]);
            if (dataSource === "body") {
                req[dataSource] = data;
            }
            next();
        }
        catch (error) {
            if (error instanceof ZodError) {
                const errorMessages = error.errors.map((issue) => ({
                    message: `${issue.path.join(".")} is ${issue.message}`,
                }));
                res.status(400).send(stringify({ success: false, data: null, error: "Invalid data", validationErrors: errorMessages }));
                return;
            }
            const errorResponse = {
                success: false,
                data: null,
                error: "Unexpected server error",
            };
            LogErrorStack({ error });
            res.status(500).send(stringify(errorResponse));
            return;
        }
    };
};
export default validate;
