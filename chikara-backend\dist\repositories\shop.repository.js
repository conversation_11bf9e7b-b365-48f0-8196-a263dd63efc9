import { db } from "../lib/db.js";
export const findAllShops = async () => {
    return await db.shop.findMany();
};
export const findShopById = async (shopId) => {
    return await db.shop.findUnique({
        where: { id: shopId },
        include: {
            shop_listing: {
                include: {
                    item: {
                        select: {
                            id: true,
                            name: true,
                            itemType: true,
                            rarity: true,
                            level: true,
                            about: true,
                            cashValue: true,
                            image: true,
                            damage: true,
                            armour: true,
                            health: true,
                            energy: true,
                            actionPoints: true,
                            baseAmmo: true,
                            itemEffects: true,
                            recipeUnlockId: true,
                        },
                    },
                },
            },
        },
    });
};
export const findShopListingById = async (listingId) => {
    return await db.shop_listing.findUnique({
        where: { id: listingId },
    });
};
export const updateShopListingStock = async (listingId, newStock) => {
    return await db.shop_listing.update({
        where: { id: listingId },
        data: { stock: newStock },
    });
};
export const createShop = async (data) => {
    return await db.shop.create({ data });
};
export const updateShop = async (shopId, data) => {
    return await db.shop.update({
        where: { id: shopId },
        data,
    });
};
export const deleteShop = async (shopId) => {
    return await db.shop.delete({
        where: { id: shopId },
    });
};
export const createShopListing = async (data) => {
    return await db.shop_listing.create({ data });
};
export const updateShopListing = async (listingId, data) => {
    return await db.shop_listing.update({
        where: { id: listingId },
        data,
    });
};
export const deleteShopListing = async (listingId) => {
    return await db.shop_listing.delete({
        where: { id: listingId },
    });
};
export const getTraderReputation = async (filter) => {
    return await db.trader_rep.findMany({
        where: filter,
    });
};
