import z from "zod";
export const usernameSchema = z.string().min(3).max(30);
const authValidation = {
    register: z.object({
        email: z.string().email(),
        password: z.string().min(8),
        username: usernameSchema,
        code: z.string().optional(),
    }),
    massEmailSchema: z.object({
        emails: z.array(z.string().email()),
        note: z.string().min(1),
    }),
    generateCodeSchema: z.object({
        note: z.string().min(1),
    }),
    checkCodeSchema: z.object({
        code: z.string().min(1),
    }),
};
export default authValidation;
