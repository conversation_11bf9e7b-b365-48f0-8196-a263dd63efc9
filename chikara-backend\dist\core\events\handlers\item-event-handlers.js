import { logger, LogErrorStack } from "../../../utils/log.js";
import * as AchievementService from "../../achievement.service.js";
import * as QuestService from "../../quest.service.js";
import * as UserRepository from "../../../repositories/user.repository.js";
export const handleItemDroppedEvent = async (payload) => {
    try {
        const { userId, itemId, quantity } = payload;
        const user = await UserRepository.getUserById(userId);
        if (!user) {
            logger.error(`User not found for item dropped event: ${userId}`);
            return;
        }
        await QuestService.handleFetchItem(userId, itemId, quantity);
        logger.debug(`Processed item dropped event for user ${userId}, item ${itemId}, quantity ${quantity}`);
    }
    catch (error) {
        LogErrorStack({ message: "Error handling item dropped event", error });
    }
};
export const handleItemCraftedEvent = async (payload) => {
    try {
        const { userId, itemId, quantity } = payload;
        const user = await UserRepository.getUserById(userId);
        if (!user) {
            logger.error(`User not found for item crafted event: ${userId}`);
            return;
        }
        await QuestService.handleCraftItem(userId, itemId, quantity);
        await AchievementService.UpdateUserAchievement(userId, "craftsCompleted", quantity);
        logger.debug(`Processed item crafted event for user ${userId}, item ${itemId}, quantity ${quantity}`);
    }
    catch (error) {
        LogErrorStack({ message: "Error handling item crafted event", error });
    }
};
