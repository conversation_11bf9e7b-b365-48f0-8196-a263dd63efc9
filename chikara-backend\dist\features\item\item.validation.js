import { z } from "zod";
const itemEffectSchema = z.object({
    description: z.string().optional(),
    effectKey: z.string(),
    effectValue: z.number().optional(),
    effectModifier: z.enum(["multiply", "add", "subtract", "divide", "set"]),
    effectTier: z.string().optional(),
});
const itemValidation = {
    create: z.object({
        name: z.string().min(1),
        about: z.string(),
        damage: z.number().int().optional(),
        armour: z.number().int().optional(),
        image: z.string().optional(),
        rarity: z.string(),
        itemType: z.string(),
        level: z.number().int().optional(),
        cashValue: z.number().int().optional(),
        health: z.number().int().optional(),
        energy: z.number().int().optional(),
        actionPoints: z.number().int().optional(),
        baseAmmo: z.number().int().optional(),
        itemEffects: z.array(itemEffectSchema).optional(),
        recipeUnlockId: z.number().int().optional(),
    }),
    update: z.object({
        id: z.number().int().positive(),
        name: z.string().min(1).optional(),
        about: z.string().optional(),
        itemType: z.string().optional(),
        image: z.string().optional(),
        damage: z.number().int().optional(),
        armour: z.number().int().optional(),
        rarity: z.string().optional(),
        level: z.number().int().optional(),
        cashValue: z.number().int().optional(),
        health: z.number().int().optional(),
        energy: z.number().int().optional(),
        actionPoints: z.number().int().optional(),
        baseAmmo: z.number().int().optional(),
        itemEffects: z.array(itemEffectSchema).optional(),
        recipeUnlockId: z.number().int().optional(),
        type: z.string().optional(),
    }),
    delete: z.object({
        id: z.number().int().positive(),
    }),
    getAll: z.object({}),
    getUpgradeItems: z.object({}),
    upgradeItem: z.object({
        upgradeCores: z.object({
            small: z
                .object({
                id: z.number().int().positive(),
                count: z.number().int().positive(),
            })
                .optional(),
            medium: z
                .object({
                id: z.number().int().positive(),
                count: z.number().int().positive(),
            })
                .optional(),
            large: z
                .object({
                id: z.number().int().positive(),
                count: z.number().int().positive(),
            })
                .optional(),
            giant: z
                .object({
                id: z.number().int().positive(),
                count: z.number().int().positive(),
            })
                .optional(),
        }),
        itemId: z.number().int().positive(),
    }),
    deathNote: z.object({
        userId: z.number().int().positive(),
        injuryName: z.string(),
        injuryType: z.string(),
    }),
    lifeNote: z.object({
        userId: z.number().int().positive(),
    }),
    megaphone: z.object({
        message: z.string(),
    }),
    kompromat: z.object({
        userId: z.number().int().positive(),
        reason: z.string(),
    }),
};
export default itemValidation;
