import type { ExploreNodeLocation, TravelMethod } from "@prisma/client";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { logPlayerAction } from "../../../lib/actionLogger.js";
import { TRAVEL_COSTS, TRAVEL_TIMES } from "../explore.constants.js";
import * as ExploreController from "../explore.controller.js";
import * as ExploreRepository from "../../../repositories/explore.repository.js";

// Mock dependencies - these are handled by setupTests.ts
vi.mock("../../../repositories/explore.repository.js");

// Mock the explore helpers module
vi.mock("../explore.helpers.js", () => ({
    validateTravelParameters: vi.fn(),
}));

const mockedExploreRepository = vi.mocked(ExploreRepository);
const mockedLogPlayerAction = vi.mocked(logPlayerAction);
const mockedExploreHelpers = vi.mocked(await import("../explore.helpers.js"));

describe("ExploreController - changeMapLocation", () => {
    beforeEach(() => {
        // Default behavior: validateTravelParameters doesn't throw (valid parameters)
        mockedExploreHelpers.validateTravelParameters.mockImplementation(() => {
            // Do nothing - valid parameters
        });
    });

    it("should successfully initiate travel when parameters are valid", async () => {
        // Arrange
        const userId = 1;
        const newLocation: ExploreNodeLocation = "shinjuku";
        const method: TravelMethod = "bus";
        const mockUpdatedUser = {
            cash: 1000 - TRAVEL_COSTS.bus.shinjuku,
            currentMapLocation: "shinjuku" as ExploreNodeLocation,
            travelStartTime: new Date(),
            travelEndTime: new Date(Date.now() + TRAVEL_TIMES.bus.shinjuku * 60 * 1000),
        };

        mockedExploreRepository.initiateTravel.mockResolvedValue(mockUpdatedUser);
        mockedLogPlayerAction.mockResolvedValue();

        // Act
        const result = await ExploreController.changeMapLocation(userId, newLocation, method);

        // Assert
        expect(result.data).toBeDefined();
        expect(result.data?.cost).toBe(TRAVEL_COSTS.bus.shinjuku);
        expect(result.data?.travelTime).toBe(TRAVEL_TIMES.bus.shinjuku);
        expect(result.data?.method).toBe(method);
        expect(mockedExploreRepository.initiateTravel).toHaveBeenCalledWith(
            userId,
            newLocation,
            method,
            TRAVEL_COSTS.bus.shinjuku,
            TRAVEL_TIMES.bus.shinjuku
        );
        expect(mockedLogPlayerAction).toHaveBeenCalledWith(
            "EXPLORE_TRAVEL_INITIATED",
            expect.objectContaining({
                destination: newLocation,
                method,
                cost: TRAVEL_COSTS.bus.shinjuku,
                travelTimeMinutes: TRAVEL_TIMES.bus.shinjuku,
            }),
            userId
        );
    });

    it("should handle walking to all valid locations", async () => {
        // Arrange
        const userId = 1;
        const method: TravelMethod = "walk";
        const locations: ExploreNodeLocation[] = ["shibuya", "shinjuku", "bunkyo", "chiyoda", "minato"];

        for (const location of locations) {
            const mockUpdatedUser = {
                cash: 1000, // Walking is free
                currentMapLocation: location,
                travelStartTime: new Date(),
                travelEndTime: new Date(Date.now() + TRAVEL_TIMES.walk[location] * 60 * 1000),
            };

            mockedExploreRepository.initiateTravel.mockResolvedValue(mockUpdatedUser);

            // Act
            const result = await ExploreController.changeMapLocation(userId, location, method);

            // Assert
            expect(result.data).toBeDefined();
            expect(result.data?.cost).toBe(0); // Walking is free
            expect(result.data?.method).toBe("walk");
            expect(result.error).toBeUndefined();
        }
    });

    it("should handle bus travel to all valid locations", async () => {
        // Arrange
        const userId = 1;
        const method: TravelMethod = "bus";
        const locations: ExploreNodeLocation[] = ["shibuya", "shinjuku", "bunkyo", "chiyoda", "minato"];

        for (const location of locations) {
            const mockUpdatedUser = {
                cash: 1000 - TRAVEL_COSTS.bus[location],
                currentMapLocation: location,
                travelStartTime: new Date(),
                travelEndTime: new Date(Date.now() + TRAVEL_TIMES.bus[location] * 60 * 1000),
            };

            mockedExploreRepository.initiateTravel.mockResolvedValue(mockUpdatedUser);

            // Act
            const result = await ExploreController.changeMapLocation(userId, location, method);

            // Assert
            expect(result.data).toBeDefined();
            expect(result.data?.cost).toBe(TRAVEL_COSTS.bus[location]);
            expect(result.data?.method).toBe("bus");
            expect(result.error).toBeUndefined();
        }
    });
});
