import Button from "@/components/Buttons/Button";
import { APIROUTES } from "@/helpers/apiRoutes";
import { handlePost } from "@/helpers/axiosInstance";
import { useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";

const CombatPageSection = () => {
    const queryClient = useQueryClient();
    const navigate = useNavigate();

    const fullHeal = async () => {
        try {
            await handlePost("/dev/fullheal", {});
            queryClient.invalidateQueries({
                queryKey: [APIROUTES.USER.CURRENTUSERINFO],
            });
        } catch (error) {
            console.error("Error performing full heal:", error);
        }
    };

    const healAllUsers = async () => {
        try {
            await handlePost("/dev/fullhealall", {});
            queryClient.invalidateQueries({
                queryKey: [APIROUTES.USER.CURRENTUSERINFO],
            });
        } catch (error) {
            console.error("Error healing all users:", error);
        }
    };

    const randomPVPBattle = async () => {
        try {
            await handlePost("/dev/pvpbattle", { version: 2 });

            await queryClient.invalidateQueries({
                queryKey: [APIROUTES.USER.CURRENTUSERINFO],
            });
            await queryClient.invalidateQueries({ queryKey: APIROUTES.BATTLE.STATUS });

            navigate("/fight");
        } catch (error) {
            console.error("Error initiating PvP battle V2:", error);
        }
    };

    const resetAllBattles = async () => {
        try {
            await handlePost("/dev/resetBattles", {});

            await queryClient.invalidateQueries({
                queryKey: [APIROUTES.USER.CURRENTUSERINFO],
            });
            navigate("/home");
        } catch (error) {
            console.error("Error resetting battles:", error);
        }
    };

    const addRandomEffects = async () => {
        try {
            await handlePost("/dev/addRandomEffects", {});

            await queryClient.invalidateQueries({
                queryKey: APIROUTES.USER.STATUSEFFECTS,
            });
            navigate("/home");
        } catch (error) {
            console.error("Error resetting battles:", error);
        }
    };

    const removeAllEffects = async () => {
        try {
            await handlePost("/dev/removeAllEffects", {});

            await queryClient.invalidateQueries({
                queryKey: APIROUTES.USER.STATUSEFFECTS,
            });
            navigate("/home");
        } catch (error) {
            console.error("Error resetting battles:", error);
        }
    };

    return (
        <div className="grid grid-cols-2 gap-3 p-2">
            <Button className="text-sm!" type="primary" onClick={randomPVPBattle}>
                Random PvP Battle
            </Button>
            <Button type="primary" onClick={fullHeal}>
                Full Heal
            </Button>
            <Button className="text-sm!" type="primary" onClick={healAllUsers}>
                Heal all Users
            </Button>
            <Button className="text-sm!" type="primary" onClick={resetAllBattles}>
                Reset All Battles
            </Button>
            <Button className="text-sm!" type="primary" onClick={addRandomEffects}>
                Random Status Effects
            </Button>
            <Button className="text-sm!" type="primary" onClick={removeAllEffects}>
                Remove Status Effects
            </Button>
        </div>
    );
};

export default CombatPageSection;
