import * as InventoryService from "../../../core/inventory.service.js";
import * as NotificationService from "../../../core/notification.service.js";
import * as StatusEffectService from "../../../core/statuseffect.service.js";
import * as UserService from "../../../core/user.service.js";
import * as UniqueItemHelpers from "../../item/uniqueitem.helpers.js";
import * as ShrineHelper from "../../shrine/shrine.helpers.js";
import * as TalentHelper from "../../talents/talents.helpers.js";
import { logAction } from "../../../lib/actionLogger.js";
import { db } from "../../../lib/db.js";
import { NotificationTypes } from "../../../types/notification.js";
import { mapExploreLocationToLocationTypes } from "../explore.helpers.js";
import { emitEncounterCompleted, emitItemDropped } from "../../../core/events/index.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import * as ItemRepository from "../../../repositories/item.repository.js";
export const CHARACTER_NAMES = [
    "Daiki",
    "Hana",
    "Hiroshi",
    "Izumi",
    "Kenzo",
    "Mai",
    "Tashiro",
    "Yuta",
    "???",
    "Kazuya",
    "Katsuro",
];
export const ENCOUNTER_LOCATIONS_IMAGES = [
    "mall1",
    "schoolField1",
    "housingExt2",
    "street1",
    "market",
];
export const GOOD_DIALOGUE_LINES = [
    "Here's a little something to help you out. Good luck with your training!",
    "You helped me out the other day, so I wanted to give you this as a thank you. Hope it comes in handy.",
    "You're doing great at the academy. Keep it up! Here's a small token of my appreciation.",
    "You're always so kind to everyone around here. Here's a small gesture of my appreciation.",
    "I have something for you. It's not much, but I hope it'll be helpful in your studies.",
    "I've been watching you, and I think you have a lot of potential. Here, take this!",
    "I wanted to give you something for all the times you've helped me out. Thank you!",
    "I can see you're working hard to improve. Keep it up! Here's something to motivate you.",
    "I thought this might come in handy for someone like you. Take it!",
];
export const BAD_DIALOGUE_LINES = [
    "Looks like someone was carrying too much cash on them.",
    "Don't worry, I won't be hurting you. I just need your money.",
    "You should learn to be more careful with your possessions.",
    "I'm taking your money and there's nothing you can do about it.",
    "Hey there, give me all your money or else!",
];
export const APOLLO_DIALOGUE = [
    "Woof!",
    "Bark bark bark!",
    "Did you know that Godzilla has survived entering a black hole?",
];
export const CHARACTER_ENCOUNTER_CONFIG = {
    DEFAULT_ENCOUNTER_JAIL_DURATION_MS: 10 * 60 * 1000,
    APOLLO_ENCOUNTER_CHANCE: 0.05,
    ITEM_DROP_CHANCE: 0.4,
    HOSPITALIZATION_CHANCE: 0.05,
    JAIL_CHANCE: 0.15,
    APOLLO_HEAL_PERCENTAGE: 0.3,
};
function randomIntFromInterval(min, max) {
    return Math.floor(Math.random() * (max - min + 1) + min);
}
export const generateCharacterDialogue = (goodOutcome, userLevel) => {
    const selectedCharacter = CHARACTER_NAMES[Math.floor(Math.random() * CHARACTER_NAMES.length)];
    const selectedLocation = ENCOUNTER_LOCATIONS_IMAGES[Math.floor(Math.random() * ENCOUNTER_LOCATIONS_IMAGES.length)];
    const selectedLine = goodOutcome
        ? GOOD_DIALOGUE_LINES[Math.floor(Math.random() * GOOD_DIALOGUE_LINES.length)]
        : BAD_DIALOGUE_LINES[Math.floor(Math.random() * BAD_DIALOGUE_LINES.length)];
    const cashReward = randomIntFromInterval(20 + userLevel * 10, 20 + userLevel * 30);
    if (goodOutcome && Math.random() <= CHARACTER_ENCOUNTER_CONFIG.APOLLO_ENCOUNTER_CHANCE) {
        const apolloDialogue = APOLLO_DIALOGUE[Math.floor(Math.random() * APOLLO_DIALOGUE.length)];
        return {
            character: "Apollo",
            location: selectedLocation,
            line: apolloDialogue,
            isItemDrop: false,
            rewards: 0,
            mugged: false,
        };
    }
    if (goodOutcome && Math.random() <= CHARACTER_ENCOUNTER_CONFIG.ITEM_DROP_CHANCE) {
        return {
            character: selectedCharacter,
            location: selectedLocation,
            line: selectedLine,
            isItemDrop: true,
            rewards: cashReward,
            mugged: !goodOutcome,
        };
    }
    return {
        character: selectedCharacter,
        location: selectedLocation,
        line: selectedLine,
        isItemDrop: false,
        rewards: cashReward,
        mugged: !goodOutcome,
    };
};
export const getExploreDropId = async (user, location) => {
    const locationType = mapExploreLocationToLocationTypes(location);
    const potentialDrops = await db.drop_chance.findMany({
        where: {
            dropChanceType: "roguelike",
            location: {
                in: [locationType, "any"],
            },
            dropRate: {
                gt: 0,
            },
        },
        orderBy: {
            dropRate: "asc",
        },
    });
    const shrineBuffActive = await ShrineHelper.dailyBuffIsActive("rareDrops");
    for (const drop of potentialDrops) {
        let dropRate = drop.dropRate;
        if (shrineBuffActive && dropRate < 0.08) {
            dropRate *= shrineBuffActive;
        }
        dropRate = dropRate / 6.0;
        if (dropRate < 0.05) {
            dropRate = dropRate * 0.3;
        }
        if (Math.random() <= dropRate) {
            return drop.itemId || null;
        }
    }
    return null;
};
export const applyEncounterReward = async (dialogue, currentUser, location) => {
    if (dialogue.isItemDrop) {
        const droppedItemId = await getExploreDropId(currentUser, location);
        if (droppedItemId && droppedItemId !== 0) {
            const droppedItem = await ItemRepository.findItemById(droppedItemId);
            if (droppedItem) {
                await InventoryService.AddItemToUser({
                    userId: currentUser.id,
                    itemId: droppedItem.id,
                    amount: 1,
                    isTradeable: true,
                });
                dialogue.rewards = droppedItem;
                await emitItemDropped({
                    userId: currentUser.id,
                    itemId: droppedItem.id,
                    quantity: 1,
                    source: "encounter",
                    location: mapExploreLocationToLocationTypes(location),
                });
                logAction({
                    action: "EXPLORE_CHARACTER_ENCOUNTER_ITEM_DROP",
                    userId: currentUser.id,
                    info: {
                        itemId: droppedItem.id,
                        itemName: droppedItem.name,
                        location: location,
                    },
                });
                return dialogue;
            }
        }
    }
    dialogue.isItemDrop = false;
    if (!dialogue.mugged &&
        typeof dialogue.rewards === "number" &&
        dialogue.rewards > 0 &&
        (await UniqueItemHelpers.IsWealthItemEquipped(currentUser))) {
        dialogue.rewards = Math.round(dialogue.rewards * 1.25);
    }
    if (typeof dialogue.rewards === "number" && dialogue.rewards > 0) {
        const updatedCash = currentUser.cash + dialogue.rewards;
        await UserService.updateUser(currentUser.id, { cash: updatedCash });
        logAction({
            action: "EXPLORE_CHARACTER_ENCOUNTER_CASH_REWARD",
            userId: currentUser.id,
            info: {
                rewards: dialogue.rewards,
                location: location,
            },
        });
    }
    return dialogue;
};
const getMugChance = (userLevel) => {
    return userLevel < 4 ? 0 : 0.2;
};
export const handleCharacterEncounter = async (userId, nodeId, location) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return {
            success: false,
            message: "User not found",
        };
    }
    emitEncounterCompleted({ userId, encounterId: nodeId, location });
    const mugChance = getMugChance(currentUser.level);
    const goodOutcome = Math.random() > mugChance;
    let dialogue = generateCharacterDialogue(goodOutcome, currentUser.level);
    const updateValues = {};
    if (dialogue.character === "Apollo") {
        const heal = Math.round(currentUser.health * CHARACTER_ENCOUNTER_CONFIG.APOLLO_HEAL_PERCENTAGE);
        updateValues.currentHealth = Math.min(currentUser.currentHealth + heal, currentUser.health);
        logAction({
            action: "EXPLORE_CHARACTER_ENCOUNTER_HEALED",
            userId: currentUser.id,
            info: {
                heal: heal,
                location: location,
            },
        });
        if (Object.keys(updateValues).length > 0) {
            await UserService.updateUser(currentUser.id, updateValues);
        }
        return {
            success: true,
            message: "You encountered Apollo and were healed!",
            data: {
                action: "character_encounter",
                dialogue: dialogue,
                healed: true,
            },
        };
    }
    if (dialogue.rewards) {
        if (goodOutcome) {
            dialogue = await applyEncounterReward(dialogue, currentUser, location);
        }
        else if (typeof dialogue.rewards === "number") {
            const mugAmount = Math.min(dialogue.rewards, currentUser.cash);
            dialogue.rewards = mugAmount;
            updateValues.cash = Math.max(0, currentUser.cash - mugAmount);
            logAction({
                action: "EXPLORE_CHARACTER_ENCOUNTER_MUGGED",
                userId: currentUser.id,
                info: {
                    rewards: mugAmount,
                    location: location,
                },
            });
        }
    }
    if (!goodOutcome) {
        const escapeArtistTalent = await TalentHelper.UserHasEscapeArtistTalent(currentUser.id);
        const jailChance = escapeArtistTalent
            ? CHARACTER_ENCOUNTER_CONFIG.JAIL_CHANCE * (escapeArtistTalent.modifier ?? 1)
            : CHARACTER_ENCOUNTER_CONFIG.JAIL_CHANCE;
        if (Math.random() <= CHARACTER_ENCOUNTER_CONFIG.HOSPITALIZATION_CHANCE) {
            dialogue.hospitalised = true;
            const injury = await StatusEffectService.GetRandomInjury("Minor");
            if (injury) {
                await StatusEffectService.ApplyStatusEffectToUser(currentUser, injury);
                dialogue.injury = injury.name;
                dialogue.injuryTier = injury.tier || undefined;
                NotificationService.NotifyUser(currentUser.id, NotificationTypes.hospitalised, {
                    reason: "explore_character_encounter",
                    dialogue: dialogue,
                    injury: injury.name,
                    injuryTier: injury.tier,
                });
                logAction({
                    action: "EXPLORE_CHARACTER_ENCOUNTER_INJURED",
                    userId: currentUser.id,
                    info: {
                        injury: injury.name,
                        injuryTier: injury.tier,
                        location: location,
                    },
                });
            }
        }
        else if (Math.random() < jailChance) {
            const jailShrineBuffActive = (await ShrineHelper.dailyBuffIsActive("jail")) || 1;
            dialogue.jailed = true;
            updateValues.jailedUntil = BigInt(Date.now() + CHARACTER_ENCOUNTER_CONFIG.DEFAULT_ENCOUNTER_JAIL_DURATION_MS * jailShrineBuffActive);
            updateValues.jailReason = "explore";
            logAction({
                action: "EXPLORE_CHARACTER_ENCOUNTER_JAILED",
                userId: currentUser.id,
                info: {
                    jailDuration: CHARACTER_ENCOUNTER_CONFIG.DEFAULT_ENCOUNTER_JAIL_DURATION_MS * jailShrineBuffActive,
                    location: location,
                },
            });
        }
    }
    if (Object.keys(updateValues).length > 0) {
        await UserService.updateUser(currentUser.id, updateValues);
    }
    return {
        success: true,
        message: goodOutcome
            ? `You had a pleasant encounter with ${dialogue.character}!`
            : `You were mugged by ${dialogue.character}!`,
        data: {
            action: "character_encounter",
            dialogue: dialogue,
            goodOutcome: goodOutcome,
        },
    };
};
