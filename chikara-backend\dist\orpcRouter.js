import { bankRouter } from "./features/bank/bank.routes.js";
import { exploreRouter } from "./features/explore/explore.routes.js";
import { jobRouter } from "./features/job/job.routes.js";
import userRouter from "./features/user/user.orpc.js";
import talentsRouter from "./features/talents/talents.orpc.js";
import { battleRouter } from "./features/battle/battle.orpc.js";
import { storyRouter } from "./features/story/story.routes.js";
import { questRouter } from "./features/quest/quest.routes.js";
import { questAdminRouter } from "./features/quest/quest.admin.js";
import { auctionRouter } from "./features/auction/auction.routes.js";
import { shopRouter } from "./features/shop/shop.routes.js";
import { gangRouter } from "./features/gang/gang.orpc.js";
import { courseRouter } from "./features/course/course.routes.js";
import { suggestionsRouter } from "./features/suggestions/suggestions.routes.js";
import { craftingRouter } from "./features/crafting/crafting.routes.js";
import { privateMessageRouter } from "./features/privatemessage/privatemessage.routes.js";
import { profileCommentRouter } from "./features/profilecomment/profilecomment.routes.js";
import { chatRouter } from "./features/chat/chat.routes.js";
import { roguelikeRouter } from "./features/roguelike/roguelike.routes.js";
import { dailyQuestRouter } from "./features/dailyquest/dailyquest.routes.js";
import { infirmaryRouter } from "./features/infirmary/infirmary.routes.js";
import { jailRouter } from "./features/jail/jail.routes.js";
import { missionRouter } from "./features/mission/mission.routes.js";
import { propertyRouter } from "./features/property/property.routes.js";
import { petsRouter } from "./features/pets/pets.routes.js";
import casinoRouter from "./features/casino/casino.orpc.js";
import { bountyRouter } from "./features/bounty/bounty.orpc.js";
import { notificationRouter } from "./features/notification/notification.orpc.js";
import { authRouter, authAdminRouter } from "./features/auth/auth.orpc.js";
import { creatureRouter } from "./features/creature/creature.orpc.js";
import { publicAuth } from "./lib/orpc.js";
import { itemRouter, itemAdminRouter } from "./features/item/item.orpc.js";
import { shrineRouter } from "./features/shrine/shrine.orpc.js";
import { leaderboardRouter } from "./features/leaderboard/leaderboard.orpc.js";
import { dropChanceAdminRouter } from "./features/dropchance/dropchance.orpc.js";
import { socialRouter } from "./features/social/social.orpc.js";
export const appRouter = {
    healthCheck: publicAuth.handler(() => {
        return "OK";
    }),
    auth: authRouter,
    explore: exploreRouter,
    bank: bankRouter,
    job: jobRouter,
    user: userRouter,
    talents: talentsRouter,
    battle: battleRouter,
    story: storyRouter,
    quest: questRouter,
    dailyQuest: dailyQuestRouter,
    auction: auctionRouter,
    shop: shopRouter,
    gang: gangRouter,
    course: courseRouter,
    suggestions: suggestionsRouter,
    crafting: craftingRouter,
    privateMessage: privateMessageRouter,
    profileComment: profileCommentRouter,
    chat: chatRouter,
    roguelike: roguelikeRouter,
    infirmary: infirmaryRouter,
    jail: jailRouter,
    mission: missionRouter,
    property: propertyRouter,
    pets: petsRouter,
    casino: casinoRouter,
    bounty: bountyRouter,
    notification: notificationRouter,
    item: itemRouter,
    shrine: shrineRouter,
    leaderboard: leaderboardRouter,
    social: socialRouter,
    admin: {
        quest: questAdminRouter,
        creature: creatureRouter,
        auth: authAdminRouter,
        item: itemAdminRouter,
        dropChance: dropChanceAdminRouter,
    },
};
