import { NotifyUser } from "../../core/notification.service.js";
import * as SocialRepository from "../../repositories/social.repository.js";
import { NotificationTypes } from "../../types/notification.js";
import { LogErrorStack } from "../../utils/log.js";
import * as UserRepository from "../../repositories/user.repository.js";

/**
 * Get the current user's friends list with online status and other info
 */
export const getFriendsList = async (userId: number) => {
    try {
        const friends = await SocialRepository.getUserFriends(userId);
        return { data: friends };
    } catch (error) {
        LogErrorStack({ message: "Error fetching friends list", error });
        return { error: "Failed to fetch friends list", statusCode: 500 };
    }
};

/**
 * Get the current user's friend requests
 */
export const getFriendRequests = async (userId: number) => {
    try {
        const receivedRequests = await SocialRepository.getFriendRequests(userId);
        return { data: receivedRequests };
    } catch (error) {
        LogErrorStack({ message: "Error fetching friend requests", error });
        return { error: "Failed to fetch friend requests", statusCode: 500 };
    }
};

/**
 * Send a friend request to another user
 */
export const sendFriendRequest = async (senderId: number, recipientId: number) => {
    try {
        // Check if users are already friends
        const existingFriendship = await SocialRepository.findExistingFriendship(senderId, recipientId);

        if (existingFriendship) {
            return { error: "You are already friends with this user", statusCode: 400 };
        }

        // Check if a request already exists
        const existingRequest = await SocialRepository.findExistingFriendRequest(senderId, recipientId);

        if (existingRequest) {
            return { error: "A friend request already exists between you and this user", statusCode: 400 };
        }

        // Check if users are rivals
        const existingRival = await SocialRepository.findExistingRival(senderId, recipientId);

        if (existingRival) {
            return { error: "You cannot send a friend request to your rival", statusCode: 400 };
        }

        // Create the friend request
        const friendRequest = await SocialRepository.createFriendRequest(senderId, recipientId);

        await NotifyUser(recipientId, NotificationTypes.friend_request, {
            user: senderId,
        });

        return { data: friendRequest };
    } catch (error) {
        LogErrorStack({ message: "Error sending friend request", error });
        return { error: "Failed to send friend request", statusCode: 500 };
    }
};

/**
 * Respond to a friend request (accept/decline)
 */
export const respondToFriendRequest = async (userId: number, requestId: number, accept: boolean) => {
    try {
        // Find the request and verify it belongs to this user
        const request = await SocialRepository.findFriendRequestById(requestId, userId);

        if (!request) {
            return { error: "Friend request not found", statusCode: 404 };
        }

        // Delete the request regardless of acceptance
        await SocialRepository.deleteFriendRequest(requestId);

        if (accept) {
            // Create bidirectional friendship records
            await Promise.all([
                SocialRepository.createFriendship(userId, request.senderId),
                SocialRepository.createFriendship(request.senderId, userId),
            ]);

            await NotifyUser(request.senderId, NotificationTypes.friend_request_accepted, {
                user: userId,
            });

            // Fetch the newly created friendship record to return
            const friendship = await SocialRepository.getFriendshipDetails(userId, request.senderId);
            return { data: { accepted: true, friendship } };
        }

        return { data: { accepted: false, friendship: null } };
    } catch (error) {
        LogErrorStack({ message: "Error responding to friend request", error });
        return { error: "Failed to process friend request", statusCode: 500 };
    }
};

/**
 * Remove a friend
 */
export const removeFriend = async (userId: number, friendId: number) => {
    try {
        // Delete bidirectional friendship records
        await SocialRepository.deleteFriendships(userId, friendId);
        return { data: { removed: true } };
    } catch (error) {
        LogErrorStack({ message: "Error removing friend", error });
        return { error: "Failed to remove friend", statusCode: 500 };
    }
};

/**
 * Update a note on a friend
 */
export const updateFriendNote = async (userId: number, friendId: number, note: string | null) => {
    try {
        const friendship = await SocialRepository.findFriendship(userId, friendId);

        if (!friendship) {
            return { error: "Friend not found", statusCode: 404 };
        }

        const updatedFriendship = await SocialRepository.updateFriendshipNote(friendship.id, note);
        return { data: updatedFriendship };
    } catch (error) {
        LogErrorStack({ message: "Error updating friend note", error });
        return { error: "Failed to update friend note", statusCode: 500 };
    }
};

/**
 * Update user's status message
 */
export const updateStatusMessage = async (userId: number, message: string | null) => {
    try {
        const updatedUser = await SocialRepository.updateUserStatusMessage(userId, message);
        return { data: updatedUser };
    } catch (error) {
        LogErrorStack({ message: "Error updating status message", error });
        return { error: "Failed to update status message", statusCode: 500 };
    }
};

/**
 * Update privacy settings
 */
export const updatePrivacySettings = async (userId: number, showLastOnline?: boolean) => {
    try {
        const updateData: Record<string, boolean> = {};
        if (showLastOnline !== undefined) {
            updateData.showLastOnline = showLastOnline;
        }

        if (Object.keys(updateData).length === 0) {
            return { error: "No privacy settings provided", statusCode: 400 };
        }

        const user = await SocialRepository.updateUserPrivacySettings(userId, updateData);
        return { data: user };
    } catch (error) {
        LogErrorStack({ message: "Error updating privacy settings", error });
        return { error: "Failed to update privacy settings", statusCode: 500 };
    }
};

/**
 * Get the user's rivals list
 */
export const getRivalsList = async (userId: number) => {
    try {
        const rivals = await SocialRepository.getUserRivals(userId);
        return { data: rivals };
    } catch (error) {
        LogErrorStack({ message: "Error fetching rivals list", error });
        return { error: "Failed to fetch rivals list", statusCode: 500 };
    }
};

/**
 * Add a rival
 */
export const addRival = async (userId: number, rivalId: number) => {
    try {
        // Check if rival relationship already exists
        const existingRival = await SocialRepository.findExistingRival(userId, rivalId);

        if (existingRival) {
            return { error: "This user is already on your rivals list", statusCode: 400 };
        }

        // Cannot add yourself as rival
        if (userId === rivalId) {
            return { error: "You cannot add yourself as a rival", statusCode: 400 };
        }

        // Check if users are friends
        const existingFriendship = await SocialRepository.findExistingFriendship(userId, rivalId);

        if (existingFriendship) {
            return { error: "You cannot add a friend as a rival", statusCode: 400 };
        }

        // Check if user exists
        const targetUser = await UserRepository.checkUserExists(rivalId);

        if (!targetUser) {
            return { error: "User not found", statusCode: 404 };
        }

        // Create the rival relationship
        const rival = await SocialRepository.createRival(userId, rivalId);
        return { data: rival };
    } catch (error) {
        LogErrorStack({ message: "Error adding rival", error });
        return { error: "Failed to add rival", statusCode: 500 };
    }
};

/**
 * Remove a rival
 */
export const removeRival = async (userId: number, rivalId: number) => {
    try {
        // Check if the rival relationship exists
        const existingRival = await SocialRepository.findExistingRival(userId, rivalId);

        if (!existingRival) {
            return { error: "Rival not found", statusCode: 404 };
        }

        // Delete the rival relationship
        await SocialRepository.deleteRival(existingRival.id);
        return { data: { removed: true } };
    } catch (error) {
        LogErrorStack({ message: "Error removing rival", error });
        return { error: "Failed to remove rival", statusCode: 500 };
    }
};

/**
 * Update a note on a rival
 */
export const updateRivalNote = async (userId: number, rivalId: number, note: string | null) => {
    try {
        const rivalRelationship = await SocialRepository.findExistingRival(userId, rivalId);

        if (!rivalRelationship) {
            return { error: "Rival not found", statusCode: 404 };
        }

        const updatedRival = await SocialRepository.updateRivalNote(rivalRelationship.id, note);
        return { data: updatedRival };
    } catch (error) {
        LogErrorStack({ message: "Error updating rival note", error });
        return { error: "Failed to update rival note", statusCode: 500 };
    }
};
