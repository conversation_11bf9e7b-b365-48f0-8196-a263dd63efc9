import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from "../chat/chat.helpers.js";
import * as ShrineRepository from "../../repositories/shrine.repository.js";
import { getToday } from "../../utils/dateHelpers.js";
import { logger } from "../../utils/log.js";
const PRIMARY_BUFFS = [
    {
        buffType: "apRegen",
        description: "40% chance to regen 2 Action Points instead of 1",
        value: 1,
    },
    {
        buffType: "trainingCost",
        description: "Reduces energy training cost by 3",
        value: 3,
    },
    {
        buffType: "rareDrops",
        description: "Increases rare item drop chance by 40%",
        value: 1.4,
    },
    {
        buffType: "craftSpeed",
        description: "Reduces crafting time of all items by 40%",
        value: 0.6,
    },
];
const SECONDARY_BUFFS = [
    {
        buffType: "damage",
        description: "Increases all damage by 10%",
        value: 1.1,
    },
    {
        buffType: "exp",
        description: "Increases all EXP received by 15%",
        value: 1.15,
    },
    {
        buffType: "armour",
        description: "Increases armour by 20%",
        value: 1.2,
    },
    {
        buffType: "jail",
        description: "Reduces jail time by 50%",
        value: 0.5,
    },
    {
        buffType: "mission",
        description: "Reduces mission durations by 50%",
        value: 0.5,
    },
];
export const isTodaysDonationGoalReached = async () => {
    const today = getToday();
    const dailyShrineGoal = await ShrineRepository.findDailyShrineGoalWithReached(today);
    if (!dailyShrineGoal) {
        return false;
    }
    return dailyShrineGoal;
};
export const dailyBuffIsActive = async (type) => {
    const dailyShrine = await isTodaysDonationGoalReached();
    if (dailyShrine && dailyShrine.buffRewards) {
        const parsedRewards = dailyShrine.buffRewards;
        if (parsedRewards[type]) {
            return parsedRewards[type].value;
        }
    }
    return null;
};
export const dailyShrineDonations = async (date) => {
    return await ShrineRepository.getDailyDonations(date);
};
export const getDailyShrineGoal = async (date) => {
    return await ShrineRepository.findDailyShrineGoal(date);
};
export const addToDailyShrineGoal = async (user, amount) => {
    const today = getToday();
    const dailyShrineGoal = await getDailyShrineGoal(today);
    if (!dailyShrineGoal) {
        return false;
    }
    dailyShrineGoal.donationAmount += amount;
    const existingDonation = await ShrineRepository.findUserDonationForDate(user.id, today);
    if (existingDonation) {
        await ShrineRepository.updateDonation(existingDonation.id, existingDonation.amount + amount);
    }
    else {
        await ShrineRepository.createDonation(user.id, today, amount);
    }
    if (!dailyShrineGoal.goalReached && dailyShrineGoal.donationAmount >= dailyShrineGoal.donationGoal) {
        dailyShrineGoal.goalReached = true;
        await ChatHelper.SendAnnouncementMessage("shrineGoalReached", `The Shrine daily donation goal has been reached. Global buffs are now active!`);
    }
    return await ShrineRepository.updateShrineGoal(dailyShrineGoal.id, {
        donationAmount: dailyShrineGoal.donationAmount,
        goalReached: dailyShrineGoal.goalReached,
    });
};
export const getRandomDailyBuffs = () => {
    const primary = PRIMARY_BUFFS[Math.floor(Math.random() * PRIMARY_BUFFS.length)];
    const secondary = SECONDARY_BUFFS[Math.floor(Math.random() * SECONDARY_BUFFS.length)];
    return { [primary.buffType]: primary, [secondary.buffType]: secondary };
};
export const announceDonationGoalReset = async (donationGoal) => {
    try {
        await ChatHelper.SendAnnouncementMessage("shrineGoalReset", JSON.stringify({ goal: donationGoal }));
    }
    catch (error) {
        logger.error(`Failed to send chat message: ${error}`);
    }
};
