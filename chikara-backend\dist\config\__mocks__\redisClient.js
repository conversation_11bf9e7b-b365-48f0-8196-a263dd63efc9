import { vi } from "vitest";
const mockConnect = vi.fn().mockResolvedValue();
const mockDisconnect = vi.fn().mockResolvedValue();
const redisClient = {
    connect: mockConnect,
    disconnect: mockDisconnect,
    close: vi.fn().mockResolvedValue("OK"),
    isOpen: true,
    isReady: true,
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue("OK"),
    setEx: vi.fn().mockResolvedValue("OK"),
    del: vi.fn().mockResolvedValue(1),
    keys: vi.fn().mockResolvedValue([]),
    publish: vi.fn().mockResolvedValue(0),
    subscribe: vi.fn().mockResolvedValue(),
    unsubscribe: vi.fn().mockResolvedValue(),
    on: vi.fn(),
    off: vi.fn(),
    EXISTS: vi.fn().mockResolvedValue(0),
    PING: vi.fn().mockResolvedValue("PONG"),
    HGETALL: vi.fn().mockResolvedValue({}),
    HSET: vi.fn().mockResolvedValue(1),
    hGet: vi.fn().mockResolvedValue(null),
    hGetAll: vi.fn().mockResolvedValue({}),
    hSet: vi.fn().mockResolvedValue(1),
    hDel: vi.fn().mockResolvedValue(1),
    _commandsExecuted: {},
};
const redisOptions = {
    host: "localhost",
    port: 6379,
};
export { redisClient, redisOptions, mockConnect, mockDisconnect };
