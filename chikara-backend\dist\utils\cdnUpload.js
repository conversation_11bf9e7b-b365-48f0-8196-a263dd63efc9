import { LogErrorStack } from "./log.js";
import { DeleteObjectCommand, PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
const R2_CONFIG = {
    region: "auto",
    endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
    credentials: {
        accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
        secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
    },
};
const s3Client = new S3Client(R2_CONFIG);
const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET_NAME;
export const saveToCDN = async (image, path, previousFile) => {
    try {
        const uploadParams = {
            Bucket: BUCKET_NAME,
            Key: path,
            Body: image,
            ContentType: "image/webp",
            CacheControl: "public, max-age=31536000",
        };
        await s3Client.send(new PutObjectCommand(uploadParams));
        if (previousFile) {
            try {
                await s3Client.send(new DeleteObjectCommand({
                    Bucket: BUCKET_NAME,
                    Key: previousFile,
                }));
            }
            catch (error) {
                LogErrorStack({ message: `Failed to delete old file ${previousFile}: ${error}`, error });
            }
        }
        return path;
    }
    catch (error) {
        LogErrorStack({ message: "Failed to save image to R2:", error });
        return false;
    }
};
