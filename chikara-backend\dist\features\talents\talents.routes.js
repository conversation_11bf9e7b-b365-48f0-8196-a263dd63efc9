import express from "express";
import * as TalentsController from "./talents.controller.js";
import { equipAbilitySchema, unequipAbilitySchema, unlockTalentSchema } from "./talents.validation.js";
import authHelper from "../../middleware/authMiddleware.js";
import validate from "../../middleware/validate.js";
import routeHandler from "../../utils/routeHandler.js";
const router = express.Router();
router.get("/list", authHelper.IsLoggedIn, routeHandler(async () => {
    return await TalentsController.getTalents();
}));
router.get("/unlocked", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await TalentsController.getUnlockedTalents(req.user.id);
}));
router.post("/unlock", authHelper.IsLoggedInAndCanMakeStateChanges, validate(unlockTalentSchema), routeHandler(async (req) => {
    const { talentId } = req.body;
    return await TalentsController.unlock(req.user.id, talentId);
}));
router.post("/equipAbility", authHelper.IsLoggedInAndCanMakeStateChanges, validate(equipAbilitySchema), routeHandler(async (req) => {
    const { talentId, slot } = req.body;
    return await TalentsController.equipAbility(req.user.id, talentId, slot);
}));
router.post("/unequipAbility", authHelper.IsLoggedInAndCanMakeStateChanges, validate(unequipAbilitySchema), routeHandler(async (req) => {
    return await TalentsController.unequipAbility(req.user.id, req.body.slot);
}));
router.post("/reset", authHelper.IsLoggedInAndCanMakeStateChanges, routeHandler(async (req) => {
    return await TalentsController.resetTalents(req.user.id);
}));
export default router;
