import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as CourseController from "./course.controller.js";
import courseSchema from "./course.validation.js";
export const courseRouter = {
    list: isLoggedInAuth.input(courseSchema.courseList).handler(async ({ context }) => {
        const result = await CourseController.courseList(context.user.id);
        return handleResponse(result);
    }),
    start: canMakeStateChangesAuth.input(courseSchema.courseStart).handler(async ({ input, context }) => {
        const result = await CourseController.startCourse(context.user.id, input.courseId);
        return handleResponse(result);
    }),
};
export default courseRouter;
