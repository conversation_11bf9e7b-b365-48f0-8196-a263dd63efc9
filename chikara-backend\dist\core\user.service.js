import gameConfig from "../config/gameConfig.js";
import * as InventoryService from "./inventory.service.js";
import * as NotificationService from "./notification.service.js";
import { referralRewards } from "../data/referralRewards.js";
import * as ChatHelper from "../features/chat/chat.helpers.js";
import * as ShrineHelper from "../features/shrine/shrine.helpers.js";
import { logAction } from "../lib/actionLogger.js";
import { db } from "../lib/db.js";
import { expiryQueue } from "../queues/expiryValues/queue.js";
import { NotificationTypes } from "../types/notification.js";
import { LogErrorStack, logger } from "../utils/log.js";
import * as UserRepository from "../repositories/user.repository.js";
const { XP_TO_LEVEL_MULTIPLIER, MAX_LEVEL_CAP, TALENTS_LEVEL_GATE } = gameConfig;
export const updateUser = async (userId, updateData) => {
    return await db.user.update({
        where: { id: userId },
        data: updateData,
    });
};
export const getXpForNextLevel = (user) => {
    const level = user.level || 1;
    if (level >= 25) {
        return Math.floor(level * (XP_TO_LEVEL_MULTIPLIER * (level / 16)));
    }
    if (level >= 30) {
        return Math.floor(level * (XP_TO_LEVEL_MULTIPLIER * (level / 12)));
    }
    if (level >= 35) {
        return Math.floor(level * (XP_TO_LEVEL_MULTIPLIER * (level / 8)));
    }
    return Math.floor(level * XP_TO_LEVEL_MULTIPLIER);
};
export const processReferralRewards = async (user, newLevel) => {
    if (!user.referrerId)
        return;
    const rewardTier = `level${newLevel}`;
    if (!(rewardTier in referralRewards))
        return;
    const rewards = referralRewards[rewardTier].rewards;
    const referrer = await UserRepository.getUserById(user.referrerId);
    if (!referrer)
        return;
    for (const reward of rewards) {
        await InventoryService.AddItemToUser({
            userId: referrer.id,
            itemId: reward.itemId,
            amount: reward.quantity,
            isTradeable: true,
        });
    }
    await NotificationService.NotifyUser(referrer.id, NotificationTypes.referral_reward, {
        title: "Referral Reward",
        message: `You received rewards because ${user.username} reached level ${newLevel}!`,
        items: rewards,
    });
    logAction({
        action: "REFERRAL_REWARD",
        userId: referrer.id,
        info: {
            referreeId: user.id,
            level: newLevel,
            rewards,
        },
    });
};
export const processLevelUps = (user, currentState) => {
    let nextLevelXpReq = getXpForNextLevel({ ...user, level: currentState.level });
    const updatedState = { ...currentState };
    while (updatedState.xp >= nextLevelXpReq && updatedState.level < MAX_LEVEL_CAP) {
        updatedState.level++;
        if (updatedState.level >= TALENTS_LEVEL_GATE) {
            updatedState.talentPoints++;
        }
        updatedState.health += 50;
        updatedState.currentHealth += 50;
        updatedState.xp -= nextLevelXpReq;
        nextLevelXpReq = getXpForNextLevel({ ...user, level: updatedState.level });
        NotificationService.NotifyUser(user.id, NotificationTypes.levelup, { newLevel: updatedState.level });
        processReferralRewards(user, updatedState.level).catch((error) => {
            LogErrorStack({ message: "Error processing referral rewards:", error });
        });
        if (updatedState.level === MAX_LEVEL_CAP) {
            ChatHelper.SendAnnouncementMessage("levelCapReached", JSON.stringify({ username: user.username, id: user.id }));
        }
        logAction({
            action: "LEVEL_UP",
            userId: user.id,
            info: {
                newLevel: updatedState.level,
            },
        });
    }
    return updatedState;
};
export const AddXPToUser = async (user, xp) => {
    const shrineBuffActive = await ShrineHelper.dailyBuffIsActive("exp");
    let adjustedXp = xp;
    if (shrineBuffActive) {
        adjustedXp *= shrineBuffActive;
    }
    adjustedXp = Math.round(adjustedXp);
    const initialState = {
        xp: user.xp,
        level: user.level,
        talentPoints: user.talentPoints,
        health: user.health,
        currentHealth: user.currentHealth,
    };
    const currentState = {
        xp: initialState.xp + adjustedXp,
        level: initialState.level,
        talentPoints: initialState.talentPoints,
        health: initialState.health,
        currentHealth: initialState.currentHealth,
    };
    if (currentState.level >= MAX_LEVEL_CAP) {
        await updateUser(user.id, { xp: currentState.xp });
        return adjustedXp;
    }
    const updatedState = processLevelUps(user, currentState);
    const updates = {
        xp: updatedState.xp,
    };
    if (updatedState.level !== initialState.level) {
        updates.level = updatedState.level;
        updates.health = updatedState.health;
        updates.currentHealth = updatedState.currentHealth;
    }
    if (updatedState.talentPoints !== initialState.talentPoints) {
        updates.talentPoints = updatedState.talentPoints;
    }
    await updateUser(user.id, updates);
    return adjustedXp;
};
export const JailUser = async (userId, duration, reason, notificationDetails) => {
    const jailEndTime = Date.now() + duration;
    const user = await db.user.update({
        where: { id: userId },
        data: { jailedUntil: BigInt(jailEndTime), jailReason: reason },
    });
    if (!user.jailedUntil) {
        logger.warn(`User ${userId} was not jailed correctly`);
        return user;
    }
    const expiryTime = user.jailedUntil.toString();
    await expiryQueue.add("jail-expiry", {
        userId: userId,
        action: "jailedUntil",
        expiryTime,
    }, {
        delay: duration,
        jobId: `jail-${userId}-${expiryTime}`,
        attempts: 3,
        removeOnComplete: true,
        removeOnFail: false,
    });
    if (notificationDetails) {
        NotificationService.NotifyUser(userId, notificationDetails.notificationType || NotificationTypes.jail, {
            reason,
            jailedUntil: jailEndTime,
            ...notificationDetails,
        });
    }
    return user;
};
