import { db } from "../lib/db.js";
export const getPetById = async (petId) => {
    return await db.pet.findUnique({
        where: { id: petId },
    });
};
export async function findUserWithPets(userId) {
    return await db.user.findUnique({
        where: { id: userId },
        include: { user_pet: true },
    });
}
export const createUserPet = async (createPetData) => {
    return await db.user_pet.create({
        data: createPetData,
    });
};
export const getUserPets = async (userId) => {
    return await db.user_pet.findMany({
        where: { userId },
    });
};
export async function getUserPetsWithPetData(userId) {
    return await db.user_pet
        .findMany({
        where: { userId },
        include: { pet: true },
    })
        .then((userPets) => userPets.map((userPet) => {
        const pet = userPet.pet;
        const currentStage = userPet.evolution?.current || "egg";
        const evolutionStage = pet?.evolution_stages?.find((stage) => stage.stage === currentStage);
        return {
            ...userPet,
            image: evolutionStage?.image,
            buffs: evolutionStage?.buffs,
            species: pet?.species,
            maxLevel: pet?.maxLevel,
            name: userPet.name ?? pet?.name,
            evolution: userPet.evolution,
        };
    }));
}
export async function getPets() {
    return await db.pet.findMany();
}
export async function updatePetEnergyAndHappiness(petId, energy, happiness) {
    return await db.user_pet.update({
        where: { id: petId },
        data: {
            energy: Math.min(energy, 100),
            happiness: Math.min(happiness, 100),
        },
    });
}
export async function updatePetHappiness(petId, happiness) {
    return await db.user_pet.update({
        where: { id: petId },
        data: {
            happiness: Math.min(happiness, 100),
        },
    });
}
export async function updatePetLevel(petId, level) {
    return await db.user_pet.update({
        where: { id: petId },
        data: {
            level,
        },
    });
}
export async function updatePetXp(petId, xp, nextLevelXp) {
    return await db.user_pet.update({
        where: { id: petId },
        data: {
            xp,
            nextLevelXp,
        },
    });
}
export async function updatePetLevelAndXp(petId, level, xp, nextLevelXp) {
    return await db.user_pet.update({
        where: { id: petId },
        data: {
            level,
            xp,
            nextLevelXp,
        },
    });
}
export async function updatePetEvolution(userPetId, evolution) {
    return await db.user_pet.update({
        where: { id: userPetId },
        data: {
            evolution,
        },
    });
}
export async function updatePetName(userPetId, name) {
    return await db.user_pet.update({
        where: { id: userPetId },
        data: { name },
        include: { pet: true },
    });
}
export async function evolveUserPet(userPetId, evolutionData) {
    return await db.user_pet.update({
        where: { id: userPetId },
        data: {
            level: 1,
            happiness: 50,
            xp: 0,
            nextLevelXp: 100,
            evolution: evolutionData,
        },
        include: { pet: true },
    });
}
export async function findUserPetByIdAndOwner(userId, userPetId) {
    return await db.user_pet.findUnique({
        where: {
            id: userPetId,
            userId,
        },
        include: { pet: true },
    });
}
export async function setActivePet(userId, userPetId) {
    await db.user_pet.updateMany({
        where: { userId, isActive: true },
        data: { isActive: false },
    });
    return await db.user_pet.update({
        where: { id: userPetId, userId },
        data: { isActive: true },
        include: { pet: true },
    });
}
export async function updatePetBuffs(petId, buffs) {
    const pet = await db.pet.findUnique({
        where: { id: petId },
    });
    if (!pet) {
        throw new Error("Pet not found");
    }
    const evolutionStages = pet.evolution_stages || [];
    const updatedEvolutionStages = evolutionStages.map((stage) => ({
        ...stage,
        buffs,
    }));
    return await db.pet.update({
        where: { id: petId },
        data: {
            evolution_stages: updatedEvolutionStages,
        },
    });
}
export async function updatePetEvolutionProgress(userPetId, evolution) {
    return await db.user_pet.update({
        where: { id: userPetId },
        data: { evolution },
    });
}
