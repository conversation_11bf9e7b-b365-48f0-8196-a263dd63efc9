import z from "zod";
import { SkillType } from "@prisma/client";
const userInfoSchema = z.object({
    id: z.number().int().positive(),
});
export const updateAccountDetailsSchema = z.object({
    about: z.string().max(500).optional(),
    username: z.string().min(3).max(30).optional(),
    email: z.string().email().optional(),
    newPassword: z.string().min(8).optional(),
    currentPassword: z.string().optional(),
});
const equipItemSchema = z.object({
    userItemId: z.number().int().positive(),
});
const unequipItemSchema = z.object({
    slot: z.enum(["weapon", "ranged", "head", "chest", "hands", "legs", "feet", "finger", "offhand", "shield"]),
});
const useItemSchema = z.object({
    userItemId: z.number().int().positive(),
});
const trainSchema = z.object({
    stat: z.enum(["strength", "intelligence", "dexterity", "defence", "endurance", "vitality"]),
    focusAmount: z.number().int().positive(),
});
const linkDiscordSchema = z.object({
    token: z.string(),
});
const setLastNewsIDReadSchema = z.object({
    id: z.number().int().positive(),
});
const skillsQuerySchema = z.object({
    skills: z.array(z.nativeEnum(SkillType)).optional().default([]),
});
const updateProfileDetailsSchema = z.object({
    about: z.string().max(500).optional(),
    username: z.string().min(3).max(30).optional(),
    avatar: z.instanceof(File).optional(),
    banner: z.instanceof(File).optional(),
});
export default {
    userInfoSchema,
    updateAccountDetailsSchema,
    equipItemSchema,
    unequipItemSchema,
    useItemSchema,
    trainSchema,
    linkDiscordSchema,
    setLastNewsIDReadSchema,
    skillsQuerySchema,
    updateProfileDetailsSchema,
};
