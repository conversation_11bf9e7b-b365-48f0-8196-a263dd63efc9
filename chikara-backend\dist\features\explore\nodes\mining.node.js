import * as InventoryService from "../../../core/inventory.service.js";
import * as NotificationService from "../../../core/notification.service.js";
import * as StatusEffectService from "../../../core/statuseffect.service.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import { logAction } from "../../../lib/actionLogger.js";
import { db } from "../../../lib/db.js";
import { NotificationTypes } from "../../../types/notification.js";
import { mapExploreLocationToLocationTypes } from "../explore.helpers.js";
import * as ExploreRepository from "../../../repositories/explore.repository.js";
export const MINING_CONFIG = {
    FAIL_CHANCE: 0.25,
    INJURY_CHANCE: 0.7,
    SUCCESS_BONUS_CHANCE: 0.2,
    DEFAULT_ENERGY_COST: 3,
    MINING_TIMEOUT_MS: 2 * 60 * 1000,
    MINING_TYPES: ["ore", "gems", "crystals", "rare_metals"],
    INJURY_TYPES: ["cave_in", "equipment_failure", "exhaustion"],
};
export const findExploreMiningDrops = async (userLevel, location, miningType) => {
    const mappedLocation = mapExploreLocationToLocationTypes(location);
    return await db.drop_chance.findMany({
        where: {
            dropChanceType: "scavenge",
            scavengeType: miningType,
            location: {
                in: [mappedLocation, "any"],
            },
            minLevel: {
                lte: userLevel,
            },
            maxLevel: {
                gte: userLevel,
            },
            dropRate: {
                gt: 0,
            },
        },
        include: {
            item: true,
        },
        orderBy: {
            dropRate: "asc",
        },
    });
};
export const handleMiningSuccess = async (user, location, miningType, difficulty) => {
    const potentialDrops = await findExploreMiningDrops(user.level, location, miningType);
    if (!potentialDrops || potentialDrops.length === 0) {
        return null;
    }
    const randomIndex = Math.floor(Math.random() * potentialDrops.length);
    const selectedItem = potentialDrops[randomIndex];
    if (selectedItem.itemId === null || !selectedItem.item) {
        return null;
    }
    const difficultyMultiplier = difficulty === "easy" ? 1 : difficulty === "medium" ? 1.5 : 2;
    const baseQuantity = selectedItem.quantity;
    const finalQuantity = Math.max(1, Math.floor(baseQuantity * difficultyMultiplier));
    await InventoryService.AddItemToUser({
        userId: user.id,
        itemId: selectedItem.itemId,
        amount: finalQuantity,
        isTradeable: true,
    });
    const baseExp = 15;
    const diffExp = difficulty === "easy" ? 1 : difficulty === "medium" ? 1.5 : 2;
    const experienceGained = Math.floor(baseExp * diffExp);
    let bonusResult = null;
    if (Math.random() <= MINING_CONFIG.SUCCESS_BONUS_CHANCE) {
        const bonusItem = potentialDrops[Math.floor(Math.random() * potentialDrops.length)];
        if (bonusItem.itemId && bonusItem.item) {
            const bonusQuantity = Math.max(1, Math.floor(bonusItem.quantity * 0.5));
            await InventoryService.AddItemToUser({
                userId: user.id,
                itemId: bonusItem.itemId,
                amount: bonusQuantity,
                isTradeable: true,
            });
            bonusResult = {
                bonusReward: bonusItem.item,
                bonusQuantity,
            };
        }
    }
    logAction({
        action: "EXPLORE_MINING_SUCCESS",
        userId: user.id,
        info: {
            itemId: selectedItem.item.id,
            itemName: selectedItem.item.name,
            quantity: finalQuantity,
            location: location,
            miningType: miningType,
            difficulty: difficulty,
            experienceGained,
            bonusReward: bonusResult?.bonusReward?.name || null,
        },
    });
    return {
        itemReward: selectedItem.item,
        itemQuantity: finalQuantity,
        experienceGained,
        ...bonusResult,
    };
};
export const handleMiningFailure = async (currentUser, location, miningType, difficulty) => {
    const injuryTypes = ["Physical", "Mental"];
    const injuryType = injuryTypes[Math.floor(Math.random() * injuryTypes.length)];
    const injury = await StatusEffectService.GetRandomInjury("Minor", injuryType);
    if (injury) {
        await StatusEffectService.ApplyStatusEffectToUser(currentUser, injury);
        NotificationService.NotifyUser(currentUser.id, NotificationTypes.injured, {
            reason: "mining accident",
            injury: injury.name,
            injuryTier: injury.tier,
        }, true);
        logAction({
            action: "EXPLORE_MINING_INJURY",
            userId: currentUser.id,
            info: {
                injury: injury.name,
                injuryTier: injury.tier,
                location: location,
                miningType: miningType,
                difficulty: difficulty,
            },
        });
        return injury;
    }
    return null;
};
export const determineMiningDifficulty = (location, userLevel) => {
    const locationDifficulty = {
        shibuya: "easy",
        shinjuku: "medium",
        bunkyo: "easy",
        chiyoda: "hard",
        minato: "medium",
    };
    const baseDifficulty = locationDifficulty[location] || "easy";
    if (userLevel >= 30) {
        return baseDifficulty === "easy" ? "medium" : baseDifficulty === "medium" ? "hard" : "hard";
    }
    else if (userLevel >= 15) {
        return baseDifficulty === "easy" ? "easy" : baseDifficulty === "medium" ? "medium" : "medium";
    }
    return "easy";
};
export const selectMiningType = (location) => {
    const locationMiningTypes = {
        shibuya: ["ore", "crystals"],
        shinjuku: ["ore", "rare_metals"],
        bunkyo: ["gems", "crystals"],
        chiyoda: ["rare_metals", "gems"],
        minato: ["ore", "gems"],
    };
    const availableTypes = locationMiningTypes[location] || ["ore"];
    return availableTypes[Math.floor(Math.random() * availableTypes.length)];
};
export const initializeMining = async (userId, location) => {
    const user = await UserRepository.getUserById(userId);
    if (!user) {
        throw new Error("User not found");
    }
    const miningType = selectMiningType(location);
    const difficulty = determineMiningDifficulty(location, user.level);
    const energyCost = MINING_CONFIG.DEFAULT_ENERGY_COST * (difficulty === "easy" ? 1 : difficulty === "medium" ? 1.5 : 2);
    const miningValidUntil = Date.now() + MINING_CONFIG.MINING_TIMEOUT_MS;
    logAction({
        action: "EXPLORE_MINING_INITIATED",
        userId: userId,
        info: {
            location: location,
            miningType: miningType,
            difficulty: difficulty,
            energyCost: energyCost,
        },
    });
    return {
        miningType,
        difficulty,
        energyCost,
        miningValidUntil,
    };
};
export const processMining = async (userId, nodeId, location, miningType, difficulty) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return {
            success: false,
            message: "User not found",
        };
    }
    const goodOutcome = Math.random() > MINING_CONFIG.FAIL_CHANCE;
    const result = {
        success: goodOutcome,
        miningType,
    };
    if (goodOutcome) {
        const miningResult = await handleMiningSuccess(currentUser, location, miningType, difficulty);
        if (miningResult) {
            result.itemReward = miningResult.itemReward;
            result.itemQuantity = miningResult.itemQuantity;
            result.experienceGained = miningResult.experienceGained;
            result.bonusReward = miningResult.bonusReward;
            result.bonusQuantity = miningResult.bonusQuantity;
            if (result.bonusReward) {
                result.message = `You successfully mined ${result.itemQuantity} ${result.itemReward?.name} and found a bonus of ${result.bonusQuantity} ${result.bonusReward.name}!`;
            }
            else {
                result.message = `You successfully mined ${result.itemQuantity} ${result.itemReward?.name}!`;
            }
        }
        else {
            result.message = "You mined the area thoroughly but didn't find any valuable materials.";
        }
    }
    else {
        const failureResult = await handleMiningFailure(currentUser, location, miningType, difficulty);
        result.injury = failureResult ?? undefined;
        if (failureResult) {
            result.message = "A mining accident occurred! You've been injured in the process.";
        }
        else {
            result.message = "You mined the area thoroughly but didn't find any valuable materials.";
        }
    }
    return {
        success: true,
        message: "Mining operation completed",
        data: result,
    };
};
export const processMiningOperation = async (userId, nodeId, location, miningType, difficulty) => {
    return await processMining(userId, nodeId, location, miningType, difficulty);
};
export const handleMiningEncounter = async (userId, nodeId, location) => {
    try {
        const miningOperation = await initializeMining(userId, location);
        const metadataUpdated = await ExploreRepository.updatePlayerNodeMetadata(nodeId, userId, {
            miningType: miningOperation.miningType,
            difficulty: miningOperation.difficulty,
            energyCost: miningOperation.energyCost,
            miningValidUntil: miningOperation.miningValidUntil,
        });
        if (!metadataUpdated) {
            return {
                success: false,
                message: "Failed to initialize mining operation",
            };
        }
        return {
            success: true,
            message: "Mining site prepared. Click 'Mine Ore' to begin extraction.",
            data: {
                action: "mining_ready",
                nodeId,
                miningType: miningOperation.miningType,
                difficulty: miningOperation.difficulty,
                energyCost: miningOperation.energyCost,
                estimatedReward: miningOperation.difficulty === "easy"
                    ? "low"
                    : miningOperation.difficulty === "medium"
                        ? "medium"
                        : "high",
            },
        };
    }
    catch (error) {
        logAction({
            action: "EXPLORE_MINING_ERROR",
            userId: userId,
            info: {
                nodeId,
                location,
                error: error instanceof Error ? error.message : "Unknown error",
            },
        });
        return {
            success: false,
            message: "Failed to prepare mining site",
        };
    }
};
