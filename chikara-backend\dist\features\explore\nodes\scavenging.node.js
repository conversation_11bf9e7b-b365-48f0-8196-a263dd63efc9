import * as InventoryService from "../../../core/inventory.service.js";
import * as NotificationService from "../../../core/notification.service.js";
import * as StatusEffectService from "../../../core/statuseffect.service.js";
import * as UserService from "../../../core/user.service.js";
import getScavengeLocation from "../../../data/scavengeLocations.js";
import * as ShrineHelper from "../../shrine/shrine.helpers.js";
import { logAction } from "../../../lib/actionLogger.js";
import { db } from "../../../lib/db.js";
import { NotificationTypes } from "../../../types/notification.js";
import { mapExploreLocationToLocationTypes } from "../explore.helpers.js";
import * as ExploreRepository from "../../../repositories/explore.repository.js";
import * as UserRepository from "../../../repositories/user.repository.js";
export const SCAVENGING_CONFIG = {
    FAIL_CHANCE: 0.3,
    JAIL_CHANCE: 0.2,
    DEFAULT_JAIL_DURATION_MS: 10 * 60 * 1000,
    SCAVENGE_CHOICES: ["trash", "medical", "upgrade", "herb", "tech", "ore"],
    SCAVENGE_TIMEOUT_MS: 5 * 60 * 1000,
};
export const findExploreScavengeDrops = async (userLevel, location, scavengeType) => {
    const mappedLocation = mapExploreLocationToLocationTypes(location);
    return await db.drop_chance.findMany({
        where: {
            dropChanceType: "scavenge",
            scavengeType,
            location: {
                in: [mappedLocation, "any"],
            },
            minLevel: {
                lte: userLevel,
            },
            maxLevel: {
                gte: userLevel,
            },
            dropRate: {
                gt: 0,
            },
        },
        include: {
            item: {
                omit: {
                    createdAt: true,
                    updatedAt: true,
                },
            },
        },
        orderBy: {
            dropRate: "asc",
        },
    });
};
export const handleScavengeSuccess = async (user, location, choice) => {
    const potentialDrops = await findExploreScavengeDrops(user.level, location, choice);
    if (!potentialDrops || potentialDrops.length === 0) {
        return null;
    }
    const randomIndex = Math.floor(Math.random() * potentialDrops.length);
    const selectedItem = potentialDrops[randomIndex];
    if (selectedItem.itemId === null || !selectedItem.item) {
        return null;
    }
    await InventoryService.AddItemToUser({
        userId: user.id,
        itemId: selectedItem.itemId,
        amount: selectedItem.quantity,
        isTradeable: true,
    });
    logAction({
        action: "EXPLORE_SCAVENGE_ITEM",
        userId: user.id,
        info: {
            itemId: selectedItem.item.id,
            itemName: selectedItem.item.name,
            quantity: selectedItem.quantity,
            location: location,
            choice: choice,
        },
    });
    return {
        itemReward: selectedItem.item,
        itemQuantity: selectedItem.quantity,
    };
};
export const handleScavengeFailure = async (currentUser, location, choice, scavengeChoices) => {
    const outcome = {};
    if (Math.random() <= SCAVENGING_CONFIG.JAIL_CHANCE) {
        const jailShrineBuffActive = (await ShrineHelper.dailyBuffIsActive("jail")) || 1;
        const jailDuration = SCAVENGING_CONFIG.DEFAULT_JAIL_DURATION_MS * jailShrineBuffActive;
        await UserService.JailUser(currentUser.id, jailDuration, "Caught scavenging", {
            notificationType: "jail",
        });
        outcome.jailed = true;
        outcome.jailDuration = jailDuration;
        logAction({
            action: "EXPLORE_SCAVENGE_JAILED",
            userId: currentUser.id,
            info: {
                jailDuration: jailDuration,
                location: location,
                choice: choice,
            },
        });
        NotificationService.NotifyUser(currentUser.id, NotificationTypes.jail, {
            reason: "scavenging",
            duration: jailDuration,
        }, true);
    }
    else {
        const scavengeData = getScavengeLocation(scavengeChoices);
        if (!scavengeData.choices[choice]) {
            throw new Error(`Invalid scavenge choice: ${choice}`);
        }
        const injuryType = scavengeData.choices[choice].injury;
        const injury = await StatusEffectService.GetRandomInjury("Minor", injuryType);
        if (!injury) {
            throw new Error(`No minor injury found for type: ${injuryType}`);
        }
        await StatusEffectService.ApplyStatusEffectToUser(currentUser, injury);
        outcome.injury = injury;
        NotificationService.NotifyUser(currentUser.id, NotificationTypes.injured, {
            reason: "scavenging",
            injury: injury.name,
            injuryTier: injury.tier,
        }, true);
        logAction({
            action: "EXPLORE_SCAVENGE_INJURY",
            userId: currentUser.id,
            info: {
                injury: injury.name,
                injuryTier: injury.tier,
                location: location,
                choice: choice,
            },
        });
    }
    return outcome;
};
export const initializeScavenging = async (userId, location) => {
    const scavengeChoices = [...SCAVENGING_CONFIG.SCAVENGE_CHOICES];
    const scavengeChoice1Index = Math.floor(Math.random() * scavengeChoices.length);
    const scavengeChoice1 = scavengeChoices[scavengeChoice1Index];
    scavengeChoices.splice(scavengeChoice1Index, 1);
    const scavengeChoice2 = scavengeChoices[Math.floor(Math.random() * scavengeChoices.length)];
    const choices = [scavengeChoice1, scavengeChoice2];
    const scavengeValidUntil = Date.now() + SCAVENGING_CONFIG.SCAVENGE_TIMEOUT_MS;
    const scavengeData = getScavengeLocation(choices);
    logAction({
        action: "EXPLORE_SCAVENGE_INITIATED",
        userId: userId,
        info: {
            location: location,
            choices: choices,
            scavengeLocation: scavengeData.location,
        },
    });
    return {
        choices,
        scavengeValidUntil,
        scavengeData,
    };
};
export const processScavengeChoice = async (userId, nodeId, location, choiceIndex, currentChoices) => {
    if (choiceIndex !== 1 && choiceIndex !== 2) {
        return {
            success: false,
            message: "Invalid choice. Please select 1 or 2.",
        };
    }
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return {
            success: false,
            message: "User not found",
        };
    }
    if (!currentChoices || currentChoices.length < 2) {
        return {
            success: false,
            message: "No scavenging choices available",
        };
    }
    const selectedChoice = currentChoices[choiceIndex - 1];
    const goodOutcome = Math.random() > SCAVENGING_CONFIG.FAIL_CHANCE;
    const scavengeData = getScavengeLocation(currentChoices);
    const result = {
        success: goodOutcome,
        choice: selectedChoice,
        scavengeData,
    };
    if (goodOutcome) {
        const itemResult = await handleScavengeSuccess(currentUser, location, selectedChoice);
        if (itemResult) {
            result.itemReward = itemResult.itemReward;
            result.itemQuantity = itemResult.itemQuantity;
            result.message = scavengeData.choices[selectedChoice].success;
        }
        else {
            result.message = "You searched thoroughly but didn't find anything useful.";
        }
    }
    else {
        const failureResult = await handleScavengeFailure(currentUser, location, selectedChoice, currentChoices);
        result.jailed = failureResult.jailed;
        result.jailDuration = failureResult.jailDuration;
        result.injury = failureResult.injury;
        if (failureResult.jailed) {
            result.message = scavengeData.choices[selectedChoice].failureJail;
        }
        else {
            result.message = scavengeData.choices[selectedChoice].failureInjury;
        }
    }
    return {
        success: true,
        message: "Scavenging completed",
        data: result,
    };
};
export const handleScavengingEncounter = async (userId, nodeId, location) => {
    try {
        const scavengeChoices = await initializeScavenging(userId, location);
        const metadataUpdated = await ExploreRepository.updatePlayerNodeMetadata(nodeId, userId, {
            choices: scavengeChoices.choices,
            scavengeValidUntil: scavengeChoices.scavengeValidUntil,
            scavengeData: scavengeChoices.scavengeData,
        });
        if (!metadataUpdated) {
            return {
                success: false,
                message: "Failed to initialize scavenging",
            };
        }
        return {
            success: true,
            message: "Choose your scavenging approach",
            data: {
                action: "scavenge_choices",
                nodeId,
                ...scavengeChoices,
            },
        };
    }
    catch {
        return {
            success: false,
            message: "Failed to initialize scavenging encounter",
        };
    }
};
