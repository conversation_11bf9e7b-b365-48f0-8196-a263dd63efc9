import { db } from "../../../../lib/db.js";
import { LogErrorStack } from "../../../../utils/log.js";
async function cleanupExpiredStatusEffects() {
    try {
        const now = Date.now();
        await db.user_status_effect.deleteMany({
            where: { endsAt: { lt: now } },
        });
    }
    catch (error) {
        LogErrorStack({ message: `Error on scheduled cleanup`, error });
    }
}
export default cleanupExpiredStatusEffects;
