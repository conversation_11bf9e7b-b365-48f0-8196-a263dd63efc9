import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from "../../../../features/chat/chat.helpers.js";
import { LogErrorStack } from "../../../../utils/log.js";
export async function announceLotteryEnding() {
    try {
        await <PERSON><PERSON><PERSON><PERSON><PERSON>.SendAnnouncementMessage("lotteryEndingSoon", "The current lottery is ending in 30 minutes!");
    }
    catch (error) {
        LogErrorStack({ message: "Error announcing lottery ending soon:", error });
    }
}
