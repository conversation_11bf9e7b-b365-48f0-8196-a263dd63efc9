import { db } from "../lib/db.js";
export const getUserById = async (userId, options) => {
    return await db.user.findUnique({
        where: { id: userId },
        ...options,
    });
};
export const checkUserExists = async (userId) => {
    return await db.user.findUnique({
        where: { id: userId },
        select: { id: true },
    });
};
export const getUserWithAchievements = async (userId) => {
    return await db.user.findUnique({
        where: { id: userId },
        include: {
            user_achievements: { select: { totalMissionHours: true } },
        },
    });
};
export const getUserProfile = async (userId) => {
    const attributes = {
        id: true,
        username: true,
        about: true,
        userType: true,
        avatar: true,
        level: true,
        hospitalisedUntil: true,
        hospitalisedReason: true,
        jailedUntil: true,
        jailReason: true,
        createdAt: true,
        gangId: true,
        profileBanner: true,
        class: true,
        gang: {
            select: {
                id: true,
                name: true,
            },
        },
    };
    return await db.user.findUnique({
        where: { id: userId },
        select: attributes,
    });
};
export const findUserInventory = async (userId) => {
    const inventory = await db.user_item.findMany({
        where: { userId },
        select: {
            id: true,
            count: true,
            upgradeLevel: true,
            isTradeable: true,
            quality: true,
            item: {
                omit: {
                    createdAt: true,
                    updatedAt: true,
                },
            },
        },
    });
    return inventory;
};
export const findTradeableInventory = async (userId) => {
    return await db.user.findUnique({
        where: { id: userId },
        include: {
            user_item: {
                where: { isTradeable: true },
                select: {
                    id: true,
                    count: true,
                    upgradeLevel: true,
                    isTradeable: true,
                    quality: true,
                    item: {
                        omit: {
                            createdAt: true,
                            updatedAt: true,
                        },
                    },
                },
            },
        },
    });
};
export const getAllUsers = async () => {
    return await db.user.findMany({
        select: {
            id: true,
            username: true,
            userType: true,
            avatar: true,
            level: true,
            class: true,
            classPoints: true,
            gang: {
                select: {
                    id: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
};
export const updateUserProfile = async (userId, updateValues) => {
    return await db.user.update({
        where: { id: userId },
        data: updateValues,
    });
};
export const findUserByUsername = async (username) => {
    return await db.user.findFirst({
        where: { username },
    });
};
export const updateUserStats = async (user, statsToUpdate) => {
    const formattedStats = {};
    for (const [key, value] of Object.entries(statsToUpdate)) {
        if (key === "lastEnergyTick" && value instanceof Date) {
            formattedStats[key] = BigInt(value.getTime());
        }
        else if (key === "lastFatigueReset" && value instanceof Date) {
            formattedStats[key] = value;
        }
        else {
            formattedStats[key] = value;
        }
    }
    return await db.user.update({
        where: { id: user.id },
        data: formattedStats,
    });
};
export const findUserStatusEffects = async (userId) => {
    const now = Date.now();
    return await db.user_status_effect.findMany({
        where: {
            userId,
            endsAt: { gt: BigInt(now) },
        },
        select: {
            id: true,
            endsAt: true,
            userId: true,
            stacks: true,
            customName: true,
            effect: true,
        },
    });
};
export const createUserRecipe = async (userId, craftingRecipeId) => {
    return await db.user_recipe.create({
        data: {
            userId,
            craftingRecipeId,
        },
    });
};
export const findUserRecipe = async (userId, craftingRecipeId) => {
    return await db.user_recipe.findFirst({
        where: {
            userId,
            craftingRecipeId,
        },
    });
};
export const updateLastNewsIDRead = async (userId, newsId) => {
    return await db.user.update({
        where: { id: userId },
        data: { lastNewsIDRead: newsId },
    });
};
export const findUserByIdWithGang = async (id) => {
    return await db.user.findUnique({
        where: { id },
        include: { gang: true },
    });
};
export const findUserItemByUserIdAndItemId = async (userId, itemId, isTradeable, upgradeLevel, quality, tx) => {
    if (tx) {
        return await tx.user_item.findFirst({
            where: { userId, itemId, isTradeable, upgradeLevel, quality },
        });
    }
    return await db.user_item.findFirst({
        where: { userId, itemId, isTradeable, upgradeLevel, quality },
    });
};
export const createUserItem = async (userId, itemId, count, isTradeable = false, upgradeLevel, quality, tx) => {
    const data = {
        userId,
        itemId,
        count,
        isTradeable,
        upgradeLevel,
        quality,
    };
    if (tx) {
        return await tx.user_item.create({
            data,
            include: {
                item: true,
            },
        });
    }
    return await db.user_item.create({
        data,
        include: {
            item: true,
        },
    });
};
export const updateUserItemCount = async (userItem, newCount, tx) => {
    if (tx) {
        return await tx.user_item.update({
            where: { id: userItem.id },
            data: { count: newCount },
            include: {
                item: true,
            },
        });
    }
    return await db.user_item.update({
        where: { id: userItem.id },
        data: { count: newCount },
        include: {
            item: true,
        },
    });
};
export const findUserItemById = async (userItemId, tx) => {
    if (tx) {
        return await tx.user_item.findUnique({
            where: { id: userItemId },
            include: { item: true },
        });
    }
    return await db.user_item.findUnique({
        where: { id: userItemId },
        include: { item: true },
    });
};
export const deleteUserItem = async (userItem, tx) => {
    if (tx) {
        return await tx.user_item.delete({
            where: { id: userItem.id },
        });
    }
    return await db.user_item.delete({
        where: { id: userItem.id },
    });
};
export const findAllUserItemsByUserIdAndItemId = async (userId, itemId, isTradeable, upgradeLevel, tx) => {
    if (tx) {
        return await tx.user_item.findMany({
            where: { userId, itemId, isTradeable, upgradeLevel },
            orderBy: { count: "asc" },
        });
    }
    return await db.user_item.findMany({
        where: { userId, itemId, isTradeable, upgradeLevel },
        orderBy: { count: "asc" },
    });
};
export const findTradeableUserItems = async (userId, itemId) => {
    return await db.user_item.findMany({
        where: { userId, itemId, isTradeable: true },
    });
};
export const upsertEquippedItem = async (userId, data) => {
    return await db.equipped_item.upsert({
        where: {
            userId_slot: {
                userId,
                slot: data.slot,
            },
        },
        update: {
            userItemId: data.userItemId,
        },
        create: {
            userId,
            slot: data.slot,
            userItemId: data.userItemId,
        },
    });
};
export const findUserForChat = async (userId) => {
    return await db.user.findUnique({
        where: { id: userId },
        select: {
            id: true,
            username: true,
            avatar: true,
            userType: true,
            level: true,
            chatBannedUntil: true,
        },
    });
};
export const findUserWithMiningSkills = async (userId) => {
    return await db.user.findUnique({
        where: { id: userId },
        include: {
            user_skills: {
                where: { skillType: "mining" },
            },
        },
    });
};
export const findUserByEmail = async (email) => {
    return await db.user.findUnique({
        where: { email },
    });
};
export const findUserHealthInfo = async (userId) => {
    return await db.user.findUnique({
        where: { id: userId },
        select: {
            id: true,
            username: true,
            currentHealth: true,
            hospitalisedUntil: true,
            userType: true,
            health: true,
            jailedUntil: true,
            jailReason: true,
        },
    });
};
export const findUserWithGangInfo = async (userId) => {
    return await db.user.findUnique({
        where: { id: userId },
        select: {
            id: true,
            gangId: true,
        },
    });
};
export const updateUserCash = async (userId, newCashAmount) => {
    return await db.user.update({
        where: { id: userId },
        data: { cash: newCashAmount },
    });
};
export const incrementUserCash = async (userId, amount) => {
    if (amount <= 0) {
        throw new Error("Amount must be positive");
    }
    return await db.user.update({
        where: { id: userId },
        data: { cash: { increment: amount } },
    });
};
export const decrementUserCash = async (userId, amount) => {
    if (amount <= 0) {
        throw new Error("Amount must be positive");
    }
    return await db.user.update({
        where: { id: userId },
        data: { cash: { decrement: amount } },
    });
};
