import { uniqueItemsConfig } from "../../config/gameConfig.js";
import * as EquipmentService from "../../core/equipment.service.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import { createCache } from "../../utils/cacheHelper.js";
const { ANON_ITEM_NAME, RUNSHOES_ITEM_NAME, WEALTH_RING_ITEM_NAME } = uniqueItemsConfig.public;
const itemCache = {
    anon: createCache("24h"),
    run: createCache("24h"),
    wealth: createCache("24h"),
};
export const fetchAnonItemId = async () => {
    if (itemCache.anon.isValid()) {
        return itemCache.anon.get().data;
    }
    const item = await ItemRepository.findItemByName(ANON_ITEM_NAME);
    if (item) {
        itemCache.anon.set(item.id);
        return item.id;
    }
    return null;
};
export const fetchRunItemId = async () => {
    if (itemCache.run.isValid()) {
        return itemCache.run.get().data;
    }
    const item = await ItemRepository.findItemByName(RUNSHOES_ITEM_NAME);
    if (item) {
        itemCache.run.set(item.id);
        return item.id;
    }
    return null;
};
export const fetchWealthItemId = async () => {
    if (itemCache.wealth.isValid()) {
        return itemCache.wealth.get().data;
    }
    const item = await ItemRepository.findItemByName(WEALTH_RING_ITEM_NAME);
    if (item) {
        itemCache.wealth.set(item.id);
        return item.id;
    }
    return null;
};
export const IsAnonymousItemEquipped = async (user) => {
    const cachedItemId = await fetchAnonItemId();
    const equippedItem = await EquipmentService.GetEquippedItem(user, "head");
    if (equippedItem && equippedItem.id === cachedItemId) {
        return true;
    }
    return false;
};
export const IsRunItemEquipped = async (user) => {
    const cachedItemId = await fetchRunItemId();
    const equippedItem = await EquipmentService.GetEquippedItem(user, "feet");
    if (equippedItem && equippedItem.id === cachedItemId) {
        return true;
    }
    return false;
};
export const IsWealthItemEquipped = async (user) => {
    const cachedItemId = await fetchWealthItemId();
    const equippedItem = await EquipmentService.GetEquippedItem(user, "finger");
    if (equippedItem && equippedItem.id === cachedItemId) {
        return true;
    }
    return false;
};
