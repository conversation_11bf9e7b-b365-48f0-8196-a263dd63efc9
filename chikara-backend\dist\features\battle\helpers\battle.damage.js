import * as BattleHelpers from "./battle.helpers.js";
import * as EquipmentService from "../../../core/equipment.service.js";
import { DEFAULT_FIST_WEAPON, GetOffhandWeaponForCombatant, GetWeaponForCombatant } from "./battle.equipment.js";
import { GetToxicDartDamage } from "../logic/battle.abilities.js";
import { STUN_EFFECTS, RAGE_ABILITY_NAME, CRIPPLE_ABILITY_NAME, EXHAUST_ABILITY_NAME, HEAL_OVER_TIME_ABILITY_NAME, SLEEP_ABILITY_NAME, SELF_HARM_ABILITY_NAME, TOXIC_DART_ABILITY_NAME, DISARM_ABILITY_NAME, HIGH_GUARD_ABILITY_NAME, ATTACK_TYPE_MELEE, ATTACK_TYPE_RANGED, } from "./battle.constants.js";
import * as ShrineHelper from "../../shrine/shrine.helpers.js";
import * as <PERSON>Helper from "../../talents/talents.helpers.js";
import { getStrengthPercentageModifier, getDexterityPercentageModifier, getDefencePercentageReduction, getDefenceFlatReduction, getCriticalHitChance, getEvasionChance, getArmorPenetration, getUnavoidableForce, getDotResistance, } from "./battle.scaling.js";
const GetDamageModifier = () => {
    return Math.round((Math.random() * (1.2 - 0.8) + 0.8) * 10) / 10;
};
const applyDefenceReduction = (damage, defenceLevel, defenceModifier, armorPenetration = 0) => {
    let effectiveDefenceLevel = Math.max(1, Math.round(defenceLevel * defenceModifier));
    effectiveDefenceLevel = Math.max(1, Math.round(effectiveDefenceLevel * (1 - armorPenetration)));
    const percentageReduction = getDefencePercentageReduction(effectiveDefenceLevel);
    const flatReduction = getDefenceFlatReduction(effectiveDefenceLevel);
    let reducedDamage = damage * (1 - percentageReduction);
    reducedDamage = reducedDamage - flatReduction;
    return Math.max(1, Math.round(reducedDamage));
};
const checkCriticalHit = (dexterityLevel) => {
    const critChance = getCriticalHitChance(dexterityLevel);
    return Math.random() < critChance;
};
const checkEvasion = (enduranceLevel, attackerStrengthLevel, isMeleeAttack) => {
    let evasionChance = getEvasionChance(enduranceLevel);
    if (isMeleeAttack) {
        const unavoidableForce = getUnavoidableForce(attackerStrengthLevel);
        evasionChance *= unavoidableForce;
    }
    evasionChance = Math.max(0, Math.min(1, evasionChance));
    return Math.random() < evasionChance;
};
const calculateAttackDamage = (weaponDamage, statPercentageModifier, targetArmour, targetDefenceLevel, defenceModifier, isCriticalHit = false, armorPenetration = 0) => {
    let baseDamage = weaponDamage * statPercentageModifier;
    if (isCriticalHit) {
        baseDamage *= 2;
    }
    const mitigationFactor = 0.8;
    const effectiveArmourMitigation = targetArmour * mitigationFactor;
    const softMinDamage = baseDamage * 0.1;
    let netDamage = baseDamage - effectiveArmourMitigation + 20;
    netDamage = Math.max(softMinDamage, netDamage);
    const damageAfterRandom = Math.max(1, Math.round(netDamage * GetDamageModifier()));
    return applyDefenceReduction(damageAfterRandom, targetDefenceLevel, defenceModifier, armorPenetration);
};
export const GetDamage = async (attacker, target, attackType = ATTACK_TYPE_MELEE) => {
    const isMeleeAttack = attackType === ATTACK_TYPE_MELEE;
    const weapon = GetWeaponForCombatant(attacker, isMeleeAttack) || DEFAULT_FIST_WEAPON;
    const offhandWeapon = GetOffhandWeaponForCombatant(attacker, isMeleeAttack);
    let weaponDamage = weapon.damage ?? 1;
    weaponDamage = weaponDamage + (offhandWeapon?.damage ?? 0);
    let strModifier = 1;
    let defModifier = 1;
    let dexModifier = 1;
    if (target.userType === "npc") {
        strModifier = (attacker.buffs?.roguelikeStrBuff ?? 0) / 2;
        dexModifier = (attacker.buffs?.roguelikeDexBuff ?? 0) / 2;
    }
    else if (attacker.userType === "npc") {
        defModifier = (target.buffs?.roguelikeDefBuff ?? 0) / 2;
    }
    const attackerStatuses = attacker.statusEffects;
    const targetStatuses = target.statusEffects;
    let stunned = false;
    for (const stunEffectName of STUN_EFFECTS) {
        if (attackerStatuses[stunEffectName] &&
            attackerStatuses[stunEffectName].turns !== 3) {
            stunned = true;
        }
    }
    if (attackerStatuses[DISARM_ABILITY_NAME] &&
        attackerStatuses[DISARM_ABILITY_NAME].turns !== 3) {
        weaponDamage = DEFAULT_FIST_WEAPON.damage;
    }
    if (attackerStatuses[RAGE_ABILITY_NAME] && isMeleeAttack) {
        strModifier += (await TalentHelper.GetAllTalentModifiers(RAGE_ABILITY_NAME))[0] ?? 0;
    }
    if (attackerStatuses[SELF_HARM_ABILITY_NAME]) {
        weaponDamage *= (await TalentHelper.GetAllTalentModifiers(SELF_HARM_ABILITY_NAME))[0] ?? 1;
    }
    if (attackerStatuses[EXHAUST_ABILITY_NAME]) {
        strModifier -= (await TalentHelper.GetAllTalentModifiers(EXHAUST_ABILITY_NAME))[0] ?? 0;
    }
    if (attackerStatuses[HEAL_OVER_TIME_ABILITY_NAME]) {
        attacker.currentHealth = Math.min(attacker.maxHealth, attacker.currentHealth +
            Math.round(attacker.maxHealth *
                ((await TalentHelper.GetAllTalentModifiers(HEAL_OVER_TIME_ABILITY_NAME))[0] ?? 0)));
    }
    let poisonDamage = 0;
    if (targetStatuses[CRIPPLE_ABILITY_NAME]) {
        defModifier -= (await TalentHelper.GetAllTalentModifiers(CRIPPLE_ABILITY_NAME))[0] ?? 0;
    }
    if (targetStatuses[TOXIC_DART_ABILITY_NAME]) {
        poisonDamage += GetToxicDartDamage(target, attacker.abilities);
    }
    if (targetStatuses[SLEEP_ABILITY_NAME]) {
        delete targetStatuses[SLEEP_ABILITY_NAME];
    }
    if (stunned) {
        ++target.currentTurn;
        target.statusEffects = targetStatuses;
        return 0;
    }
    if (attacker.userType === "player") {
        const meleeDamageIncreaseTalent = await TalentHelper.UserHasMeleeDamageIncreaseTalent(Number.parseInt(attacker.id));
        if (isMeleeAttack && meleeDamageIncreaseTalent) {
            strModifier += meleeDamageIncreaseTalent.modifier ?? 0;
        }
        const berserkerTalent = await TalentHelper.UserHasBerserkerTalent(Number.parseInt(attacker.id));
        if (berserkerTalent) {
            const userPercentHp = (target.currentHealth / target.maxHealth) * 100;
            if (userPercentHp <= (berserkerTalent.modifier ?? 1)) {
                weaponDamage *= berserkerTalent.secondaryModifier ?? 1;
            }
        }
        const rangerTalent = await TalentHelper.UserHasRangerTalent(Number.parseInt(attacker.id));
        if (attackType === ATTACK_TYPE_RANGED && rangerTalent) {
            dexModifier += rangerTalent.modifier ?? 0;
        }
    }
    if (target.userType === "player") {
        const activeDefenceTalent = await TalentHelper.UserHasActiveDefenceTalent(Number.parseInt(target.id));
        if (activeDefenceTalent) {
            defModifier += activeDefenceTalent.modifier ?? 0;
        }
    }
    if (attackerStatuses["strength_buff"]) {
        strModifier += attackerStatuses["strength_buff"]?.value ?? 0;
    }
    if (attackerStatuses["dexterity_buff"]) {
        dexModifier += attackerStatuses["dexterity_buff"]?.value ?? 0;
    }
    if (targetStatuses["defence_buff"]) {
        defModifier += targetStatuses["defence_buff"]?.value ?? 0;
    }
    strModifier += BattleHelpers.processStatItemEffects(attacker, "strength", attacker.attributes.strength);
    dexModifier += BattleHelpers.processStatItemEffects(attacker, "dexterity", attacker.attributes.dexterity);
    defModifier += BattleHelpers.processStatItemEffects(target, "defence", target.attributes.defence);
    const attackerStrLevel = attacker.attributes.strength || 1;
    const attackerDexLevel = attacker.attributes.dexterity || 1;
    const targetDefLevel = target.attributes.defence || 1;
    const strPercentageModifier = getStrengthPercentageModifier(attackerStrLevel) * strModifier;
    const dexPercentageModifier = getDexterityPercentageModifier(attackerDexLevel) * dexModifier;
    const statPercentageModifier = isMeleeAttack ? strPercentageModifier : dexPercentageModifier;
    const armourShrineBuffActive = (await ShrineHelper.dailyBuffIsActive("armour")) || 1;
    const armour = target.userType === "player"
        ? (await EquipmentService.GetTotalEquippedValue(target, "armour")) * armourShrineBuffActive
        : 0;
    let finalDefModifier = defModifier;
    if (targetStatuses["defence_debuff"]) {
        finalDefModifier = defModifier * (1 - (targetStatuses["defence_debuff"].value ?? 0));
    }
    const isCriticalHit = checkCriticalHit(attackerDexLevel);
    const armorPenetration = getArmorPenetration(attackerStrLevel);
    const attackDamage = calculateAttackDamage(weaponDamage, statPercentageModifier, armour, targetDefLevel, finalDefModifier, isCriticalHit, armorPenetration);
    const damageShrineBuffActive = await ShrineHelper.dailyBuffIsActive("damage");
    let finalDamage = attackDamage + poisonDamage;
    if (target.userType === "player" && poisonDamage > 0) {
        const dotResistance = getDotResistance(targetDefLevel);
        const resistedPoisonDamage = Math.round(poisonDamage * (1 - dotResistance));
        finalDamage = attackDamage + resistedPoisonDamage;
    }
    if (attacker.userType === "player" && damageShrineBuffActive) {
        finalDamage = Math.round(finalDamage * damageShrineBuffActive);
    }
    const currentTurn = attacker.currentTurn || 1;
    if (currentTurn > 10) {
        const extraTurns = currentTurn - 10;
        finalDamage *= Math.round(Math.pow(1.3, extraTurns));
    }
    if (target.userType === "player") {
        const mitigationTalent = await TalentHelper.UserHasMitigationTalent(Number.parseInt(target.id));
        if (mitigationTalent) {
            const mitigationValue = 1 - (mitigationTalent.secondaryModifier ?? 0) * target.currentTurn;
            finalDamage = Math.round(finalDamage * Math.max(mitigationValue, mitigationTalent.modifier ?? 0));
        }
    }
    if (attackerStatuses[HIGH_GUARD_ABILITY_NAME]) {
        finalDamage *= (await TalentHelper.GetAllTalentModifiers(HIGH_GUARD_ABILITY_NAME))[0] ?? 1;
    }
    if (targetStatuses[HIGH_GUARD_ABILITY_NAME]) {
        finalDamage *= (await TalentHelper.GetAllTalentModifiers(HIGH_GUARD_ABILITY_NAME))[0] ?? 1;
    }
    attacker.statusEffects = attackerStatuses;
    ++target.currentTurn;
    target.statusEffects = targetStatuses;
    if (attacker.userType === "rooftop_npc") {
        finalDamage = BattleHelpers.applyRooftopBattleOffensivePassives(attacker, finalDamage, target);
        if (finalDamage === 0) {
            return 0;
        }
    }
    if (target.userType === "rooftop_npc") {
        finalDamage = BattleHelpers.applyRooftopBattleDefensivePassives(target, finalDamage, attacker, isMeleeAttack, poisonDamage);
    }
    if (attackerStatuses["damage_buff"]) {
        finalDamage = Math.round(finalDamage * (1 + (attackerStatuses["damage_buff"]?.value ?? 0)));
    }
    if (attackerStatuses["damage_debuff"]) {
        finalDamage = Math.round(finalDamage * (1 - (attackerStatuses["damage_debuff"]?.value ?? 0)));
    }
    if ((target.userType === "npc" || target.userType === "rooftop_npc") && attacker.userType === "player") {
        const npcDamageModifier = BattleHelpers.processNpcDamageItemEffects(attacker);
        if (npcDamageModifier > 1) {
            finalDamage = Math.round(finalDamage * npcDamageModifier);
        }
    }
    finalDamage = Math.max(1, finalDamage);
    if (target.userType === "player") {
        const targetEndLevel = target.attributes.endurance || 1;
        if (checkEvasion(targetEndLevel, attackerStrLevel, isMeleeAttack)) {
            finalDamage = 0;
        }
    }
    if (target.userType === "player") {
        if (!isMeleeAttack) {
            const deflectDamageTalent = await TalentHelper.UserHasDeflectDamageTalent(Number.parseInt(target.id));
            if (deflectDamageTalent && Math.random() < (deflectDamageTalent.modifier ?? 0)) {
                finalDamage = 0;
            }
        }
        const shadowStepTalent = await TalentHelper.UserHasShadowStepTalent(Number.parseInt(target.id));
        if (shadowStepTalent) {
            const dodgeChance = Math.max((shadowStepTalent.secondaryModifier ?? 0) +
                (shadowStepTalent.modifier ?? 0) -
                (shadowStepTalent.secondaryModifier ?? 0) * target.currentTurn, 0);
            if (Math.random() < dodgeChance) {
                finalDamage = 0;
            }
        }
    }
    if (attackerStatuses["accuracy_debuff"] && Math.random() < (attackerStatuses["accuracy_debuff"]?.value ?? 0)) {
        finalDamage = 0;
    }
    return Math.min(finalDamage, target.currentHealth);
};
