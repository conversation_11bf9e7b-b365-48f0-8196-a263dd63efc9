import { db } from "../lib/db.js";
export const findAllMessages = async (userId) => {
    return await db.private_message.findMany({
        where: {
            OR: [{ receiverId: userId }, { senderId: userId }],
            NOT: {
                AND: [{ senderId: userId }, { isGlobal: true }],
            },
        },
        orderBy: {
            createdAt: "desc",
        },
    });
};
export const countUnreadMessages = async (userId) => {
    return await db.private_message.count({
        where: {
            AND: [{ receiverId: userId }, { read: false }],
        },
    });
};
export const createMessage = async (senderId, receiverId, message) => {
    return await db.private_message.create({
        data: {
            senderId,
            receiverId,
            message,
            read: false,
        },
    });
};
export const findMessageById = async (messageId) => {
    return await db.private_message.findUnique({
        where: {
            id: messageId,
        },
    });
};
export const updateMessageRead = async (message) => {
    return await db.private_message.update({
        where: {
            id: message.id,
        },
        data: {
            read: true,
        },
    });
};
