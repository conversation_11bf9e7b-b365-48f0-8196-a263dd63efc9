import { z } from "zod";
import { ShopTypes, ShopListingCurrency } from "@prisma/client";
const purchaseItemSchema = z.object({
    id: z.number().int(),
    amount: z.number().int().positive(),
});
const sellItemSchema = z.object({
    userItemId: z.number().int(),
    amount: z.number().int().positive(),
});
const createShopSchema = z.object({
    name: z.string(),
    shopType: z.nativeEnum(ShopTypes),
    avatar: z.string().optional(),
    description: z.string(),
    disabled: z.boolean().optional(),
});
const editShopSchema = z.object({
    id: z.number().int(),
    name: z.string().optional(),
    shopType: z.nativeEnum(ShopTypes).optional(),
    avatar: z.string().optional(),
    description: z.string().optional(),
    disabled: z.boolean().optional(),
});
const deleteShopSchema = z.object({
    id: z.number().int(),
});
const createShopListingSchema = z.object({
    shopId: z.number().int(),
    itemId: z.number().int(),
    customCost: z.number().int().optional(),
    repRequired: z.number().int().optional(),
    stock: z.number().int().nullable().optional(),
    currency: z.nativeEnum(ShopListingCurrency).optional(),
});
const editShopListingSchema = z.object({
    id: z.number().int(),
    shopId: z.number().int().optional(),
    itemId: z.number().int().optional(),
    customCost: z.number().int().optional(),
    repRequired: z.number().int().optional(),
    stock: z.number().int().nullable().optional(),
    currency: z.nativeEnum(ShopListingCurrency).optional(),
});
const deleteShopListingSchema = z.object({
    id: z.number().int(),
});
export default {
    purchaseItemSchema,
    sellItemSchema,
    createShopSchema,
    editShopSchema,
    deleteShopSchema,
    createShopListingSchema,
    editShopListingSchema,
    deleteShopListingSchema,
};
