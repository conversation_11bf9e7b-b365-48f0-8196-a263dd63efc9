import { shopConfig } from "../../config/gameConfig.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as UserService from "../../core/user.service.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import * as ShopRepository from "../../repositories/shop.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { LogErrorStack } from "../../utils/log.js";
import { ItemTypes } from "@prisma/client";
import * as UserRepository from "../../repositories/user.repository.js";
const { SHOP_ITEM_COST_MULTIPLIER } = shopConfig.public;
export const shopList = async () => {
    try {
        const shops = await ShopRepository.findAllShops();
        return { data: shops };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to get shop list:", error });
        return { error: "Failed to retrieve shops" };
    }
};
export const shopInfo = async (shopId) => {
    try {
        const shop = await ShopRepository.findShopById(shopId);
        if (!shop) {
            return { error: "Shop not found" };
        }
        return { data: shop };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to get shop by ID:", error });
        return { error: "Failed to retrieve shop information" };
    }
};
export const processCurrencyDeduction = (user, shopListing, cost) => {
    const userUpdate = {};
    switch (shopListing.currency) {
        case "yen": {
            if (user.cash < cost) {
                throw new Error("Not enough cash to complete purchase.");
            }
            userUpdate.cash = user.cash - cost;
            break;
        }
        case "gangCreds": {
            if (user.gangCreds < cost) {
                throw new Error("Not enough creds to complete purchase.");
            }
            userUpdate.gangCreds = user.gangCreds - cost;
            break;
        }
        case "classPoints": {
            if (user.classPoints < cost) {
                throw new Error("Not enough points to complete purchase.");
            }
            userUpdate.classPoints = user.classPoints - cost;
            break;
        }
        default: {
            throw new Error("Invalid currency type");
        }
    }
    return userUpdate;
};
export const purchaseItem = async (listingId, amount, userId) => {
    try {
        if (!amount || !Number.isSafeInteger(amount) || amount <= 0) {
            return { error: "Invalid amount" };
        }
        const shopListing = await ShopRepository.findShopListingById(listingId);
        if (!shopListing) {
            return { error: "Shop listing not found" };
        }
        if (shopListing.shopId == null) {
            return { error: "Invalid shop listing – no shop attached" };
        }
        const shop = await ShopRepository.findShopById(shopListing.shopId);
        if (!shop || shop.disabled) {
            return { error: "Shop is closed" };
        }
        const currentUser = await UserRepository.getUserById(userId);
        if (!currentUser) {
            return { error: "User not found" };
        }
        if (shopListing.stock !== null) {
            if (shopListing.stock === 0 || shopListing.stock < amount) {
                return { error: "Not enough stock!" };
            }
            if (!currentUser.weeklyBuyLimitRemaining) {
                return { error: "Personal buy limit reached!" };
            }
            if (currentUser.weeklyBuyLimitRemaining < amount) {
                return { error: "Can't purchase this many!" };
            }
        }
        if (shopListing.itemId == null) {
            return { error: "Invalid shop listing – no item attached" };
        }
        const item = await ItemRepository.findItemById(shopListing.itemId);
        if (!item) {
            return { error: "Item not found" };
        }
        const customCost = shopListing.customCost ?? 0;
        const cashValue = item.cashValue ?? 0;
        const costPerItem = customCost > 0 ? customCost : Math.round(cashValue * SHOP_ITEM_COST_MULTIPLIER);
        const totalCost = costPerItem * amount;
        let userUpdate;
        try {
            userUpdate = processCurrencyDeduction(currentUser, shopListing, totalCost);
        }
        catch (error) {
            return { error: error.message };
        }
        if (shopListing.stock !== null) {
            await ShopRepository.updateShopListingStock(shopListing.id, shopListing.stock - amount);
            userUpdate.weeklyBuyLimitRemaining = currentUser.weeklyBuyLimitRemaining - amount;
        }
        await UserService.updateUser(currentUser.id, userUpdate);
        logAction({
            action: "SHOP_PURCHASE",
            userId,
            info: {
                itemId: shopListing.itemId,
                itemName: item.name,
                quantity: amount,
            },
        });
        await InventoryService.AddItemToUser({
            userId: currentUser.id,
            itemId: shopListing.itemId,
            amount,
            isTradeable: false,
        });
        return { data: {} };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to purchase item:", error });
        return { error: "Failed to purchase item" };
    }
};
export const sellItem = async (userItemId, amount, userId) => {
    try {
        if (!amount || !Number.isSafeInteger(amount) || amount <= 0) {
            return { error: "Invalid amount" };
        }
        const userItem = await ItemRepository.findUserItemById(userItemId, userId);
        if (!userItem || !userItem.item) {
            return { error: "Invalid item id" };
        }
        const item = userItem.item;
        if (item.itemType === ItemTypes.quest) {
            return { error: "Can't sell quest items" };
        }
        const currentUser = await UserRepository.getUserById(userId);
        if (!currentUser) {
            return { error: "User not found" };
        }
        if (!(await InventoryService.UserHasNumberOfItem(currentUser.id, item.id, amount))) {
            return { error: "You don't have enough items!" };
        }
        await InventoryService.SubtractUserItemFromUser(userItemId, amount);
        const value = (item.cashValue ?? 0) * amount;
        await UserService.updateUser(currentUser.id, { cash: currentUser.cash + value });
        logAction({
            action: "SHOP_SELL",
            userId: userId,
            info: {
                itemId: item.id,
                itemName: item.name,
                quantity: amount,
            },
        });
        return { data: { cashValue: value } };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to sell item:", error });
        return { error: "Failed to sell item" };
    }
};
export const getTraderRep = async (userId, shopId) => {
    try {
        const filter = { userId };
        if (shopId) {
            filter.shopId = shopId;
        }
        const rep = await ShopRepository.getTraderReputation(filter);
        return { data: rep };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to get trader reputation:", error });
        return { error: "Failed to get trader reputation" };
    }
};
