import * as NotificationController from "./notification.controller.js";
import { notificationSchema } from "./notification.validation.js";
import { isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
export const notificationRouter = {
    getList: isLoggedInAuth.input(notificationSchema.getNotificationList).handler(async ({ input, context }) => {
        const response = await NotificationController.getNotificationList(context.user.id, input.limit);
        return handleResponse(response);
    }),
    getUnreadCount: isLoggedInAuth.handler(async ({ context }) => {
        const response = await NotificationController.getNumberOfUnreadNotifications(context.user.id);
        return handleResponse(response);
    }),
    markRead: isLoggedInAuth.input(notificationSchema.markNotificationRead).handler(async ({ input, context }) => {
        const response = await NotificationController.MarkNotificationRead(context.user.id, input.notificationId);
        return handleResponse(response);
    }),
    saveFCMToken: isLoggedInAuth.input(notificationSchema.saveFCMToken).handler(async ({ input, context }) => {
        const response = await NotificationController.saveFCMToken(context.user.id, input.token);
        return handleResponse(response);
    }),
    updatePushSettings: isLoggedInAuth
        .input(notificationSchema.updatePushNotificationSettings)
        .handler(async ({ input, context }) => {
        const response = await NotificationController.UpdatePushNotificationSettings(context.user.id, input.pushEnabled);
        return handleResponse(response);
    }),
};
