import * as AuthController from "./auth.controller.js";
import authSchema from "./auth.validation.js";
import { authLimiter } from "../../middleware/rateLimitMiddleware.js";
import validate from "../../middleware/validate.js";
import routeHandler from "../../utils/routeHandler.js";
import { checkCode, codeList, generateCode, massEmailCodes, referralCodeList } from "./registrationcode.controller.js";
import authHelper from "../../middleware/authMiddleware.js";
import express from "express";
const router = express.Router();
router.get("/checkUsername", routeHandler(async (req) => {
    return await AuthController.checkUsernameAvailable(String(req.query.username));
}));
router.post("/register", authLimiter, validate(authSchema.register), routeHandler(async (req) => {
    const { code, username, email, password } = req.body;
    return await AuthController.registerUser(req.headers, username, email, password, code);
}));
router.get("/referralcodes", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await referralCodeList(req.user.id, req.user.username);
}));
router.post("/codes/check", authLimiter, validate(authSchema.checkCodeSchema), routeHandler(async (req) => {
    return await checkCode(req.body.code);
}));
router.get("/codes", authHelper.IsAdmin, routeHandler(async () => {
    return await codeList();
}));
router.post("/codes/generate", authHelper.IsAdmin, validate(authSchema.generateCodeSchema), routeHandler(async (req) => {
    return await generateCode(req.body.note);
}));
router.post("/codes/mass-email", authHelper.IsAdmin, validate(authSchema.massEmailSchema), routeHandler(async (req) => {
    const { emails, note } = req.body;
    return await massEmailCodes(emails, note, req.user.id);
}));
export default router;
