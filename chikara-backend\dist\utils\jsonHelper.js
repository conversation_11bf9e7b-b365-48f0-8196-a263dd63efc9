import { LogErrorStack } from "./log.js";
export function stringify(obj, space) {
    return JSON.stringify(obj, (key, value) => {
        if (typeof value === "bigint") {
            return value.toString();
        }
        return value;
    }, space);
}
export function unstringify(jsonString) {
    return JSON.parse(jsonString, (key, value) => {
        if (typeof value === "string" && /^\d+$/.test(value)) {
            try {
                return BigInt(value);
            }
            catch {
                return value;
            }
        }
        return value;
    });
}
export const parseJsonArray = (value) => {
    if (!value)
        return null;
    if (Array.isArray(value))
        return value;
    try {
        return JSON.parse(value);
    }
    catch (error) {
        LogErrorStack({ message: "Failed to parse JSON array", error });
        return null;
    }
};
