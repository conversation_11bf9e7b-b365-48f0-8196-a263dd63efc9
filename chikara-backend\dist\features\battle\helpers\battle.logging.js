export var CombatLogAction;
(function (CombatLogAction) {
    CombatLogAction["ATTACK"] = "attack";
    CombatLogAction["RANGED"] = "ranged";
    CombatLogAction["FLEE_SUCCESS"] = "flee_success";
    CombatLogAction["FLEE_FAILED"] = "flee_failed";
    CombatLogAction["BATTLE_WIN"] = "battle_win";
})(CombatLogAction || (CombatLogAction = {}));
export const addCombatLogEntry = (action, battleState, actorId, targetId, details) => {
    const logEntry = {
        id: (Array.isArray(battleState.combatLog) ? battleState.combatLog : []).reduce((max, entry) => {
            const id = typeof entry?.id === "number" ? entry.id : 0;
            return Math.max(id, max);
        }, 0) + 1,
        timestamp: Date.now(),
        round: battleState.currentRound,
        actorId,
        targetId,
        action,
        damage: details?.damage,
        healing: details?.healing,
        details: {
            attackType: details?.attackType,
            statusEffects: details?.effects,
        },
        remainingHealth: {
            actor: details?.attackerHealth ?? null,
            target: details?.targetHealth ?? null,
        },
    };
    const insertIndex = battleState.combatLog.findIndex((entry) => entry.timestamp > logEntry.timestamp);
    if (insertIndex === -1) {
        battleState.combatLog.push(logEntry);
    }
    else {
        battleState.combatLog.splice(insertIndex, 0, logEntry);
    }
    return battleState;
};
export const logActionResult = (battleState, actor, target, result) => {
    const details = {
        damage: result.damage,
        attackType: result.actionType,
        effects: [
            ...(result.lifeSteal ? [`Lifesteal: ${result.lifeSteal}`] : []),
            ...(result.bleedAmount ? [`Bleed: ${result.bleedAmount}`] : []),
        ],
        attackerHealth: actor.currentHealth,
        targetHealth: target.currentHealth,
    };
    addCombatLogEntry(result.actionType, battleState, actor.id, target.id, details);
};
