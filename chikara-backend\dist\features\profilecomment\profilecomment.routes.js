import { isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import { CommentOnProfile, getComments } from "./profilecomment.controller.js";
import profileCommentSchema from "./profilecomment.validation.js";
export const profileCommentRouter = {
    getComments: isLoggedInAuth.input(profileCommentSchema.getComments).handler(async ({ input }) => {
        const result = await getComments(input.userId);
        return handleResponse(result);
    }),
    postComment: isLoggedInAuth.input(profileCommentSchema.postComment).handler(async ({ input, context }) => {
        const result = await CommentOnProfile(context.user.id, input.userId, input.message, context.user.chatBannedUntil);
        return handleResponse(result);
    }),
};
export default profileCommentRouter;
