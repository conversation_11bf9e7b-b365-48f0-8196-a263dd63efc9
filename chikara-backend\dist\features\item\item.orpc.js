import * as DropChanceService from "../dropchance/dropchance.controller.js";
import * as ItemControllerAdmin from "./item.admin.js";
import * as ItemController from "./item.controller.js";
import itemSchema from "./item.validation.js";
import * as UniqueItemController from "./uniqueitem.service.js";
import { isLoggedInAuth, canMakeStateChangesAuth, adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
export const itemRouter = {
    getUpgradeItems: isLoggedInAuth.handler(async () => {
        const response = await ItemController.getUpgradeItems();
        return handleResponse(response);
    }),
    upgradeItem: canMakeStateChangesAuth.input(itemSchema.upgradeItem).handler(async ({ input, context }) => {
        const response = await ItemController.upgradeItem(context.user.id, input.upgradeCores, input.itemId.toString());
        return handleResponse(response);
    }),
    useDeathNote: canMakeStateChangesAuth.input(itemSchema.deathNote).handler(async ({ input, context }) => {
        const response = await UniqueItemController.useDeathNote(context.user.id, input.userId, input.injuryName, input.injuryType);
        return handleResponse(response);
    }),
    useLifeNote: canMakeStateChangesAuth.input(itemSchema.lifeNote).handler(async ({ input, context }) => {
        const response = await UniqueItemController.useLifeNote(context.user.id, input.userId);
        return handleResponse(response);
    }),
    useMegaphone: canMakeStateChangesAuth.input(itemSchema.megaphone).handler(async ({ input, context }) => {
        const response = await UniqueItemController.useMegaPhone(context.user.id, input.message);
        return handleResponse(response);
    }),
    useKompromat: canMakeStateChangesAuth.input(itemSchema.kompromat).handler(async ({ input, context }) => {
        const response = await UniqueItemController.useKompromat(context.user.id, input.userId, input.reason);
        return handleResponse(response);
    }),
    useDailyChest: canMakeStateChangesAuth.handler(async ({ context }) => {
        const response = await UniqueItemController.useDailyChest(context.user.id);
        return handleResponse(response);
    }),
    useMaterialsCrate: canMakeStateChangesAuth.handler(async ({ context }) => {
        const response = await UniqueItemController.useRawMaterialsCrate(context.user.id);
        return handleResponse(response);
    }),
    useToolsCrate: canMakeStateChangesAuth.handler(async ({ context }) => {
        const response = await UniqueItemController.useToolsCrate(context.user.id);
        return handleResponse(response);
    }),
    getDailyChestItems: isLoggedInAuth.handler(async () => {
        const response = await UniqueItemController.getDailyChestItems();
        return handleResponse(response);
    }),
};
export const itemAdminRouter = {
    list: adminAuth.handler(async () => {
        const response = await ItemControllerAdmin.itemList();
        return handleResponse(response);
    }),
    create: adminAuth.input(itemSchema.create).handler(async ({ input, context }) => {
        const response = await ItemControllerAdmin.createItem(input, context.user.id);
        return handleResponse(response);
    }),
    update: adminAuth.input(itemSchema.update).handler(async ({ input }) => {
        const response = await ItemControllerAdmin.editItem(input);
        return handleResponse(response);
    }),
    getDropTables: adminAuth.handler(async () => {
        const response = await DropChanceService.getDropTables();
        return handleResponse(response);
    }),
};
