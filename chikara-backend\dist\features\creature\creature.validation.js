import { ENCOUNTER_LOCATIONS } from "../roguelike/roguelike.constants.js";
import { CreatureStatTypes } from "@prisma/client";
import { z } from "zod";
export const creatureValidation = z.object({
    name: z.string().min(1),
    image: z.string().min(1),
    minFloor: z.number().int().min(0),
    maxFloor: z.number().int().min(0),
    boss: z.boolean(),
    health: z.number().int().positive(),
    strength: z.number().int().positive(),
    defence: z.number().int().positive(),
    weaponDamage: z.number().int().positive(),
    location: z.enum(ENCOUNTER_LOCATIONS).nullable(),
    statType: z.nativeEnum(CreatureStatTypes),
});
export const createCreatureSchema = creatureValidation;
export const updateCreatureSchema = creatureValidation.partial().extend({
    id: z.number().int().positive(),
});
export const deleteCreatureSchema = z.object({
    id: z.number().int().positive(),
});
export default {
    createCreatureSchema,
    updateCreatureSchema,
    deleteCreatureSchema,
};
