import { getRedisItem, redisClient } from "../../config/redisClient.js";
import { db } from "../db.js";
import { LogErrorStack, logger } from "../../utils/log.js";
const DEFAULT_CACHE_TTL = 60 * 60 * 24;
const modelCacheConfig = {
    item: { prefix: "item:", ttl: DEFAULT_CACHE_TTL },
    talent: { prefix: "talent:", ttl: DEFAULT_CACHE_TTL },
    shop: { prefix: "shop:", ttl: DEFAULT_CACHE_TTL },
};
const redis = redisClient;
function getModelClient(model) {
    return db[model] ?? null;
}
async function clearModelCache(model, id) {
    try {
        const config = modelCacheConfig[model];
        if (!config) {
            logger.warn(`No cache configuration for model: ${model}`);
            return;
        }
        if (id) {
            const cacheKey = `${config.prefix}${id}`;
            await redis.del([cacheKey]);
            await redis.del([`${model}:all`]);
        }
        else {
            const keys = await redis.keys(`${config.prefix}*`);
            const allKey = `${model}:all`;
            const keysToDelete = [...new Set([...keys, allKey])];
            if (keysToDelete.length > 0) {
                await redis.del(keysToDelete);
                logger.debug(`Cleared ${keysToDelete.length} ${model} cache entries`);
            }
        }
    }
    catch (error) {
        LogErrorStack({ message: `Failed to clear ${model} cache`, error });
    }
}
async function getEntityById(model, id) {
    const config = modelCacheConfig[model];
    const client = getModelClient(model);
    if (!config || !client) {
        logger.warn(`No cache configuration for model: ${model}`);
        return null;
    }
    const cacheKey = `${config.prefix}${id}`;
    try {
        const cachedEntity = await getRedisItem(cacheKey);
        if (cachedEntity) {
            if (cachedEntity === "NOT_FOUND") {
                return null;
            }
            return cachedEntity;
        }
        const entity = (await client.findUnique({
            where: { id },
        }));
        if (!entity) {
            try {
                await redis.setEx(cacheKey, 60, "NOT_FOUND");
            }
            catch (cacheError) {
                logger.warn(`Failed to cache not-found state for ${model} ${id}: ${cacheError}`);
            }
            return null;
        }
        try {
            await redis.setEx(cacheKey, config.ttl, JSON.stringify(entity));
        }
        catch (cacheError) {
            logger.warn(`Failed to cache ${model} ${id}: ${cacheError}`);
        }
        return entity;
    }
    catch (error) {
        LogErrorStack({ message: `Error fetching ${model} ${id}`, error });
        if (client) {
            return client.findUnique({
                where: { id },
            });
        }
        return null;
    }
}
async function getAllEntities(model, options = {}) {
    const config = modelCacheConfig[model];
    const client = getModelClient(model);
    if (!config || !client) {
        logger.warn(`No cache configuration for model: ${model}`);
        return [];
    }
    const cacheKey = `${model}:all`;
    try {
        const cachedEntities = await getRedisItem(cacheKey);
        if (cachedEntities) {
            return cachedEntities;
        }
        const entities = (await client.findMany(options));
        try {
            await redis.setEx(cacheKey, config.ttl, JSON.stringify(entities));
        }
        catch (cacheError) {
            logger.warn(`Failed to cache all ${model}s: ${cacheError}`);
        }
        return entities;
    }
    catch (error) {
        LogErrorStack({ message: `Error fetching all ${model}s`, error });
        if (client) {
            return client.findMany(options);
        }
        return [];
    }
}
async function clearAllCache() {
    const promises = Object.keys(modelCacheConfig).map((model) => clearModelCache(model));
    try {
        await Promise.all(promises);
        logger.debug("Cleared cache for all models");
    }
    catch (error) {
        LogErrorStack({ message: `Failed to clear all cache`, error });
    }
}
export { getEntityById, getAllEntities, clearModelCache, clearAllCache };
