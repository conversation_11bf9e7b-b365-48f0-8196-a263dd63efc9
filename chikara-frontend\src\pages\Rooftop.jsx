import LoadingState from "@/components/LoadingState";
import { APIROUTES } from "@/helpers/apiRoutes";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { useQuery } from "@tanstack/react-query";
import BattleList from "../components/RooftopBattleList";

export default function Rooftop() {
    const { data: npcList, isLoading } = useQuery({ queryKey: APIROUTES.ROOFTOP.NPCLIST });
    const { data: currentUser } = useFetchCurrentUser();

    return (
        <section className=" mx-auto rounded-lg py-6 md:max-w-7xl">
            <div className=" mx-auto h-full px-0 md:px-6">
                <div className="mx-auto h-full text-center">
                    {/* <div className="mb-4">
            <h1 className="text-3xl font-medium text-custom-yellow tracking-tighter sm:text-4xl md:text-3xl">
              NPC Boss Battles
            </h1>
          </div> */}
                    <div className="mt-4 w-full md:mx-0">
                        <LoadingState isLoading={isLoading}>
                            <BattleList npcList={npcList} currentUser={currentUser} />
                        </LoadingState>
                    </div>
                </div>
            </div>
        </section>
    );
}
