import { db } from "../lib/db.js";
export const findManyComments = async (userId) => {
    return await db.profile_comment.findMany({
        where: {
            receiverId: userId,
        },
        select: {
            message: true,
            createdAt: true,
            senderId: true,
        },
        orderBy: {
            createdAt: "desc",
        },
    });
};
export const createComment = async (senderId, receiverId, message) => {
    return await db.profile_comment.create({
        data: {
            senderId: senderId,
            receiverId: receiverId,
            message: message,
        },
    });
};
