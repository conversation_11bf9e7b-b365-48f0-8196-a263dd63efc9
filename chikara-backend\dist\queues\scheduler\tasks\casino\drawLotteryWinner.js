import * as CasinoRepository from "../../../../repositories/casino.repository.js";
import * as <PERSON>t<PERSON>elper from "../../../../features/chat/chat.helpers.js";
import { logAction } from "../../../../lib/actionLogger.js";
import { getYesterday } from "../../../../utils/dateHelpers.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";
import * as UserRepository from "../../../../repositories/user.repository.js";
async function announceLotteryWinner(winner) {
    await ChatHelper.SendAnnouncementMessage("lotteryWinner", JSON.stringify({ id: winner.id, username: winner.username, prizeAmount: winner.prizeAmount }));
}
export async function drawLotteryWinner() {
    try {
        const yesterday = getYesterday();
        const lottery = await CasinoRepository.findLotteryWithoutWinner(yesterday);
        if (lottery) {
            const lotteryEntries = await CasinoRepository.findLotteryEntriesWithUsers(lottery.id);
            if (lotteryEntries.length > 0) {
                const winnerIndex = Math.floor(Math.random() * lotteryEntries.length);
                const winner = lotteryEntries[winnerIndex].user;
                if (!winner) {
                    logger.error("Lottery winner user object not found");
                    return;
                }
                await CasinoRepository.updateLotteryWinner(lottery.id, winner.id, lotteryEntries.length);
                await UserRepository.incrementUserCash(winner.id, lottery.prizeAmount);
                await announceLotteryWinner({
                    id: winner.id,
                    username: winner.username,
                    prizeAmount: lottery.prizeAmount,
                });
                logAction({
                    action: "CASINO_LOTTERY_WON",
                    userId: winner.id,
                    info: {
                        prizeAmount: lottery.prizeAmount,
                        entries: lotteryEntries.length,
                    },
                });
                return { username: winner.username, prizeAmount: lottery.prizeAmount };
            }
        }
        logger.info("No entries found for yesterday's lottery");
        return;
    }
    catch (error) {
        LogErrorStack({ message: "Error drawing lottery winner:", error });
    }
}
