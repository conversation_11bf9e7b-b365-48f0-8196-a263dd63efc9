import { userConfig } from "../../config/gameConfig.js";
import ms from "ms";
const { HEALING_TICK_INTERVAL, AP_TICK_INTERVAL } = userConfig.public;
export default {
    tasks: {
        auctions: {
            checkExpiredAuctions: ms("5m"),
        },
        cleanups: {
            cleanupExpiredStatusEffects: ms("1h"),
            checkTimeoutBattles: ms("10m"),
            cleanupExpiredScavengingSessions: ms("30m"),
        },
        jobs: {
            processFirstJobPayments: "0 2 * * *",
            processSecondJobPayments: "0 8 * * *",
            processThirdJobPayments: "0 14 * * *",
            processFourthJobPayments: "0 20 * * *",
        },
        shops: {
            restockSundayShop: "30 11 * * SUN",
            openSundayShop: "00 12 * * SUN",
            closeSundayShop: "00 00 * * MON",
        },
        casino: {
            announceLotteryEnding: "30 17 * * *",
            drawLotteryWinner: "00 18 * * *",
            createDailyLottery: "05 18 * * *",
        },
        missions: {
            processDailyMissions: "50 23 * * *",
        },
        health: {
            processHealthIncreases: HEALING_TICK_INTERVAL,
            processAPIncreases: AP_TICK_INTERVAL,
        },
        shrine: {
            processShrineGoal: "55 23 * * *",
        },
        gangs: {
            processGangPayouts: "0 0 * * MON",
            resetDailyEssenceLimit: "0 0 * * *",
        },
        stats: {
            collectDailyStats: "5 0 * * *",
            collectPlayerStats: "10 0 * * *",
        },
        npc: {
            generateRandomNPCListings: "0 */5 * * *",
            placeRandomNPCBounty: "0 */2 * * *",
        },
        pets: {
            processHappinessDecay: "0 */4 * * *",
        },
    },
};
