import type { BattlePlayer, EquippedAbility } from "../types/battle.types.js";
import { GetDamage } from "../helpers/battle.damage.js";
import {
    ATTACK_TYPE_RANGED,
    RAGE_ABILITY_NAME,
    CRIPPLE_ABILITY_NAME,
    STUN_ABILITY_NAME,
    HEADBUTT_ABILITY_NAME,
    SHIELD_BASH_ABILITY_NAME,
    SHOCKWAVE_ABILITY_NAME,
    EXHAUST_ABILITY_NAME,
    // HEAL_ABILITY_NAME,
    HEAL_OVER_TIME_ABILITY_NAME,
    SLEEP_ABILITY_NAME,
    SELF_HARM_ABILITY_NAME,
    // MAX_HP_HEAL_ABILITY_NAME,
    // RELOAD_ABILITY_NAME,
    SPRAY_ABILITY_NAME,
    TOXIC_DART_ABILITY_NAME,
    DISARM_ABILITY_NAME,
    GIANT_KILLING_SLINGSHOT_ABILITY_NAME,
    HIGH_GUARD_ABILITY_NAME,
} from "../helpers/battle.constants.js";

interface StatusAbility {
    name: string;
    turns: number;
    target: "self" | "enemy";
}

export const STATUS_ABILITIES: StatusAbility[] = [
    { name: RAGE_ABILITY_NAME, turns: 4, target: "self" },
    { name: HEAL_OVER_TIME_ABILITY_NAME, turns: 3, target: "self" },
    { name: SELF_HARM_ABILITY_NAME, turns: 3, target: "self" },
    { name: HIGH_GUARD_ABILITY_NAME, turns: 3, target: "self" },
    { name: CRIPPLE_ABILITY_NAME, turns: 3, target: "enemy" },
    { name: STUN_ABILITY_NAME, turns: 3, target: "enemy" },
    { name: SHOCKWAVE_ABILITY_NAME, turns: 3, target: "enemy" },
    { name: EXHAUST_ABILITY_NAME, turns: 3, target: "enemy" },
    { name: SLEEP_ABILITY_NAME, turns: 3, target: "enemy" },
    { name: TOXIC_DART_ABILITY_NAME, turns: 3, target: "enemy" },
    { name: DISARM_ABILITY_NAME, turns: 2, target: "enemy" },
];

// Helper function to find an ability by name from the equipped abilities
const findAbilityByName = (abilities: EquippedAbility[] | undefined, abilityName: string): EquippedAbility | null => {
    if (!abilities) return null;
    return abilities.find((ability) => ability.name === abilityName) || null;
};

export const GetToxicDartDamage = (target: BattlePlayer, abilities: EquippedAbility[] | undefined) => {
    const ability = findAbilityByName(abilities, TOXIC_DART_ABILITY_NAME);
    if (!ability || !ability.currentModifier) return 0;

    const currentModifier = ability.currentModifier || 0;
    const currentHpDamage = target.currentHealth * currentModifier;
    const secondaryModifier = ability.secondaryModifier || 0;

    return secondaryModifier + currentHpDamage;
};

export const GetGiantKillingSlingshotDamage = (
    target: BattlePlayer,
    healthyCasterTalentActive: boolean,
    abilityDmgDebuff: number,
    abilities: EquippedAbility[] | undefined
) => {
    const ability = findAbilityByName(abilities, GIANT_KILLING_SLINGSHOT_ABILITY_NAME);
    if (!ability || !ability.currentModifier) return 1;

    const damage = Math.round(target.currentHealth * ability.currentModifier);
    const finalDamage = healthyCasterTalentActive ? damage * 1.5 : damage;
    return Math.round(Math.max(1, finalDamage * (1 - abilityDmgDebuff)));
};

export const GetHeadbuttDamage = (
    target: BattlePlayer,
    healthyCasterTalentActive: boolean,
    abilityDmgDebuff: number,
    abilities: EquippedAbility[] | undefined
) => {
    // todo: just ignore armour/resistances?
    const ability = findAbilityByName(abilities, HEADBUTT_ABILITY_NAME);
    if (!ability || !ability.currentModifier) return 1;

    const damage = Math.round(target.maxHealth * ability.currentModifier);
    const finalDamage = healthyCasterTalentActive ? damage * 1.5 : damage;
    return Math.round(Math.max(1, finalDamage * (1 - abilityDmgDebuff)));
};

export const GetShieldBashDamage = (
    user: BattlePlayer,
    healthyCasterTalentActive: boolean,
    abilityDmgDebuff: number,
    abilities: EquippedAbility[] | undefined
) => {
    const ability = findAbilityByName(abilities, SHIELD_BASH_ABILITY_NAME);
    if (!ability || !ability.currentModifier) return 1;

    const secondaryModifier = ability.secondaryModifier || 0;
    const currentModifier = ability.currentModifier || 0;

    const damage = secondaryModifier + Math.round(user.attributes.defence * currentModifier);
    const finalDamage = healthyCasterTalentActive ? damage * 1.5 : damage;
    return Math.round(Math.max(1, finalDamage * (1 - abilityDmgDebuff)));
};

export const GetSprayDamage = async (
    user: BattlePlayer,
    target: BattlePlayer,
    ammoToUse: number,
    abilityDmgDebuff: number,
    abilities: EquippedAbility[] | undefined
) => {
    // 30% extra damage per additional ammo used (1 ammo used = 1, 2 = 1.3)
    const ability = findAbilityByName(abilities, SPRAY_ABILITY_NAME);
    if (!ability || !ability.currentModifier) return 1;

    const sprayDamage = Math.round(
        (await GetDamage(user, target, ATTACK_TYPE_RANGED)) * (0.7 + ability.currentModifier * ammoToUse)
    );

    return Math.round(Math.max(1, sprayDamage * (1 - abilityDmgDebuff)));
};

// Applies self-harm damage to the caster based on the ability's secondaryModifier.
// Returns the damage dealt to the caster for reference.
export const ApplySelfHarmDamage = (user: BattlePlayer, abilities: EquippedAbility[] | undefined) => {
    const ability = findAbilityByName(abilities, SELF_HARM_ABILITY_NAME);
    if (!ability || !ability.secondaryModifier) return 0;

    const damageAmount = Math.floor(user.maxHealth * ability.secondaryModifier);
    user.currentHealth = Math.max(0, user.currentHealth - damageAmount);

    return damageAmount;
};
