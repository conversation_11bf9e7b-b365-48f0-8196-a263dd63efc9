import * as CreatureRepository from "../../repositories/creature.repository.js";
export const creatureList = async () => {
    const creatures = await CreatureRepository.findAllCreatures();
    return { data: creatures };
};
export const createCreature = async (creatureData) => {
    const creature = await CreatureRepository.createNewCreature(creatureData);
    return { data: creature };
};
export const editCreature = async (id, updateData) => {
    const updatedCreature = await CreatureRepository.updateCreature(id, updateData);
    return { data: updatedCreature };
};
export const deleteCreature = async (id) => {
    await CreatureRepository.deleteCreature(id);
    return { data: `Creature deleted` };
};
