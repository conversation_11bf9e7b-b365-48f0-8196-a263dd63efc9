import { db } from "../lib/db.js";
import { Prisma, QuestProgressStatus } from "@prisma/client";
import { parseJsonArray } from "../utils/jsonHelper.js";
const transformEpisodeData = (episode) => {
    if (!episode.objectiveId) {
        throw new Error(`Episode ${episode.id} is missing required objectiveId`);
    }
    return {
        ...episode,
        objectiveId: episode.objectiveId,
        exploreLocation: episode.exploreLocation || "shibuya",
        content: episode.content,
        choices: episode.choices,
    };
};
export const findAllSeasons = async () => {
    const seasons = await db.story_season.findMany({
        orderBy: { startDate: "asc" },
    });
    return seasons;
};
export const findSeasonById = async (seasonId) => {
    const season = await db.story_season.findUnique({
        where: { id: seasonId },
    });
    if (!season)
        return null;
    return season;
};
export const findAllChapters = async () => {
    const chapters = await db.story_chapter.findMany({
        orderBy: [{ seasonId: "asc" }, { order: "asc" }],
    });
    return chapters.map((chapter) => ({
        ...chapter,
        requiredChapterIds: parseJsonArray(chapter.requiredChapterIds),
    }));
};
export const findAllEpisodes = async () => {
    const episodes = await db.story_episode.findMany({
        orderBy: [{ quest_objective: { quest: { chapterId: "asc" } } }],
    });
    return episodes.map((episode) => transformEpisodeData(episode));
};
export const findChaptersBySeasonId = async (seasonId) => {
    const chapters = await db.story_chapter.findMany({
        where: { seasonId },
        orderBy: { order: "asc" },
    });
    return chapters.map((chapter) => ({
        ...chapter,
        requiredChapterIds: parseJsonArray(chapter.requiredChapterIds),
    }));
};
export const findChaptersBySeasonIds = async (seasonIds) => {
    if (seasonIds.length === 0) {
        return [];
    }
    const chapters = await db.story_chapter.findMany({
        where: {
            seasonId: {
                in: seasonIds,
            },
        },
        orderBy: [{ seasonId: "asc" }, { order: "asc" }],
    });
    return chapters.map((chapter) => ({
        ...chapter,
        requiredChapterIds: parseJsonArray(chapter.requiredChapterIds),
    }));
};
export const findChapterById = async (chapterId) => {
    const chapter = await db.story_chapter.findUnique({
        where: { id: chapterId },
    });
    if (!chapter)
        return null;
    return {
        ...chapter,
        requiredChapterIds: parseJsonArray(chapter.requiredChapterIds),
    };
};
export const findEpisodesByChapterId = async (chapterId) => {
    const episodes = await db.story_episode.findMany({
        where: {
            quest_objective: {
                quest: {
                    chapterId: chapterId,
                },
            },
        },
        include: {
            quest_objective: {
                include: {
                    quest: true,
                },
            },
        },
        orderBy: {
            quest_objective: {
                quest: {
                    orderInChapter: "asc",
                },
            },
        },
    });
    return episodes.map((episode) => transformEpisodeData(episode));
};
export const findEpisodesByChapterIds = async (chapterIds) => {
    if (chapterIds.length === 0) {
        return [];
    }
    const episodes = await db.story_episode.findMany({
        where: {
            quest_objective: {
                quest: {
                    chapterId: {
                        in: chapterIds,
                    },
                },
            },
        },
        include: {
            quest_objective: {
                include: {
                    quest: true,
                },
            },
        },
        orderBy: [
            {
                quest_objective: {
                    quest: {
                        chapterId: "asc",
                    },
                },
            },
            {
                quest_objective: {
                    quest: {
                        orderInChapter: "asc",
                    },
                },
            },
        ],
    });
    return episodes.map((episode) => transformEpisodeData(episode));
};
export const findEpisodeById = async (episodeId) => {
    const episode = await db.story_episode.findUnique({
        where: { id: episodeId },
    });
    if (!episode)
        return null;
    return transformEpisodeData(episode);
};
export const getUserCompletedQuests = async (userId) => {
    const completedQuests = await db.quest_progress.findMany({
        where: {
            userId,
            questStatus: QuestProgressStatus.complete,
        },
        select: {
            questId: true,
        },
    });
    return completedQuests.map((q) => q.questId).filter((id) => id !== null);
};
export const createSeason = async (data) => {
    const season = await db.story_season.create({
        data: data,
    });
    return season;
};
export const updateSeason = async (seasonId, data) => {
    const season = await db.story_season.update({
        where: { id: seasonId },
        data: data,
    });
    return season;
};
export const deleteSeason = async (seasonId) => {
    await db.story_season.delete({
        where: { id: seasonId },
    });
};
export const createChapter = async (data) => {
    const createData = {
        ...data,
        requiredChapterIds: data.requiredChapterIds === null ? Prisma.JsonNull : data.requiredChapterIds,
    };
    const chapter = await db.story_chapter.create({
        data: createData,
    });
    return {
        ...chapter,
        requiredChapterIds: parseJsonArray(chapter.requiredChapterIds),
    };
};
export const updateChapter = async (chapterId, data) => {
    const updateData = {
        ...data,
        requiredChapterIds: data.requiredChapterIds === null ? Prisma.JsonNull : data.requiredChapterIds,
    };
    const chapter = await db.story_chapter.update({
        where: { id: chapterId },
        data: updateData,
    });
    return {
        ...chapter,
        requiredChapterIds: parseJsonArray(chapter.requiredChapterIds),
    };
};
export const deleteChapter = async (chapterId) => {
    await db.story_chapter.delete({
        where: { id: chapterId },
    });
};
export const createEpisode = async (data) => {
    const createData = {
        ...data,
        choices: data.choices === null ? Prisma.JsonNull : data.choices,
    };
    const episode = await db.story_episode.create({
        data: createData,
    });
    return transformEpisodeData(episode);
};
export const updateEpisode = async (episodeId, data) => {
    const updateData = {
        ...data,
        choices: data.choices === null ? Prisma.JsonNull : data.choices,
    };
    const episode = await db.story_episode.update({
        where: { id: episodeId },
        data: updateData,
    });
    return transformEpisodeData(episode);
};
export const deleteEpisode = async (episodeId) => {
    await db.story_episode.delete({
        where: { id: episodeId },
    });
};
export const findEpisodeByObjectiveId = async (objectiveId) => {
    const episode = await db.story_episode.findFirst({
        where: { objectiveId },
    });
    if (!episode)
        return null;
    return transformEpisodeData(episode);
};
