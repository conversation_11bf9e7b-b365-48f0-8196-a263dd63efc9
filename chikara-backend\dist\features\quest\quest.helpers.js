import gameConfig from "../../config/gameConfig.js";
import * as AchievementService from "../../core/achievement.service.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as UserService from "../../core/user.service.js";
import * as QuestRepository from "../../repositories/quest.repository.js";
import { QuestObjectiveTypes } from "../../types/quest.js";
import { LogErrorStack } from "../../utils/log.js";
import { QuestProgressStatus, QuestRewardType } from "@prisma/client";
const { MAX_TRADER_REP } = gameConfig;
export async function UserCanStartQuest(currentUser, quest) {
    const userQuestProgress = await QuestRepository.getUserQuestProgress(currentUser.id, quest.id);
    if (userQuestProgress) {
        return { error: "User has already started this quest", statusCode: 400 };
    }
    return await checkQuestRequirements(currentUser, quest);
}
export async function checkQuestRequirements(currentUser, quest) {
    if (quest.disabled) {
        return { error: "Quest is disabled", statusCode: 400 };
    }
    if (currentUser.level < quest.levelReq) {
        return { error: "User does not meet level requirement", statusCode: 400 };
    }
    if (quest.requiredQuestId) {
        const prerequisiteProgress = await QuestRepository.getUserQuestProgress(currentUser.id, quest.requiredQuestId);
        if (!prerequisiteProgress || prerequisiteProgress.questStatus !== QuestProgressStatus.complete) {
            return { error: "Prerequisite quest not completed", statusCode: 400 };
        }
    }
    return { success: true };
}
export const updateQuestObjectiveCount = async (questObjectiveProgress, amount = 1) => {
    if (!questObjectiveProgress) {
        return;
    }
    if (Array.isArray(questObjectiveProgress) && questObjectiveProgress.length === 0) {
        return;
    }
    if (!Array.isArray(questObjectiveProgress)) {
        questObjectiveProgress = [questObjectiveProgress];
    }
    for (const objProgress of questObjectiveProgress) {
        const objective = objProgress.quest_objective;
        if (objProgress.status === QuestProgressStatus.complete) {
            continue;
        }
        if (objective.quantity == null || objective.quantity <= 0 || objProgress.count >= objective.quantity) {
            continue;
        }
        if (objProgress.count + amount > objective.quantity) {
            amount = objective.quantity - objProgress.count;
        }
        await QuestRepository.updateQuestObjectiveProgress(objProgress.id, {
            count: { increment: amount },
        });
        const objectiveComplete = await checkObjectiveComplete(objProgress.id);
        if (objectiveComplete && objective.questId) {
            await checkQuestReadyToComplete(objProgress.userId, objective.questId);
        }
    }
};
export const checkQuestReadyToComplete = async (userId, questId) => {
    const questProgress = await QuestRepository.getUserQuestProgress(userId, questId);
    if (!questProgress || questProgress.questStatus !== QuestProgressStatus.in_progress) {
        return false;
    }
    const objectiveProgress = await QuestRepository.findRequiredUserQuestObjectiveProgressForQuest(userId, questId);
    if (objectiveProgress.length === 0) {
        return false;
    }
    const requiredObjectives = objectiveProgress.filter((progress) => progress.quest_objective.objectiveType !== QuestObjectiveTypes.ACQUIRE_ITEM);
    const allComplete = requiredObjectives.every((progress) => progress.status === QuestProgressStatus.complete);
    if (allComplete) {
        const optionalObjectives = objectiveProgress.filter((progress) => progress.quest_objective.objectiveType === QuestObjectiveTypes.ACQUIRE_ITEM);
        for (const objective of optionalObjectives) {
            await QuestRepository.updateQuestObjectiveProgress(objective.id, {
                count: objective.quest_objective.quantity || 0,
                status: QuestProgressStatus.complete,
            });
        }
        await QuestRepository.updateQuestProgress(questProgress.id, {
            questStatus: QuestProgressStatus.ready_to_complete,
        });
        return true;
    }
    return false;
};
export const checkObjectiveComplete = async (objectiveProgressId, type = "quantity") => {
    const objectiveProgress = await QuestRepository.getQuestObjectiveProgressById(objectiveProgressId);
    if (!objectiveProgress || objectiveProgress.status === QuestProgressStatus.complete) {
        return false;
    }
    const objective = await QuestRepository.getQuestObjectiveById(objectiveProgress.questObjectiveId);
    if (!objective || objective[type] == null) {
        return false;
    }
    if (objectiveProgress.count >= objective[type]) {
        await QuestRepository.updateQuestObjectiveProgress(objectiveProgressId, {
            status: QuestProgressStatus.complete,
        });
        if (objective.questId) {
            await checkQuestReadyToComplete(objectiveProgress.userId, objective.questId);
        }
        return true;
    }
    return false;
};
export const AddTraderRep = async (userId, shopId, rep) => {
    const traderRep = await QuestRepository.findTraderRep(userId, shopId);
    if (traderRep) {
        await QuestRepository.saveTraderRep({
            ...traderRep,
            reputationLevel: Math.min(traderRep.reputationLevel + rep, MAX_TRADER_REP),
        });
        return;
    }
    await QuestRepository.createTraderRep(userId, shopId, rep);
};
export const ApplyQuestCompletion = async (quest, user) => {
    if ("quest_reward" in quest && Array.isArray(quest.quest_reward)) {
        const updates = {};
        if (quest.cashReward && quest.cashReward > 0) {
            updates.cash = (updates.cash || user.cash || 0) + quest.cashReward;
        }
        if (quest.xpReward && quest.xpReward > 0) {
            await UserService.AddXPToUser(user, quest.xpReward);
        }
        if (quest.repReward && quest.repReward > 0) {
            if ("shopId" in quest && quest.shopId) {
                await AddTraderRep(user.id, quest.shopId, quest.repReward);
            }
            else {
                LogErrorStack({
                    message: `Quest ${quest.id} has REP reward without shopId.`,
                    error: new Error(`Quest ${quest.id} has REP reward without shopId.`),
                });
            }
        }
        for (const reward of quest.quest_reward) {
            switch (reward.rewardType) {
                case QuestRewardType.TALENT_POINTS: {
                    updates.talentPoints = (updates.talentPoints || user.talentPoints || 0) + reward.quantity;
                    break;
                }
                case QuestRewardType.ITEM: {
                    if (reward.itemId) {
                        await InventoryService.AddItemToUser({
                            userId: user.id,
                            itemId: reward.itemId,
                            amount: reward.quantity,
                            isTradeable: false,
                        });
                    }
                    else {
                        LogErrorStack({
                            message: `Quest ${quest.id} has ITEM reward without itemId.`,
                            error: new Error(`Quest ${quest.id} has ITEM reward without itemId.`),
                        });
                    }
                    break;
                }
                case QuestRewardType.GANG_CREDS: {
                    updates.gangCreds = (updates.gangCreds || user.gangCreds || 0) + reward.quantity;
                    break;
                }
                case QuestRewardType.CLASS_POINTS: {
                    updates.classPoints = (updates.classPoints || user.classPoints || 0) + reward.quantity;
                    break;
                }
                default: {
                    LogErrorStack({
                        message: `Unknown quest reward type: ${reward.rewardType} for quest ${quest.id}`,
                        error: new Error(`Unknown quest reward type: ${reward.rewardType} for quest ${quest.id}`),
                    });
                }
            }
        }
        if (Object.keys(updates).length > 0) {
            await UserService.updateUser(user.id, updates);
        }
    }
    else {
        LogErrorStack({
            message: `Quest ${quest.id} has no quest_reward array.`,
            error: new Error(`Quest ${quest.id} has no quest_reward array.`),
        });
    }
    await AchievementService.UpdateUserAchievement(user.id, "questsCompleted");
};
export const CompleteItemHandInObjective = async (objectiveProgressId, itemCount) => {
    const objectiveProgress = await QuestRepository.getQuestObjectiveProgressById(objectiveProgressId);
    if (!objectiveProgress || objectiveProgress.status !== QuestProgressStatus.in_progress) {
        return false;
    }
    const objective = await QuestRepository.getQuestObjectiveById(objectiveProgress.questObjectiveId);
    if (!objective || objective.objectiveType !== QuestObjectiveTypes.DELIVER_ITEM || !objective.quantity) {
        return false;
    }
    await QuestRepository.updateQuestObjectiveProgress(objectiveProgressId, {
        count: itemCount,
        status: QuestProgressStatus.complete,
    });
    if (objective.questId) {
        await checkQuestReadyToComplete(objectiveProgress.userId, objective.questId);
    }
    return true;
};
