import { db } from "../../lib/db.js";
import routeHandler from "../../utils/routeHandler.js";
const getPrismaModel = (modelName) => {
    return db[modelName.toLowerCase()];
};
const handleGetList = async ({ modelName, sort, range, filter }) => {
    const model = getPrismaModel(modelName);
    const whereClause = filter ? JSON.parse(filter) : {};
    const orderBy = [];
    if (sort) {
        const [field, direction] = JSON.parse(sort);
        orderBy.push({ [field]: direction.toLowerCase() });
    }
    let skip = 0;
    let take;
    if (range) {
        const [start, end] = JSON.parse(range);
        take = end - start + 1;
        skip = start;
    }
    const queryOptions = {
        where: whereClause,
        orderBy: orderBy.length > 0 ? orderBy : undefined,
        skip: skip,
        take: take,
    };
    const result = await model.findMany(queryOptions);
    const count = await model.count({ where: whereClause });
    return {
        data: result,
        headers: { "Content-Range": `items ${skip}-${skip + result.length - 1}/${count}` },
    };
};
const handleGetOne = async ({ modelName, id }) => {
    const model = getPrismaModel(modelName);
    const parsedId = typeof id === "string" && !Number.isNaN(Number(id)) ? Number(id) : id;
    const result = await model.findUnique({
        where: { id: parsedId },
    });
    if (result)
        return { data: result };
    return { error: "Not found" };
};
const handleGetMany = async ({ modelName, filter }) => {
    const model = getPrismaModel(modelName);
    let whereClause = {};
    if (filter) {
        const parsedFilter = JSON.parse(filter);
        if (parsedFilter.id) {
            whereClause = {
                id: {
                    in: Array.isArray(parsedFilter.id) ? parsedFilter.id : [parsedFilter.id],
                },
            };
        }
        else {
            whereClause = parsedFilter;
        }
    }
    const result = await model.findMany({
        where: whereClause,
    });
    return { data: result };
};
const handleCreate = async ({ modelName, body }) => {
    const model = getPrismaModel(modelName);
    const result = await model.create({
        data: body,
    });
    return { data: result, statusCode: 201 };
};
const handleUpdate = async ({ modelName, id, body }) => {
    const model = getPrismaModel(modelName);
    const parsedId = typeof id === "string" && !Number.isNaN(Number(id)) ? Number(id) : id;
    try {
        const result = await model.update({
            where: { id: parsedId },
            data: body,
        });
        return { data: result };
    }
    catch (error) {
        return { error: error instanceof Error ? error.message : "Unknown error" };
    }
};
const handleUpdateMany = async ({ modelName, filter, data }) => {
    const model = getPrismaModel(modelName);
    const whereClause = JSON.parse(filter);
    const result = await model.updateMany({
        where: whereClause,
        data: data,
    });
    return { data: result };
};
const handleDelete = async ({ modelName, id }) => {
    const model = getPrismaModel(modelName);
    const parsedId = typeof id === "string" && !Number.isNaN(Number(id)) ? Number(id) : id;
    try {
        await model.delete({
            where: { id: parsedId },
        });
        return { statusCode: 204 };
    }
    catch (error) {
        return { error: error instanceof Error ? error.message : "Unknown error" };
    }
};
const handleDeleteMany = async ({ modelName, filter }) => {
    const model = getPrismaModel(modelName);
    const whereClause = JSON.parse(filter);
    await model.deleteMany({
        where: whereClause,
    });
    return { statusCode: 204 };
};
const generateRoutesForModel = (router, modelName, auth) => {
    const routePath = `/${modelName}s`;
    router.get(routePath, auth, routeHandler(async (req) => {
        const { sort, range, filter } = req.query;
        return await handleGetList({ modelName, sort, range, filter });
    }));
    router.get(`${routePath}/:id`, auth, routeHandler(async (req) => {
        return await handleGetOne({ modelName, id: req.params.id });
    }));
    router.get(routePath, auth, routeHandler(async (req) => {
        const { filter } = req.query;
        return await handleGetMany({ modelName, filter });
    }));
    router.post(routePath, auth, routeHandler(async (req) => {
        return await handleCreate({ modelName, body: req.body });
    }));
    router.put(`${routePath}/:id`, auth, routeHandler(async (req) => {
        return await handleUpdate({ modelName, id: req.params.id, body: req.body });
    }));
    router.put(routePath, auth, routeHandler(async (req) => {
        const { filter, data } = req.body;
        return await handleUpdateMany({ modelName, filter, data });
    }));
    router.delete(`${routePath}/:id`, auth, routeHandler(async (req) => {
        return await handleDelete({ modelName, id: req.params.id });
    }));
    router.delete(routePath, auth, routeHandler(async (req) => {
        const { filter } = req.body;
        return await handleDeleteMany({ modelName, filter });
    }));
};
export const generateAdminRoutes = (router, modelNames, auth) => {
    modelNames.forEach((modelName) => {
        generateRoutesForModel(router, modelName, auth);
    });
};
