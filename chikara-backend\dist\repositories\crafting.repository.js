import { db } from "../lib/db.js";
export const findCraftingRecipeById = async (recipeId) => {
    return await db.crafting_recipe.findUnique({
        where: { id: recipeId },
    });
};
export const findCraftingQueueByUserId = async (userId) => {
    return await db.user_crafting_queue.findMany({
        where: { userId },
        select: {
            id: true,
            startedAt: true,
            endsAt: true,
            crafting_recipe: {
                select: {
                    id: true,
                    craftTime: true,
                    requiredSkillType: true,
                    requiredSkillLevel: true,
                    cost: true,
                    recipe_item: {
                        select: {
                            count: true,
                            itemType: true,
                            item: {
                                omit: {
                                    createdAt: true,
                                    updatedAt: true,
                                },
                            },
                        },
                    },
                },
            },
        },
    });
};
export const findUserRecipesByUserId = async (userId) => {
    return await db.user_recipe.findMany({
        where: { userId },
    });
};
export const findRecipesByLevelAndIds = async (recipeIds) => {
    return await db.crafting_recipe.findMany({
        select: {
            id: true,
            craftTime: true,
            requiredSkillType: true,
            requiredSkillLevel: true,
            cost: true,
            recipe_item: {
                select: {
                    count: true,
                    itemType: true,
                    item: {
                        omit: {
                            createdAt: true,
                            updatedAt: true,
                        },
                    },
                },
            },
        },
        where: {
            OR: [{ isUnlockable: false }, { id: { in: recipeIds } }],
        },
    });
};
export const findRecipeById = async (recipeId) => {
    return await db.crafting_recipe.findUnique({
        where: { id: recipeId },
    });
};
export const createRecipeWithTransaction = async (recipeData, tx) => {
    return await tx.crafting_recipe.create({
        data: recipeData,
    });
};
export const addItemToRecipe = async (recipe, itemId, data, tx) => {
    return await tx.recipe_item.create({
        data: {
            craftingRecipeId: recipe.id,
            itemId,
            count: data.count,
            itemType: data.itemType,
        },
    });
};
export const removeItemFromRecipe = async (recipe, item, tx) => {
    return await tx.recipe_item.delete({
        where: {
            craftingRecipeId_itemId: {
                craftingRecipeId: recipe.id,
                itemId: item.id,
            },
        },
    });
};
export const updateRecipe = async (recipe, data, tx) => {
    return await tx.crafting_recipe.update({
        where: { id: recipe.id },
        data: data,
    });
};
export const deleteRecipeItems = async (recipeId, tx) => {
    return await tx.recipe_item.deleteMany({
        where: { craftingRecipeId: recipeId },
    });
};
export const deleteRecipeById = async (recipeId) => {
    await db.$transaction([
        db.recipe_item.deleteMany({
            where: { craftingRecipeId: recipeId },
        }),
        db.crafting_recipe.delete({
            where: { id: recipeId },
        }),
    ]);
};
export const findCraftingQueueByUserIdAndCraftId = async (userId, craftId) => {
    return await db.user_crafting_queue.findFirst({
        where: {
            userId,
            id: craftId,
        },
    });
};
export const deleteCraftingQueue = async (craftingQueue) => {
    return await db.user_crafting_queue.delete({
        where: { id: craftingQueue.id },
    });
};
export const createUserCraftingQueue = async (queueData) => {
    return await db.user_crafting_queue.create({
        data: queueData,
    });
};
export const findRecipeItemsByRecipeId = async (recipeId, itemType) => {
    return await db.recipe_item.findMany({
        where: {
            craftingRecipeId: recipeId,
            ...(itemType && { itemType }),
        },
    });
};
