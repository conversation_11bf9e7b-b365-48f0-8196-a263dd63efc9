import { levelGatesConfig } from "../config/gameConfig.js";
import * as UserRepository from "../repositories/user.repository.js";
import * as DailyQuestHelper from "../features/dailyquest/dailyquest.helpers.js";
import * as MissionHelper from "../features/mission/mission.helpers.js";
import { getNow } from "../utils/dateHelpers.js";
import { logger } from "../utils/log.js";
const { DAILY_QUESTS_LEVEL_GATE } = levelGatesConfig.public;
export const checkMissionCompletion = async (req, res, next) => {
    try {
        const user = req.user;
        const currentTime = getNow();
        if (user?.missionEnds && currentTime.getTime() > Number(user.missionEnds)) {
            await MissionHelper.CompleteMission(user.id);
        }
    }
    catch (error) {
        logger.error("Error completing mission:" + error);
    }
    next();
};
export const checkDailyFatigueReset = async (req, res, next) => {
    try {
        const user = req.user;
        if (user) {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const lastReset = user.lastFatigueReset ? new Date(user.lastFatigueReset) : null;
            const shouldReset = !lastReset || lastReset < today;
            if (shouldReset) {
                await UserRepository.updateUserStats({ id: user.id }, {
                    dailyFatigueUsed: 0,
                    lastFatigueReset: today,
                });
                user.dailyFatigueUsed = 0;
                user.lastFatigueReset = today;
            }
        }
    }
    catch (error) {
        logger.error("Error checking daily fatigue reset:" + error);
    }
    next();
};
export const checkDailyQuestGenerated = async (req, res, next) => {
    try {
        const user = req.user;
        if (user && (user.level || 0) < DAILY_QUESTS_LEVEL_GATE) {
            return next();
        }
        await DailyQuestHelper.GenerateDailyQuestsForUser(user);
    }
    catch (error) {
        logger.error("Error generating daily quests:" + error);
    }
    next();
};
export default {
    checkMissionCompletion,
    checkDailyFatigueReset,
    checkDailyQuestGenerated,
};
