import z from "zod";
export const getNotificationListSchema = z.object({
    limit: z.number().int().min(0).optional(),
});
export const markNotificationReadSchema = z.object({
    notificationId: z.number().int().positive(),
});
export const saveFCMTokenSchema = z.object({
    token: z.string().min(1),
});
export const updatePushNotificationSettingsSchema = z.object({
    pushEnabled: z.boolean(),
});
export const notificationSchema = {
    getNotificationList: getNotificationListSchema,
    markNotificationRead: markNotificationReadSchema,
    saveFCMToken: saveFCMTokenSchema,
    updatePushNotificationSettings: updatePushNotificationSettingsSchema,
};
export default {
    getNotificationListSchema,
    markNotificationReadSchema,
    saveFCMTokenSchema,
    updatePushNotificationSettingsSchema,
};
