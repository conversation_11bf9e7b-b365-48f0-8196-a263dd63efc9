import CasinoController from "./casino.controller.js";
import { checkLotteryEntrySchema, enterLotterySchema, gambleSchema } from "./casino.validation.js";
import RouletteController from "./roulette.controller.js";
import authHelper from "../../middleware/authMiddleware.js";
import { validate } from "../../middleware/validate.js";
import { routeHandler } from "../../utils/routeHandler.js";
import { Router } from "express";

const router = Router();

router.post(
    "/gamble",
    authHelper.IsLoggedInAndCanMakeStateChanges,
    validate(gambleSchema),
    routeHandler(async (req) => {
        const { amount } = req.body;
        return await CasinoController.gamble(req.user.id, amount);
    })
);

router.post(
    "/lottery/enter",
    authHelper.IsLoggedInAndCanMakeStateChanges,
    validate(enterLotterySchema),
    routeHandler(async (req) => {
        const { lotteryId } = req.body;
        return await CasinoController.enterLottery(req.user.id, lotteryId);
    })
);

router.get(
    "/lottery",
    authHelper.IsLoggedIn,
    routeHandler(async () => {
        return await CasinoController.getLottery();
    })
);

router.get(
    "/lottery/check-entry",
    authHelper.IsLoggedIn,
    validate(checkLotteryEntrySchema),
    routeHandler(async (req) => {
        const lotteryId = req.query.id;
        return await CasinoController.checkLotteryEntry(req.user.id, Number(lotteryId));
    })
);

router.post(
    "/roulette/placeBet",
    authHelper.IsLoggedInAndCanMakeStateChanges,
    routeHandler(async (req) => {
        const { bets } = req.body;
        return await RouletteController.placeBet(req.user.id, bets);
    })
);

export default router;
