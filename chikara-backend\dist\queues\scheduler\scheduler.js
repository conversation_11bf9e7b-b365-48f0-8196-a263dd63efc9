import { promises as fs } from "node:fs";
import path from "node:path";
import { fileURLToPath } from "node:url";
import config from "./config.js";
import createQueueAndWorker from "./createQueueAndWorker.js";
import processDailyMissions from "./tasks/missions/processDailyMissions.js";
import checkDonationGoal from "./tasks/shrine/checkDonationGoal.js";
import { logger } from "../../utils/log.js";
import { QueueEvents } from "bullmq";
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const queues = new Map();
const queueEvents = new Map();
function setupJobCompletionTracking(queue) {
    const queueEvent = new QueueEvents(queue.name, { connection: queue.opts.connection });
    queueEvent.on("completed", ({ jobId }) => {
        logger.debug(`[SCHEDULER] Job ${jobId} completed successfully`);
    });
    queueEvent.on("failed", ({ jobId, failedReason }) => {
        logger.error(`[SCHEDULER] Job ${jobId} failed:` + failedReason);
    });
    queueEvents.set(queue.name, queueEvent);
}
async function runStartupTasks() {
    await checkDonationGoal();
    await processDailyMissions();
}
export async function initializeScheduler() {
    await runStartupTasks();
    const tasksDir = path.join(__dirname, "tasks");
    const directories = await fs.readdir(tasksDir);
    for (const dir of directories) {
        if (dir === "__tests__")
            continue;
        const indexPath = path.join(tasksDir, dir, "index.js");
        try {
            const tasks = await import(indexPath);
            if (!(dir in config.tasks)) {
                logger.warn(`[SCHEDULER] No configuration found for task directory: ${dir}`);
                continue;
            }
            const taskSchedules = config.tasks[dir];
            const queue = await createQueueAndWorker(dir, tasks, taskSchedules);
            setupJobCompletionTracking(queue);
            queues.set(dir, queue);
        }
        catch (error) {
            logger.error(`[SCHEDULER] Error initializing queue ${dir}: ` + error);
        }
    }
    logger.debug("[SCHEDULER] All queues initialized");
}
export async function closeScheduler() {
    logger.debug("[SCHEDULER] Closing all queues and queue events");
    const queueEventPromises = [];
    for (const [name, queueEvent] of queueEvents.entries()) {
        queueEventPromises.push(queueEvent
            .close()
            .then(() => logger.debug(`[SCHEDULER] Closed queue event for ${name}`))
            .catch((error) => logger.error(`[SCHEDULER] Error closing queue event for ${name}:`, error)));
    }
    await Promise.allSettled(queueEventPromises);
    queueEvents.clear();
    const queuePromises = [];
    for (const [name, queue] of queues.entries()) {
        queuePromises.push(queue
            .close()
            .then(() => logger.debug(`[SCHEDULER] Closed queue ${name}`))
            .catch((error) => logger.error(`[SCHEDULER] Error closing queue ${name}:`, error)));
    }
    await Promise.allSettled(queuePromises);
    queues.clear();
    logger.debug("[SCHEDULER] All scheduler resources closed successfully");
}
export async function resetAllQueues() {
    try {
        for (const [queueName, queue] of queues.entries()) {
            await queue.obliterate({ force: true });
            const tasks = await import(path.join(__dirname, "tasks", queueName, "index.js"));
            if (!(queueName in config.tasks)) {
                logger.warn(`[SCHEDULER] No configuration found for queue: ${queueName}`);
                continue;
            }
            const taskSchedules = config.tasks[queueName];
            const newQueue = await createQueueAndWorker(queueName, tasks, taskSchedules);
            queues.set(queueName, newQueue);
        }
        logger.debug("[SCHEDULER] All queues have been reset successfully");
    }
    catch (error) {
        logger.error("[SCHEDULER] Error resetting queues: " + error);
        throw error;
    }
}
