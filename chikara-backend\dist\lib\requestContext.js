import { AsyncLocalStorage } from "node:async_hooks";
const requestContextStorage = new AsyncLocalStorage();
export const setRequestContext = (context, callback) => {
    return requestContextStorage.run(context, callback);
};
export const getRequestContext = () => {
    return requestContextStorage.getStore();
};
export const getCurrentUserId = () => {
    return getRequestContext()?.userId;
};
export const getCurrentIpAddress = () => {
    return getRequestContext()?.ipAddress;
};
export const getCurrentUserAgent = () => {
    return getRequestContext()?.userAgent;
};
export const getCurrentCorrelationId = () => {
    return getRequestContext()?.correlationId;
};
