import * as UserService from "../../../core/user.service.js";
import { logAction } from "../../../lib/actionLogger.js";
import { createBattleState, generateBattleId, sanitizeBattleStateForFrontend } from "../battle.state.js";
import { battleConfig, roguelikeConfig } from "../../../config/gameConfig.js";
import * as BattleHelpers from "../helpers/battle.helpers.js";
import { LogErrorStack } from "../../../utils/log.js";
const { PVP_BATTLE_AP_COST, ROOFTOP_BATTLE_AP_COST } = battleConfig.public;
const { BATTLE_TIMEOUT_MS } = battleConfig.hidden;
const { NORMAL_NPC_BATTLE_TIMEOUT_MS, BOSS_BATTLE_TIMEOUT_MS } = roguelikeConfig.hidden;
const calculateBattleTimeout = (battleType, target) => {
    switch (battleType) {
        case "pve":
        case "pve-explore": {
            const npcTarget = target;
            const isBoss = npcTarget?.boss || false;
            return Date.now() + (isBoss ? BOSS_BATTLE_TIMEOUT_MS : NORMAL_NPC_BATTLE_TIMEOUT_MS);
        }
        case "pvp":
        case "pve-rooftop":
        default: {
            return Date.now() + BATTLE_TIMEOUT_MS;
        }
    }
};
const logBattleAction = (battleType, userId, battleId, target) => {
    if (battleType === "pvp") {
        logAction({
            action: "BATTLE_INITIATED",
            userId,
            info: {
                battleId,
                battleType,
                targetId: target.id,
                targetName: "username" in target ? target.username : target.name,
            },
        });
    }
    else {
        const npcTarget = target;
        logAction({
            action: "BATTLE_STARTED_NPC",
            userId,
            info: {
                npcId: npcTarget.id,
                npcName: npcTarget.name,
                npcLevel: npcTarget.level,
                npcHealth: npcTarget.currentHealth || npcTarget.health,
                isBoss: "boss" in npcTarget ? npcTarget.boss : false,
            },
        });
    }
};
export const initiateBattle = async (currentUser, target, battleType, validUntil) => {
    const validationError = await BattleHelpers.GetBeginBattleError(currentUser, target, battleType);
    if (validationError) {
        return { error: validationError };
    }
    const currentUserId = currentUser.id.toString();
    const targetId = battleType === "pvp" ? target.id.toString() : `npc_${target.id}`;
    const battleId = generateBattleId(currentUserId, targetId, battleType);
    const updateValues = {};
    if (battleType === "pvp") {
        updateValues.actionPoints = {
            decrement: PVP_BATTLE_AP_COST,
        };
    }
    if (battleType === "pve-rooftop") {
        updateValues.actionPoints = {
            decrement: ROOFTOP_BATTLE_AP_COST,
        };
    }
    await UserService.updateUser(currentUser.id, updateValues);
    const battleValidUntil = validUntil || calculateBattleTimeout(battleType, target);
    const battleState = await createBattleState(currentUser, target, battleId, battleValidUntil, battleType);
    if (!battleState) {
        LogErrorStack({
            error: new Error("Battle state not found"),
        });
        return { error: "Battle state not found" };
    }
    const sanitizedBattleState = sanitizeBattleStateForFrontend(battleState);
    logBattleAction(battleType, currentUser.id, battleState.id, target);
    return {
        data: {
            battleId,
            attackerId: currentUserId,
            defenderId: targetId,
            state: sanitizedBattleState,
        },
    };
};
