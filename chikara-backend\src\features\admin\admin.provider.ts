import { db } from "../../lib/db.js";
import routeHandler from "../../utils/routeHandler.js";
import { Request, Response, Router } from "express";

// Define types for model names in Prisma
type PrismaModelName = string;

interface GetListParams {
    modelName: PrismaModelName;
    sort?: string;
    range?: string;
    filter?: string;
}

interface GetOneParams {
    modelName: PrismaModelName;
    id: string | number;
}

interface GetManyParams {
    modelName: PrismaModelName;
    filter?: string;
}

interface CreateParams {
    modelName: PrismaModelName;
    body: Record<string, unknown>;
}

interface UpdateParams {
    modelName: PrismaModelName;
    id: string | number;
    body: Record<string, unknown>;
}

interface UpdateManyParams {
    modelName: PrismaModelName;
    filter: string;
    data: Record<string, unknown>;
}

interface DeleteParams {
    modelName: PrismaModelName;
    id: string | number;
}

interface DeleteManyParams {
    modelName: PrismaModelName;
    filter: string;
}

interface ResponseData<T = unknown> {
    data?: T;
    headers?: Record<string, string>;
    error?: string;
    statusCode?: number;
}

// Helper function to get the Prisma model by name
const getPrismaModel = (modelName: string) => {
    // @ts-expect-error - Dynamic access to Prisma models
    return db[modelName.toLowerCase()];
};

const handleGetList = async ({ modelName, sort, range, filter }: GetListParams): Promise<ResponseData> => {
    const model = getPrismaModel(modelName);
    const whereClause: Record<string, unknown> = filter ? JSON.parse(filter) : {};
    const orderBy: Record<string, string>[] = [];

    if (sort) {
        const [field, direction] = JSON.parse(sort);
        orderBy.push({ [field]: direction.toLowerCase() });
    }

    let skip: number = 0;
    let take: number | undefined;

    if (range) {
        const [start, end] = JSON.parse(range) as [number, number];
        take = end - start + 1;
        skip = start;
    }

    // Build the query options
    const queryOptions: {
        where: Record<string, unknown>;
        orderBy?: Record<string, string>[];
        skip?: number;
        take?: number;
    } = {
        where: whereClause,
        orderBy: orderBy.length > 0 ? orderBy : undefined,
        skip: skip,
        take: take,
    };

    // Execute the query
    const result = await model.findMany(queryOptions);
    const count = await model.count({ where: whereClause });

    return {
        data: result,
        headers: { "Content-Range": `items ${skip}-${skip + result.length - 1}/${count}` },
    };
};

const handleGetOne = async ({ modelName, id }: GetOneParams): Promise<ResponseData> => {
    const model = getPrismaModel(modelName);
    const parsedId = typeof id === "string" && !Number.isNaN(Number(id)) ? Number(id) : id;

    const result = await model.findUnique({
        where: { id: parsedId },
    });

    if (result) return { data: result };
    return { error: "Not found" };
};

const handleGetMany = async ({ modelName, filter }: GetManyParams): Promise<ResponseData> => {
    const model = getPrismaModel(modelName);
    let whereClause: Record<string, unknown> = {};

    if (filter) {
        const parsedFilter = JSON.parse(filter) as Record<string, unknown>;
        if (parsedFilter.id) {
            whereClause = {
                id: {
                    in: Array.isArray(parsedFilter.id) ? parsedFilter.id : [parsedFilter.id],
                },
            };
        } else {
            whereClause = parsedFilter;
        }
    }

    const result = await model.findMany({
        where: whereClause,
    });

    return { data: result };
};

const handleCreate = async ({ modelName, body }: CreateParams): Promise<ResponseData> => {
    const model = getPrismaModel(modelName);

    const result = await model.create({
        data: body as Record<string, unknown>,
    });

    return { data: result, statusCode: 201 };
};

const handleUpdate = async ({ modelName, id, body }: UpdateParams): Promise<ResponseData> => {
    const model = getPrismaModel(modelName);
    const parsedId = typeof id === "string" && !Number.isNaN(Number(id)) ? Number(id) : id;

    try {
        const result = await model.update({
            where: { id: parsedId },
            data: body as Record<string, unknown>,
        });

        return { data: result };
    } catch (error) {
        return { error: error instanceof Error ? error.message : "Unknown error" };
    }
};

const handleUpdateMany = async ({ modelName, filter, data }: UpdateManyParams): Promise<ResponseData> => {
    const model = getPrismaModel(modelName);
    const whereClause = JSON.parse(filter);

    const result = await model.updateMany({
        where: whereClause,
        data: data as Record<string, unknown>,
    });

    return { data: result };
};

const handleDelete = async ({ modelName, id }: DeleteParams): Promise<ResponseData> => {
    const model = getPrismaModel(modelName);
    const parsedId = typeof id === "string" && !Number.isNaN(Number(id)) ? Number(id) : id;

    try {
        await model.delete({
            where: { id: parsedId },
        });

        return { statusCode: 204 };
    } catch (error) {
        return { error: error instanceof Error ? error.message : "Unknown error" };
    }
};

const handleDeleteMany = async ({ modelName, filter }: DeleteManyParams): Promise<ResponseData> => {
    const model = getPrismaModel(modelName);
    const whereClause = JSON.parse(filter);

    await model.deleteMany({
        where: whereClause,
    });

    return { statusCode: 204 };
};

type AuthMiddleware = (req: Request, res: Response, next: () => void) => void;

const generateRoutesForModel = (router: Router, modelName: string, auth: AuthMiddleware): void => {
    const routePath = `/${modelName}s`;

    router.get(
        routePath,
        auth,
        routeHandler(async (req) => {
            const { sort, range, filter } = req.query as {
                sort?: string;
                range?: string;
                filter?: string;
            };
            return await handleGetList({ modelName, sort, range, filter });
        })
    );

    router.get(
        `${routePath}/:id`,
        auth,
        routeHandler(async (req) => {
            return await handleGetOne({ modelName, id: req.params.id });
        })
    );

    router.get(
        routePath,
        auth,
        routeHandler(async (req) => {
            const { filter } = req.query as { filter?: string };
            return await handleGetMany({ modelName, filter });
        })
    );

    router.post(
        routePath,
        auth,
        routeHandler(async (req) => {
            return await handleCreate({ modelName, body: req.body });
        })
    );

    router.put(
        `${routePath}/:id`,
        auth,
        routeHandler(async (req) => {
            return await handleUpdate({ modelName, id: req.params.id, body: req.body });
        })
    );

    router.put(
        routePath,
        auth,
        routeHandler(async (req) => {
            const { filter, data } = req.body as { filter: string; data: Record<string, unknown> };
            return await handleUpdateMany({ modelName, filter, data });
        })
    );

    router.delete(
        `${routePath}/:id`,
        auth,
        routeHandler(async (req) => {
            return await handleDelete({ modelName, id: req.params.id });
        })
    );

    router.delete(
        routePath,
        auth,
        routeHandler(async (req) => {
            const { filter } = req.body as { filter: string };
            return await handleDeleteMany({ modelName, filter });
        })
    );
};

export const generateAdminRoutes = (router: Router, modelNames: string[], auth: AuthMiddleware): void => {
    modelNames.forEach((modelName) => {
        generateRoutesForModel(router, modelName, auth);
    });
};
