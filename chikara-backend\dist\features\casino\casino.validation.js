import { casinoConfig } from "../../config/gameConfig.js";
import z from "zod";
const { SLOTS_MAX_BET } = casinoConfig.public;
export const gambleSchema = z.object({
    amount: z.number().positive().max(SLOTS_MAX_BET),
});
export const checkLotteryEntrySchema = z.object({
    id: z.string(),
});
export const enterLotterySchema = z.object({
    lotteryId: z.number().int().positive(),
});
export const placeBetSchema = z.record(z.string(), z.number().positive());
export default {
    gambleSchema,
    checkLotteryEntrySchema,
    enterLotterySchema,
    placeBetSchema,
};
