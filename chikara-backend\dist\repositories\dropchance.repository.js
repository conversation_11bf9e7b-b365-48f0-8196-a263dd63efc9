import { db } from "../lib/db.js";
import { DropChanceTypes } from "@prisma/client";
export const findAllDrops = async () => {
    return await db.drop_chance.findMany({
        include: {
            item: {
                select: {
                    id: true,
                    name: true,
                    itemType: true,
                    rarity: true,
                    level: true,
                    about: true,
                    cashValue: true,
                    image: true,
                    damage: true,
                    armour: true,
                    health: true,
                    energy: true,
                    actionPoints: true,
                    baseAmmo: true,
                    itemEffects: true,
                    recipeUnlockId: true,
                },
            },
        },
    });
};
export const findDropChanceById = async (id) => {
    return await db.drop_chance.findUnique({
        where: { id },
    });
};
export const createDropChance = async (data) => {
    return await db.drop_chance.create({
        data: data,
    });
};
export const updateDropChance = async (dropChance, data) => {
    return await db.drop_chance.update({
        where: { id: dropChance.id },
        data: data,
    });
};
export const deleteDropChanceById = async (id) => {
    return await db.drop_chance.delete({
        where: { id },
    });
};
export const getAllDropChanceTypes = () => {
    return Object.values(DropChanceTypes);
};
