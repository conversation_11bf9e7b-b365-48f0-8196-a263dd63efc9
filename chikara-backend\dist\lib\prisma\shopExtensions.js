import { shopConfig } from "../../config/gameConfig.js";
import { Prisma } from "@prisma/client";
const { SHOP_ITEM_COST_MULTIPLIER } = shopConfig.public;
export default Prisma.defineExtension((client) => {
    return client.$extends({
        result: {
            shop_listing: {
                cost: {
                    needs: {
                        id: true,
                        customCost: true,
                        itemId: true,
                    },
                    compute(shop_listing) {
                        if (shop_listing.customCost !== null) {
                            return shop_listing.customCost;
                        }
                        const item = shop_listing.item;
                        if (item && item.cashValue !== null) {
                            return Math.round(item.cashValue * SHOP_ITEM_COST_MULTIPLIER);
                        }
                        return null;
                    },
                },
            },
        },
    });
});
