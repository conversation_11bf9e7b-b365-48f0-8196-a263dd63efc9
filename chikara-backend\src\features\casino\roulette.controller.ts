import crypto from "node:crypto";
import { getRedisItem, redisClient } from "../../config/redisClient.js";
import { logAction } from "../../lib/actionLogger.js";
import { getNow } from "../../utils/dateHelpers.js";
import { Server } from "socket.io";
import * as UserRepository from "../../repositories/user.repository.js";

interface BetData {
    userId: number;
    amount: number;
    type: string;
    [key: string]: unknown;
}

interface PayoutScale {
    "1ST_DOZEN": number;
    "2ND_DOZEN": number;
    "3RD_DOZEN": number;
    BLACK: number;
    RED: number;
    EVEN: number;
    ODD: number;
    "1_TO_18": number;
    "19_TO_36": number;
}

const PAYOUT_SCALE: PayoutScale = {
    "1ST_DOZEN": 3,
    "2ND_DOZEN": 3,
    "3RD_DOZEN": 3,
    BLACK: 2,
    RED: 2,
    EVEN: 2,
    ODD: 2,
    "1_TO_18": 2,
    "19_TO_36": 2,
};

type BetTypeMap = Record<string, number[]>;

const BET_TYPE_MAP: BetTypeMap = {
    RED: [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36],
    BLACK: [2, 4, 6, 8, 10, 11, 13, 15, 17, 20, 22, 24, 26, 28, 29, 31, 33, 35],
    ODD: Array.from({ length: 18 }, (_, i) => 2 * i + 1),
    EVEN: Array.from({ length: 18 }, (_, i) => 2 * i + 2),
    "1ST_DOZEN": Array.from({ length: 12 }, (_, i) => i + 1),
    "2ND_DOZEN": Array.from({ length: 12 }, (_, i) => i + 13),
    "3RD_DOZEN": Array.from({ length: 12 }, (_, i) => i + 25),
    "1_TO_18": Array.from({ length: 18 }, (_, i) => i + 1),
    "19_TO_36": Array.from({ length: 18 }, (_, i) => i + 19),
};

export const saveBetToRedis = async (betKey: string, betData: BetData) => {
    return await redisClient.set(betKey, JSON.stringify(betData));
};

export const getActiveBets = async () => {
    const betKeys = await redisClient.keys("roulette_bet:*");
    const bets = [];

    for (const key of betKeys) {
        const bet = (await getRedisItem(key)) as BetData;
        bets.push({ key, bet });
    }

    return bets;
};

export const deleteBet = async (betKey: string) => {
    return await redisClient.del(betKey);
};

function getRandomRouletteWinBet(layoutType: "european" | "american" = "european"): string {
    const possibleWinBets = ["0", ...Array.from({ length: 36 }, (_, i) => `${i + 1}`)];
    if (layoutType === "american") possibleWinBets.push("00");

    const randomBuffer = crypto.randomBytes(4);
    return possibleWinBets[randomBuffer.readUInt32BE(0) % possibleWinBets.length];
}

interface Bet {
    userId: number;
    type: string;
    amount: number;
    numbers: number[];
    placedAt: string;
    [key: string]: unknown;
}

function calculatePayout(bet: Bet | BetData, winningNumber: string): number {
    // Cast to Bet to ensure numbers property is available
    const betWithNumbers = bet as Bet;
    return betWithNumbers.numbers?.includes(Number(winningNumber)) ? PAYOUT_SCALE[bet.type as keyof PayoutScale] : 0;
}

type BetInput = Record<string, number>;

interface PlaceBetResult {
    data: {
        totalAmount: number;
        betCount: number;
    };
}

export async function placeBet(userId: number, bets: BetInput): Promise<PlaceBetResult> {
    const totalAmount = Object.values(bets).reduce((sum, amount) => sum + amount, 0);
    const betEntries: string[] = [];

    await UserRepository.decrementUserCash(userId, totalAmount);

    for (const [betType, amount] of Object.entries(bets)) {
        const betId = crypto.randomBytes(16).toString("hex");
        const betKey = `roulette_bet:${betId}`;

        const betData: Bet = {
            userId,
            type: betType,
            amount,
            numbers: BET_TYPE_MAP[betType] || [],
            placedAt: getNow().toISOString(),
        };

        await saveBetToRedis(betKey, betData);
        betEntries.push(betKey);
    }

    return { data: { totalAmount, betCount: betEntries.length } };
}

export async function processSpin(winningNumber: string) {
    const activeBets = await getActiveBets();
    const results = [];

    for (const { key: betKey, bet } of activeBets) {
        const payout = calculatePayout(bet, winningNumber);

        if (payout > 0) {
            const winAmount = bet.amount * payout;
            await UserRepository.incrementUserCash(bet.userId, winAmount);

            logAction({
                action: "CASINO_ROULETTE_WIN",
                userId: bet.userId,
                info: {
                    winnings: winAmount,
                    betType: bet.type,
                    amount: bet.amount,
                },
            });

            results.push({
                userId: bet.userId,
                amount: winAmount,
                betType: bet.type,
            });
        }
        await deleteBet(betKey);
    }

    return { winningNumber, results };
}

export function startRouletteInterval(ioInstance: Server): void {
    const io = ioInstance;
    setInterval(async () => {
        const winningNumber = getRandomRouletteWinBet();
        const spinResult = await processSpin(winningNumber);
        io.emit("spin_result", spinResult);
    }, 60_000); // 1 minute interval
}

export default {
    startRouletteInterval,
    calculatePayout,
    getRandomRouletteWinBet,
    placeBet,
    processSpin,
};
