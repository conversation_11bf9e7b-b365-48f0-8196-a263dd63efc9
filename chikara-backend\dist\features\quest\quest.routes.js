import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as QuestController from "./quest.controller.js";
import questSchema from "./quest.validation.js";
import z from "zod";
export const questRouter = {
    getAvailable: isLoggedInAuth.handler(async ({ context }) => {
        const quests = await QuestController.GetAvailableQuests(context.user);
        return handleResponse(quests);
    }),
    getActive: isLoggedInAuth.handler(async ({ context }) => {
        const quests = await QuestController.GetActiveQuestList(context.user);
        return handleResponse(quests);
    }),
    getCompleted: isLoggedInAuth.handler(async ({ context }) => {
        const quests = await QuestController.GetCompletedQuestList(context.user);
        return handleResponse(quests);
    }),
    getStoryQuests: isLoggedInAuth.handler(async ({ context }) => {
        const storyQuests = await QuestController.GetStoryQuestList(context.user);
        return handleResponse(storyQuests);
    }),
    getProgress: isLoggedInAuth
        .input(z.object({
        activeOnly: z.boolean().optional().default(false),
    }))
        .handler(async ({ input, context }) => {
        const { activeOnly } = input;
        const progress = await QuestController.GetUserQuestProgress(context.user.id, activeOnly);
        return handleResponse(progress);
    }),
    getCombinedList: isLoggedInAuth.handler(async ({ context }) => {
        const quests = await QuestController.GetCombinedQuestList(context.user);
        return handleResponse(quests);
    }),
    start: canMakeStateChangesAuth.input(questSchema.startQuestSchema).handler(async ({ input, context }) => {
        const result = await QuestController.StartQuest(context.user.id, input.id);
        return handleResponse(result);
    }),
    complete: canMakeStateChangesAuth.input(questSchema.completeQuestSchema).handler(async ({ input, context }) => {
        const result = await QuestController.CompleteQuest(context.user.id, input.id);
        return handleResponse(result);
    }),
    handInItem: canMakeStateChangesAuth.input(questSchema.handInItemSchema).handler(async ({ input, context }) => {
        const { objectiveId, itemId } = input;
        const result = await QuestController.HandInItem(context.user.id, objectiveId, itemId);
        return handleResponse(result);
    }),
};
export default questRouter;
