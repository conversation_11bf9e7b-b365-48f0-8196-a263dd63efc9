import { db } from "../lib/db.js";
import { QuestObjectiveTypes } from "../types/quest.js";
import { QuestProgressStatus } from "@prisma/client";
export const findPotentialDropsForRoguelike = async (userLevel, mapLocation) => {
    return await db.drop_chance.findMany({
        where: {
            dropChanceType: "roguelike",
            location: {
                in: [mapLocation, "any"],
            },
            minLevel: {
                lte: userLevel,
            },
            maxLevel: {
                gte: userLevel,
            },
            dropRate: {
                gt: 0,
            },
        },
        orderBy: {
            dropRate: "asc",
        },
    });
};
export const findApplicableQuestsForDrops = async (userId, mapLocation) => {
    return await db.quest_progress.findMany({
        where: {
            userId,
            questStatus: QuestProgressStatus.in_progress,
        },
        include: {
            quest: {
                include: {
                    quest_objective: {
                        where: {
                            objectiveType: {
                                in: [QuestObjectiveTypes.ACQUIRE_ITEM],
                            },
                            location: mapLocation,
                        },
                    },
                },
            },
        },
    });
};
export const findDropChanceByItemId = async (itemId) => {
    return await db.drop_chance.findFirst({
        where: {
            itemId,
        },
    });
};
export const findScavengeDrops = async (userLevel, mapLocation, scavengeType) => {
    return await db.drop_chance.findMany({
        where: {
            dropChanceType: "scavenge",
            scavengeType,
            location: {
                in: [mapLocation, "any"],
            },
            minLevel: {
                lte: userLevel,
            },
            maxLevel: {
                gte: userLevel,
            },
            dropRate: {
                gt: 0,
            },
        },
        include: {
            item: {
                omit: {
                    createdAt: true,
                    updatedAt: true,
                },
            },
        },
        orderBy: {
            dropRate: "asc",
        },
    });
};
