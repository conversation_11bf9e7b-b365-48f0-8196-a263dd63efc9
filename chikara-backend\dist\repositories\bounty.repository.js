import { db } from "../lib/db.js";
export async function findAllBounties() {
    return await db.bounty.findMany();
}
export async function findActiveBountiesWithUsers() {
    return await db.bounty.findMany({
        where: { active: true },
        include: {
            placer: {
                select: {
                    id: true,
                    username: true,
                    avatar: true,
                },
            },
            target: {
                select: {
                    id: true,
                    username: true,
                    avatar: true,
                },
            },
        },
    });
}
export async function findActiveBountyByTargetId(targetId) {
    return await db.bounty.findFirst({
        where: {
            AND: [{ targetId: targetId }, { active: true }],
        },
    });
}
export async function createBounty(data) {
    return await db.bounty.create({
        data,
    });
}
export async function updateBountyAmount(bountyId, newAmount) {
    return await db.bounty.update({
        where: { id: bountyId },
        data: { amount: newAmount },
    });
}
export async function updateBountyClaimStatus(bountyId, claimedById, active) {
    return await db.bounty.update({
        where: { id: bountyId },
        data: {
            active,
            claimedById,
        },
    });
}
export async function deleteBountyById(id) {
    await db.bounty.delete({
        where: { id },
    });
}
export async function getUserTypeAndLevel(userId) {
    const user = await db.user.findUnique({
        where: { id: userId },
        select: {
            userType: true,
            level: true,
        },
    });
    if (!user)
        return null;
    return user;
}
