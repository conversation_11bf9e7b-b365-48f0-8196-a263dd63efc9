import * as TalentHelper from "../features/talents/talents.helpers.js";
import { logAction } from "../lib/actionLogger.js";
import { db } from "../lib/db.js";
import { LogErrorStack } from "../utils/log.js";
export const GetUserStatusEffects = async (user) => {
    try {
        const now = Date.now();
        const effects = await db.user_status_effect.findMany({
            where: { userId: user.id, endsAt: { gt: now } },
            include: { effect: true },
        });
        return effects || null;
    }
    catch (error) {
        LogErrorStack({ error });
        throw new Error(error);
    }
};
export const removeUserStatusEffects = async (userId) => {
    await db.user_status_effect.deleteMany({
        where: { userId },
    });
};
export const GetRandomInjury = async (tier = "Minor", source = null) => {
    try {
        const whereClause = {
            tier,
            disabled: false,
        };
        if (source) {
            whereClause.source = source;
        }
        const injuries = await db.status_effect.findMany({
            where: whereClause,
        });
        if (!injuries || injuries.length === 0) {
            return null;
        }
        const randomIndex = Math.floor(Math.random() * injuries.length);
        return injuries[randomIndex];
    }
    catch (error) {
        LogErrorStack({ error });
        return null;
    }
};
export const GetInjuryOfType = async (injuryType, tier = "Minor") => {
    try {
        const injuries = await db.status_effect.findMany({
            where: {
                tier,
                disabled: false,
                category: injuryType,
            },
        });
        if (!injuries || injuries.length === 0)
            return null;
        const randomIndex = Math.floor(Math.random() * injuries.length);
        return injuries[randomIndex];
    }
    catch (error) {
        LogErrorStack({ error });
        return null;
    }
};
export const GetBattleStatusEffects = async (user) => {
    try {
        const effects = await GetUserStatusEffects(user);
        if (!effects)
            return {};
        const effectValues = effects.reduce((acc, effect) => {
            if (!effect.effect)
                return acc;
            const key = `${effect.effect.category}_${effect.effect.effectType.toLowerCase()}`;
            const value = (effect.effect.modifier ?? 0) * effect.stacks;
            if (acc[key]) {
                switch (effect.effect.modifierType) {
                    case "multiply": {
                        acc[key].value *= value;
                        break;
                    }
                    case "add": {
                        acc[key].value += value;
                        break;
                    }
                    case "divide": {
                        if (value !== 0) {
                            acc[key].value /= value;
                        }
                        break;
                    }
                }
            }
            else {
                switch (effect.effect.modifierType) {
                    case "multiply": {
                        acc[key] = { value };
                        break;
                    }
                    case "add": {
                        acc[key] = { value: 1 + value };
                        break;
                    }
                    case "divide": {
                        acc[key] = { value: value === 0 ? 1 : 1 / value };
                        break;
                    }
                    default: {
                        acc[key] = { value: 1 };
                    }
                }
            }
            return acc;
        }, {});
        return effectValues;
    }
    catch (error) {
        LogErrorStack({ error });
        return {};
    }
};
export const ApplyStatusEffectToUser = async (user, effect, customName = null) => {
    try {
        const existingEffect = await db.user_status_effect.findFirst({
            where: {
                userId: user.id,
                effectId: effect.id,
                customName: customName,
            },
        });
        if (effect.effectType === "DEBUFF" && effect.category === "damage") {
            const strongBonesTalent = await TalentHelper.UserHasStrongBonesTalent(user.id);
            if (strongBonesTalent?.modifier && Math.random() < strongBonesTalent.modifier) {
                return null;
            }
        }
        if (existingEffect) {
            if (!effect.stackable) {
                const effectDuration = BigInt(effect.duration);
                existingEffect.endsAt = BigInt(Date.now()) + effectDuration;
                await db.user_status_effect.update({
                    where: { id: existingEffect.id },
                    data: existingEffect,
                });
                return existingEffect;
            }
            else if (effect.maxStacks && existingEffect.stacks >= effect.maxStacks) {
                const existingEndsAt = Number(existingEffect.endsAt);
                const effectDuration = effect.duration;
                existingEffect.endsAt = BigInt(existingEndsAt + Math.floor(effectDuration / 2));
                await db.user_status_effect.update({
                    where: { id: existingEffect.id },
                    data: existingEffect,
                });
                return existingEffect;
            }
            const existingEndsAt = Number(existingEffect.endsAt);
            const effectDuration = effect.duration;
            existingEffect.endsAt = BigInt(existingEndsAt + Math.floor(effectDuration / 2));
            existingEffect.stacks += 1;
            await db.user_status_effect.update({
                where: { id: existingEffect.id },
                data: existingEffect,
            });
            return existingEffect;
        }
        const currentTime = BigInt(Date.now());
        const effectDuration = BigInt(effect.duration);
        const newTime = currentTime + effectDuration;
        const newEffect = await db.user_status_effect.create({
            data: {
                customName: customName,
                userId: user.id,
                effectId: effect.id,
                endsAt: newTime,
                stacks: 1,
            },
        });
        return newEffect;
    }
    catch (error) {
        LogErrorStack({ message: `Error applying status effect`, error });
        return null;
    }
};
const tiers = ["Minor", "Moderate", "Severe", "Critical"];
export const removeStatusEffect = async (userId, effectType, tier, amount = 1) => {
    const now = Date.now();
    if (!tier || !effectType) {
        return false;
    }
    const effectWhere = {
        source: `${effectType}_injury`,
    };
    if (tier !== "All") {
        const tierIndex = tiers.indexOf(tier);
        if (tierIndex === -1) {
            return false;
        }
        effectWhere.tier = {
            in: tiers.slice(0, tierIndex + 1),
        };
    }
    const effects = await db.user_status_effect.findMany({
        where: {
            userId,
            endsAt: { gt: now },
            effect: effectWhere,
        },
        include: { effect: true },
        orderBy: [{ effect: { tier: "desc" } }, { createdAt: "asc" }],
    });
    if (!effects || effects.length === 0)
        return false;
    const effect = effects[0];
    logAction({
        action: "STATUS_EFFECT_REMOVED",
        userId: userId,
        info: {
            effectName: effect.effect?.name ?? "Unknown",
            amount: amount,
        },
    });
    if (effect.stacks === undefined || effect.stacks === null)
        return false;
    if (effect.stacks > amount) {
        effect.stacks -= amount;
        await db.user_status_effect.update({
            where: { id: effect.id },
            data: { stacks: effect.stacks },
        });
        return true;
    }
    await db.user_status_effect.delete({ where: { id: effect.id } });
    return true;
};
