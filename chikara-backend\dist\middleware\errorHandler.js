import { stringify } from "../utils/jsonHelper.js";
import { LogErrorStack } from "../utils/log.js";
const errorResponse = {
    success: false,
    data: null,
    error: "An unexpected error occurred",
};
const errorHandler = (err, req, res, next) => {
    if (res.headersSent) {
        return next(err);
    }
    LogErrorStack({ message: "Unhandled error" + err, error: err });
    return res.status(500).send(stringify(errorResponse));
};
export default errorHandler;
