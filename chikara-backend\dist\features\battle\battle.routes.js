import * as Battle<PERSON>ontroller from "./battle.controller.js";
import battleSchema from "./battle.validation.js";
import * as BattleResolver from "./logic/battle.resolver.js";
import authHelper from "../../middleware/authMiddleware.js";
import validate from "../../middleware/validate.js";
import routeHandler from "../../utils/routeHandler.js";
import express from "express";
const router = express.Router();
router.get("/status", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await BattleController.getBattleStatus(req.user.id);
}));
router.post("/begin", authHelper.IsLoggedInAndCanMakeStateChanges, validate(battleSchema.beginBattle), routeHandler(async (req) => {
    if (req.body.battleOpponentId == req.user.id) {
        return { error: "Stop hitting yourself" };
    }
    return await BattleController.initiatePVPBattle(req.user.id, req.body.battleOpponentId);
}));
router.post("/attack", authHelper.IsLoggedIn, validate(battleSchema.attack), routeHandler(async (req) => {
    return await BattleController.processAttack(req.user.id, req.body.action);
}));
router.post("/flee", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await BattleController.processAttack(req.user.id, "flee");
}));
router.get("/rooftopList", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await BattleController.listRooftopBattles(req.user.id);
}));
router.post("/begin-npc", authHelper.IsLoggedInAndCanMakeStateChanges, validate(battleSchema.beginRooftopBattle), routeHandler(async (req) => {
    return await BattleController.initiateRooftopBattle(req.user.id, req.body.battleOpponentId);
}));
router.post("/postBattleAction", authHelper.IsLoggedIn, validate(battleSchema.postBattleAction), routeHandler(async (req) => {
    return await BattleResolver.processVictoryAction(req.user.id, req.body.action);
}));
export default router;
