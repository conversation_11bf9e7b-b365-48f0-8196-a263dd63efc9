import * as Dev<PERSON>ontroller from "./dev.controller.js";
import * as DevRepository from "../../repositories/dev.repository.js";
import * as RoguelikeController from "../roguelike/roguelike.controller.js";
import authHelper from "../../middleware/authMiddleware.js";
import { NotificationTypes } from "../../types/notification.js";
import routeHandler from "../../utils/routeHandler.js";
import { Router } from "express";

const router = Router();

// TEST ROUTES - TO BE REMOVED
router.get(
    "/notify",
    authHelper.IsLoggedIn,
    routeHandler(async (req) => {
        return await DevController.SendNotification(
            req.user.id,
            req.query.type as NotificationTypes,
            req.query.details as string
        );
    })
);

router.post(
    "/email",
    authHelper.IsAdmin,
    routeHandler(async () => {
        return await DevController.TestEmail();
    })
);

router.post(
    "/send-ai-chat",
    authHelper.IsAdmin,
    routeHandler(async () => {
        return await DevController.SendAIChatMessage();
    })
);

// Dev mode hax
router.post(
    "/addxp",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        return await DevController.AddXp(req.user.id, req.body.xp);
    })
);

router.post(
    "/fullheal",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        return await DevController.FullHeal(req.user.id);
    })
);

router.post(
    "/fullhealall",
    authHelper.IsDevEnv,
    routeHandler(async () => {
        return await DevController.FullHealEveryone();
    })
);

router.post(
    "/pvpbattle",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        return await DevController.StartRandomPVPBattle(req.user.id);
    })
);

router.post(
    "/resetquests",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        return await DevController.ResetQuests(req.user.id);
    })
);

router.post(
    "/randomroguelike",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        const result = await DevController.StartRandomMap(req.user.id);
        // Special case for roguelike which needs to call another controller
        if (result.data) {
            const { level, location } = result.data;
            req.body = { level, location };
            // We need to handle this differently since roguelikeController.newRun expects req and res
            // This is a temporary solution until roguelikeController is also updated
            return new Promise((resolve) => {
                RoguelikeController.newRun(req.user.id, req.body.level, req.body.location).then((res) => {
                    resolve(res);
                });
            });
        }
        return result;
    })
);

router.post(
    "/addcash",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        return await DevController.AddCash(req.user.id, req.body.cash);
    })
);

router.post(
    "/addstats",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        return await DevController.AddStats(req.user.id, req.body.amount);
    })
);

router.post(
    "/removestats",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        return await DevController.RemoveStats(req.user.id, req.body.amount);
    })
);

router.post(
    "/addallitems",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        return await DevController.AddAllItems(req.user.id);
    })
);

router.post(
    "/completequests",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        return await DevController.CompleteAllQuests(req.user.id);
    })
);

router.post(
    "/resetBattles",
    authHelper.IsDevEnv,
    routeHandler(async () => {
        return await DevController.cleanupAllBattles();
    })
);

router.post(
    "/removeAllEffects",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        const selectedUser = req.body.userId || req.user.id;
        return await DevController.RemoveAllEffects(selectedUser);
    })
);

router.post(
    "/addRandomEffects",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        const selectedUser = req.body.userId || req.user.id;
        return await DevController.AddRandomEffects(selectedUser);
    })
);

router.post(
    "/additem",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        const { itemId, quantity = 1 } = req.body;
        if (!itemId) {
            return { error: "itemId is required", statusCode: 400 };
        }
        return await DevController.AddItem(req.user.id, Number(itemId), Number(quantity));
    })
);

// Pets
router.post(
    "/petslist",
    authHelper.IsDevEnv,
    routeHandler(async () => {
        const petsList = await DevRepository.findPetsList();
        return { data: petsList };
    })
);

router.post(
    "/hatcheggs",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        return await DevController.HatchEggs(req.user.id);
    })
);

router.post(
    "/fullpethappiness",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        return await DevController.SetAllPetsHappiness(req.user.id);
    })
);

router.post(
    "/addpetxp",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        const xpAmount = req.body.xp || 100;
        return await DevController.AddXpToAllPets(req.user.id, Number(xpAmount));
    })
);

router.post(
    "/deleteexplorenodes",
    authHelper.IsDevEnv,
    routeHandler(async (req) => {
        return await DevController.DeleteExploreNodes(req.user.id);
    })
);

export default router;
