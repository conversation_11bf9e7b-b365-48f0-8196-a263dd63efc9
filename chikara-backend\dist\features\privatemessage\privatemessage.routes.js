import { isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import privateMessageController from "./privatemessage.controller.js";
import privateMessageSchema from "./privatemessage.validation.js";
export const privateMessageRouter = {
    getChatHistory: isLoggedInAuth.handler(async ({ context }) => {
        const result = await privateMessageController.getChatHistory(context.user.id);
        return handleResponse(result);
    }),
    getUnreadCount: isLoggedInAuth.handler(async ({ context }) => {
        const result = await privateMessageController.getNumberOfUnreadMessages(context.user.id);
        return handleResponse(result);
    }),
    sendMessage: isLoggedInAuth.input(privateMessageSchema.sendMessage).handler(async ({ input, context }) => {
        const result = await privateMessageController.SendMessage(context.user.id, input.userId, input.message);
        return handleResponse(result);
    }),
    markMessageRead: isLoggedInAuth.input(privateMessageSchema.markMessageRead).handler(async ({ input, context }) => {
        const result = await privateMessageController.MarkMessageRead(context.user.id, input.messageId);
        return handleResponse(result);
    }),
};
export default privateMessageRouter;
