import { HEALTH_THRESHOLD, PRIORITY_MULTIPLIERS } from "./battle.ai.js";
export const evaluateHealOverTimeAbility = (aiPlayer, defender, healthPercentage, missingHealthAmount, staminaCost) => {
    let score;
    const totalHealingPotential = aiPlayer.maxHealth * 0.15 * 3;
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score = PRIORITY_MULTIPLIERS.LOW_HEALTH_HEALING;
    }
    else if (healthPercentage < 0.4) {
        score = 3.0;
    }
    else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score = 1.5;
    }
    else if (healthPercentage < HEALTH_THRESHOLD.MEDIUM) {
        score = 0.7;
    }
    else {
        score = 0.2;
    }
    if (totalHealingPotential > missingHealthAmount * 2) {
        score *= 0.6;
    }
    const enemyHealthPercentage = defender.currentHealth / defender.maxHealth;
    if (enemyHealthPercentage < 0.3 && healthPercentage > 0.4) {
        score *= 0.7;
    }
    if (aiPlayer.currentStamina < staminaCost * 1.5) {
        score *= 0.8;
    }
    return score;
};
export const evaluateMaxHPHealAbility = (aiPlayer, defender, healthPercentage, missingHealthAmount, staminaCost) => {
    let score;
    const healingAmount = aiPlayer.maxHealth * 0.75;
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score = PRIORITY_MULTIPLIERS.LOW_HEALTH_HEALING + 1.0;
    }
    else if (healthPercentage < 0.4) {
        score = 3.5;
    }
    else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score = 1.7;
    }
    else {
        score = 0.1;
    }
    if (healingAmount > missingHealthAmount * 1.5) {
        score *= 0.4;
    }
    if (aiPlayer.currentStamina < staminaCost * 1.3) {
        score *= 0.7;
    }
    const enemyHealthPercentage = defender.currentHealth / defender.maxHealth;
    if (enemyHealthPercentage < 0.2 && healthPercentage > 0.3) {
        score *= 0.5;
    }
    return score;
};
export const evaluateHealingAbility = (ability, aiPlayer, defender) => {
    let score = 1.0;
    const healthPercentage = aiPlayer.currentHealth / aiPlayer.maxHealth;
    const missingHealthAmount = aiPlayer.maxHealth - aiPlayer.currentHealth;
    if (!ability.staminaCost) {
        return 0;
    }
    switch (ability.name) {
        case "heal_over_time": {
            score = evaluateHealOverTimeAbility(aiPlayer, defender, healthPercentage, missingHealthAmount, ability.staminaCost);
            break;
        }
        case "max_hp_heal": {
            score = evaluateMaxHPHealAbility(aiPlayer, defender, healthPercentage, missingHealthAmount, ability.staminaCost);
            break;
        }
    }
    if (aiPlayer.currentStamina < ability.staminaCost * 1.5) {
        score *= 0.8;
    }
    return score;
};
