import { getRedisItem, redisClient } from "../../config/redisClient.js";
import * as EquipmentService from "../../core/equipment.service.js";
import * as NotificationService from "../../core/notification.service.js";
import * as StatusEffectService from "../../core/statuseffect.service.js";
import * as UserService from "../../core/user.service.js";
import { BATTLE_STATE } from "./helpers/battle.constants.js";
import gameConfig from "../../config/gameConfig.js";
import * as BattleEquipment from "./helpers/battle.equipment.js";
import { GetMaxStamina } from "./helpers/battle.helpers.js";
import { getInitiativeModifier } from "./helpers/battle.scaling.js";
import type {
    BattlePlayer,
    BattleState,
    BattleStatusEffects,
    BattleSummary,
    BattleType,
    CombatLogEntry,
    NPCUser,
    RooftopNPCUser,
    SanitizedBattlePlayer,
    SanitizedBattleState,
} from "./types/battle.types.js";
import * as TalentHelper from "../talents/talents.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import { ExtUserModel } from "../../lib/db.js";
import { LogErrorStack, logger } from "../../utils/log.js";
import { getAllUserStatLevels } from "../user/user.helpers.js";
import * as UserRepository from "../../repositories/user.repository.js";
import { Prisma } from "@prisma/client";

const { BASE_STAMINA } = gameConfig;

// Add Redis key prefix constant
const REDIS_KEY_PREFIX = {
    BATTLE: "battle",
    PLAYER_ACTIVE_BATTLE: "player",
} as const;

// Add NPC key prefix constant
const NPC_ID_PREFIX = "npc_";

// Helper to convert user/npc ID to string format
const formatPlayerId = (id: number | string, userType: "player" | "npc" | "rooftop_npc"): string => {
    if (userType === "player") {
        return id.toString();
    }
    return `${NPC_ID_PREFIX}${id}`;
};

// Redis key helpers
const getBattleKey = (battleId: string) => `${REDIS_KEY_PREFIX.BATTLE}:${battleId}`;

const getPlayerActiveBattleKey = (playerId: string) =>
    `${REDIS_KEY_PREFIX.PLAYER_ACTIVE_BATTLE}:${playerId}:activeBattle`;

export const generateBattleId = (currentUserId: string, targetId: string, battleType: string) => {
    return `battle_${battleType}_${Date.now()}_${currentUserId}_${targetId}`;
};

// Add interfaces/types at the top

export const getActiveBattleForUser = async (userId: string): Promise<BattleState | null> => {
    const battleId = await redisClient.get(getPlayerActiveBattleKey(userId));
    if (!battleId || typeof battleId !== "string") return null;

    return (await getRedisItem(getBattleKey(battleId))) as BattleState;
};

export const listBattles = async (): Promise<{ data: BattleSummary[] } | { error: string }> => {
    try {
        // Get all battle keys from Redis
        const battleKeys = await redisClient.keys(`${REDIS_KEY_PREFIX.BATTLE}:*`);

        if (battleKeys.length === 0) {
            return { data: [] };
        }

        // Get all battle states
        const battleStates = await Promise.all(
            battleKeys.map(async (key) => {
                const battleData = await redisClient.get(key);
                if (!battleData || typeof battleData !== "string") return null;
                return JSON.parse(battleData);
            })
        );

        // Filter out null values and only include active battles
        const activeBattles = battleStates
            .filter(
                (battle): battle is BattleState =>
                    battle !== null && battle.state === BATTLE_STATE.IN_PROGRESS && Date.now() <= battle.validUntil
            )
            .map((battle) => ({
                id: battle.id,
                battleType: battle.battleType,
                startTime: battle.startTime,
                validUntil: battle.validUntil,
                currentRound: battle.currentRound,
                aggressorId: battle.aggressorId,
                players: Object.values(battle.players).map((player) => ({
                    id: player.id,
                    username: player.username,
                    currentHealth: player.currentHealth,
                    maxHealth: player.maxHealth,
                    userType: player.userType,
                    // Note: Sensitive data like attributes, weaponDamage, ammo, damageTaken, abilities excluded
                })),
            }));

        return { data: activeBattles };
    } catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to list battles" };
    }
};

export const saveBattleState = async (
    battleState: BattleState,
    battleId: string,
    currentUser: ExtUserModel,
    target?: ExtUserModel | NPCUser | RooftopNPCUser
): Promise<void> => {
    const isNPCBattle = battleState.battleType !== "pvp";

    const multi = redisClient
        .multi()
        .set(getBattleKey(battleId), JSON.stringify(battleState))
        .set(getPlayerActiveBattleKey(currentUser.id.toString()), battleId);
    // .expire(getBattleKey(battleId), Math.ceil(BATTLE_TIMEOUT_MS / 1000));

    if (!isNPCBattle && target) {
        multi.set(getPlayerActiveBattleKey(target.id.toString()), battleId);
    }

    await multi.exec();
};

const createPlayerState = async (
    user: ExtUserModel | NPCUser | RooftopNPCUser,
    userType: "player" | "npc" | "rooftop_npc",
    initialPlayerDebuffs?: BattleStatusEffects
) => {
    const isNPC = userType === "npc" || userType === "rooftop_npc";
    const battleStatusEffects = isNPC
        ? (user as NPCUser | RooftopNPCUser).battleStatusEffects
        : await StatusEffectService.GetBattleStatusEffects(user as ExtUserModel);

    const playerId = formatPlayerId(user.id, userType);

    let buffs;
    if ((user as ExtUserModel).roguelikeMap) {
        try {
            const roguelikeMap = (user as ExtUserModel).roguelikeMap;
            buffs = {
                roguelikeStrBuff: roguelikeMap?.strBuff || 1,
                roguelikeDefBuff: roguelikeMap?.defBuff || 1,
                roguelikeDexBuff: roguelikeMap?.dexBuff || 1,
            };
        } catch (error) {
            LogErrorStack({ message: "Failed to parse roguelikeMap", error });
            buffs = {
                roguelikeStrBuff: 1,
                roguelikeDefBuff: 1,
                roguelikeDexBuff: 1,
            };
        }
    }
    let equipment = null;

    if (!isNPC) {
        equipment = await EquipmentService.GetEquippedItems(user.id);
    }

    const statusEffects = {
        ...battleStatusEffects,
        ...initialPlayerDebuffs,
    };

    // Get stats from user_skill model for players
    let playerStats = {
        strength: isNPC ? (user as NPCUser).strength : 1,
        defence: isNPC ? (user as NPCUser).defence : 1,
        dexterity: isNPC ? 0 : 1,
        intelligence: isNPC ? 0 : 1,
        endurance: isNPC ? 0 : 1,
        vitality: isNPC ? 0 : 1,
    };

    if (!isNPC) {
        const userStats = await getAllUserStatLevels((user as ExtUserModel).id);
        playerStats = userStats;
    }

    // For stamina, use GetMaxStamina which includes buffs
    const finalMaxStamina = isNPC ? BASE_STAMINA : GetMaxStamina(playerStats.endurance, statusEffects, user.id);

    // For health, use user.maxHealth which includes vitality bonuses
    let finalMaxHealth: number;
    if (isNPC) {
        finalMaxHealth = user.health;
    } else {
        finalMaxHealth = await (user as ExtUserModel).maxHealth;
    }

    return {
        id: playerId,
        userType: userType,
        username: isNPC ? (user as NPCUser).name : (user as ExtUserModel).username,
        avatar: isNPC ? (user as NPCUser).image : (user as ExtUserModel).avatar || "",
        level: user.level,
        currentHealth: isNPC ? (user as NPCUser).health : (user as ExtUserModel).currentHealth,
        maxHealth: finalMaxHealth,
        currentStamina: finalMaxStamina,
        maxStamina: finalMaxStamina,
        attributes: {
            strength: playerStats.strength,
            defence: playerStats.defence,
            dexterity: playerStats.dexterity,
            intelligence: playerStats.intelligence,
            endurance: playerStats.endurance,
            vitality: playerStats.vitality,
        },
        weaponDamage: isNPC ? (user as NPCUser).weaponDamage : undefined,
        ammo: isNPC ? 999 : await BattleEquipment.GetAmmoForUser(user.id, equipment),
        damageTaken: 0,
        currentTurn: 0,
        statusEffects: {
            ...battleStatusEffects,
            ...initialPlayerDebuffs,
        },
        abilities: isNPC ? undefined : await TalentHelper.GetEquippedAbilities(user as ExtUserModel),
        buffs,
        equipment,
        isBoss: userType === "npc" ? (user as NPCUser)?.boss || false : false,
    };
};

const getDefenderType = (battleType: BattleType) => {
    if (battleType === "pvp") return "player";
    if (battleType === "pve-rooftop") return "rooftop_npc";
    return "npc";
};

// Determine who attacks first based on vitality (initiative)
const getFirstAttacker = (player: BattlePlayer, opponent: BattlePlayer) => {
    const playerInitiative =
        player.userType === "player" ? 0.5 + getInitiativeModifier(player.attributes.vitality) : 0.5; // Base 50% chance + vitality bonus
    const opponentInitiative =
        opponent.userType === "player" ? 0.5 + getInitiativeModifier(opponent.attributes.vitality) : 0.5; // Base 50% chance + vitality bonus
    // Add small random component to break ties
    const playerRoll = playerInitiative + Math.random() * 0.1;
    const opponentRoll = opponentInitiative + Math.random() * 0.1;
    return playerRoll > opponentRoll ? player : opponent;
};

export const createBattleState = async (
    currentUser: ExtUserModel,
    target: ExtUserModel | NPCUser | RooftopNPCUser,
    battleId: string,
    battleValidUntil: number,
    battleType: BattleType
) => {
    const initialPlayerDebuffs = battleType === "pve-rooftop" ? (target as RooftopNPCUser).initialDebuff : undefined;

    const player = await createPlayerState(currentUser, "player", initialPlayerDebuffs);
    const opponent = await createPlayerState(target, getDefenderType(battleType));

    const attacker = getFirstAttacker(player, opponent);

    const combatLog = [
        {
            id: 1,
            timestamp: Date.now(),
            round: 0,
            actorId: String(player.id),
            targetId: String(opponent.id),
            action: "battle_start",
        },
    ] as CombatLogEntry[];

    const battleState = {
        id: battleId,
        state: BATTLE_STATE.IN_PROGRESS,
        battleType,
        startTime: Date.now(),
        validUntil: battleValidUntil,
        currentRound: 1,
        aggressorId: player.id,
        firstAttackerId: attacker.id,
        combatLog: combatLog,
        players: {
            [player.id]: player,
            [opponent.id]: opponent,
        },
    };

    await saveBattleState(battleState, battleId, currentUser, target);

    return battleState;
};

export const updateBattleState = async (battleId: string, battleState: BattleState) => {
    return await redisClient.set(getBattleKey(battleId), JSON.stringify(battleState));
};

export const cleanupBattleState = async (battleState: BattleState) => {
    // Save the battle combat log to the database before cleaning up
    // await saveBattleCombatLog(battleState);

    // battleState.state = BATTLE_STATE.FINISHED;
    // await updateBattleState(battleState.id, battleState);

    await redisClient.del(getBattleKey(battleState.id));

    // Defensive check ‑ corrupted states may have no players object
    if (battleState.players && typeof battleState.players === "object") {
        for (const playerId of Object.keys(battleState.players)) {
            await redisClient.del(getPlayerActiveBattleKey(playerId));
        }
    }
};

export const handleBattleTimeout = async (battleState: BattleState) => {
    // If the battle state is missing player information, perform a best-effort cleanup and exit early
    if (!battleState.players || Object.keys(battleState.players).length === 0) {
        logger.warn(`handleBattleTimeout: Battle ${battleState.id} is missing player data – performing cleanup only`);
        await cleanupBattleState(battleState);
        return;
    }

    const aggressorId = Number.parseInt(battleState.aggressorId);

    // Find any opponent that is not the aggressor (may be undefined for corrupted states)
    const targetPlayer = Object.values(battleState.players).find((player) => player.id !== String(aggressorId));

    const user = await UserRepository.getUserById(aggressorId);
    if (!user) {
        logger.error(`User not found for battle timeout: ${aggressorId}`);
        await cleanupBattleState(battleState);
        return;
    }

    const updateValues: Prisma.userUpdateInput = {
        currentHealth: Math.ceil(user.currentHealth / 2),
    };

    // Clear roguelike zone
    if (battleState.battleType === "pve") {
        updateValues.roguelikeMap = Prisma.JsonNull;
    }
    await UserService.updateUser(aggressorId, updateValues);
    await cleanupBattleState(battleState);

    // Notify remaining players (if any) of the timeout
    if (battleState.players && typeof battleState.players === "object") {
        for (const playerId of Object.keys(battleState.players)) {
            if (playerId.startsWith(NPC_ID_PREFIX)) continue;
            NotificationService.NotifyUser(Number.parseInt(playerId), "fight_timeout", {
                battleId: battleState.id,
                battleType: battleState.battleType,
                aggressorId,
                targetId: targetPlayer && targetPlayer.id,
            });
        }
    }

    logAction({
        action: "BATTLE_TIMEOUT",
        userId: String(aggressorId),
        info: { battleId: battleState.id, battleType: battleState.battleType, targetId: targetPlayer },
    });
};

export const validateBattleState = async (userId: string) => {
    const battleState = await getActiveBattleForUser(userId);

    if (!battleState) {
        return { error: "Active battle not found" };
    }

    if (battleState.state === BATTLE_STATE.FINISHED) {
        return { error: "Battle is already over" };
    }

    if (Date.now() > battleState.validUntil) {
        logger.info(`Battle expired for user ID: ${userId}`);
        await handleBattleTimeout(battleState);
        return { error: "Battle has timed out" };
    }

    const playerState = battleState.players[userId];

    let targetState;

    if (battleState.battleType === "pvp") {
        targetState = Object.values(battleState.players as Record<string, BattlePlayer>).find(
            (player) => player.id !== userId
        );
    } else {
        const targetKey = Object.keys(battleState.players).find((key) => key.startsWith(NPC_ID_PREFIX));
        targetState = targetKey ? battleState.players[targetKey] : null;
    }

    return { battleState, playerState, targetState };
};

/**
 * Sanitizes battle state by removing sensitive player data before sending to frontend
 */
export const sanitizeBattleStateForFrontend = (battleState: BattleState): SanitizedBattleState => {
    const sanitizedPlayers: Record<string, SanitizedBattlePlayer> = {};

    for (const [playerId, player] of Object.entries(battleState.players)) {
        sanitizedPlayers[playerId] = {
            id: player.id,
            username: player.username,
            userType: player.userType,
            avatar: player.avatar,
            level: player.level,
            currentHealth: player.currentHealth,
            maxHealth: player.maxHealth,
            currentStamina: player.currentStamina,
            maxStamina: player.maxStamina,
            currentTurn: player.currentTurn,
            isBoss: player.isBoss,
            statusEffects: player.statusEffects,
            equipment: player.equipment,
        };
    }

    return {
        id: battleState.id,
        state: battleState.state,
        battleType: battleState.battleType,
        startTime: battleState.startTime,
        validUntil: battleState.validUntil,
        currentRound: battleState.currentRound,
        aggressorId: battleState.aggressorId,
        firstAttackerId: battleState.firstAttackerId,
        combatLog: battleState.combatLog,
        players: sanitizedPlayers,
    };
};
