import { adminAuth, canMakeStateChangesAuth, isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as JobController from "./job.controller.js";
import jobSchema from "./job.validation.js";
export const jobRouter = {
    list: isLoggedInAuth.handler(async () => {
        const response = await JobController.jobList();
        return handleResponse(response);
    }),
    info: isLoggedInAuth.handler(async ({ context }) => {
        const response = await JobController.getCurrentJobInfo(context.user.id);
        return handleResponse(response);
    }),
    apply: canMakeStateChangesAuth.input(jobSchema.applyForJob).handler(async ({ input, context }) => {
        const response = await JobController.applyForJob(context.user.id, input.jobId);
        return handleResponse(response);
    }),
    promote: canMakeStateChangesAuth.handler(async ({ context }) => {
        const response = await JobController.applyForPromotion(context.user.id);
        return handleResponse(response);
    }),
    getRequirements: isLoggedInAuth.input(jobSchema.getJobRequirements).handler(async ({ input }) => {
        const response = await JobController.GetJobLevelRequirement(input.level, input.jobId);
        return handleResponse(response);
    }),
    changePayoutTime: isLoggedInAuth.input(jobSchema.changePayoutTime).handler(async ({ input, context }) => {
        const response = await JobController.changeJobPayoutTime(context.user.id, input.time);
        return handleResponse(response);
    }),
    admin: {
        list: adminAuth.handler(async () => {
            const response = await JobController.fullJobList();
            return handleResponse(response);
        }),
    },
};
export default jobRouter;
