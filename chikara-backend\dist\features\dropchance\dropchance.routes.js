import { createDropChance, deleteDropChance, editDropChance } from "./dropchance.controller.js";
import * as authHelper from "../../middleware/authMiddleware.js";
import routeHandler from "../../utils/routeHandler.js";
import express from "express";
const router = express.Router();
router.post("/createDropChance", authHelper.IsAdmin, routeHandler(async (req) => {
    return await createDropChance(req.body);
}));
router.post("/editDropChance", authHelper.IsAdmin, routeHandler(async (req) => {
    return await editDropChance(req.body);
}));
router.post("/deleteDropChance", authHelper.IsAdmin, routeHandler(async (req) => {
    return await deleteDropChance(req.body.id);
}));
export default router;
