import { db } from "../lib/db.js";
import { getNow } from "../utils/dateHelpers.js";
export const getUserFriends = async (userId) => {
    return await db.friend.findMany({
        where: { userId },
        include: {
            friend: {
                select: {
                    id: true,
                    username: true,
                    level: true,
                    last_activity: true,
                    statusMessage: true,
                    statusMessageUpdatedAt: true,
                    showLastOnline: true,
                    gangId: true,
                    jailedUntil: true,
                    hospitalisedUntil: true,
                    avatar: true,
                    gang: {
                        select: {
                            name: true,
                        },
                    },
                },
            },
        },
    });
};
export const getFriendRequests = async (userId) => {
    return await db.friend_request.findMany({
        where: { receiverId: userId },
        include: {
            sender: {
                select: {
                    id: true,
                    username: true,
                    level: true,
                    avatar: true,
                    gangId: true,
                    gang: {
                        select: {
                            name: true,
                        },
                    },
                },
            },
        },
    });
};
export const findExistingFriendship = async (userId1, userId2) => {
    return await db.friend.findFirst({
        where: {
            OR: [
                { userId: userId1, friendId: userId2 },
                { userId: userId2, friendId: userId1 },
            ],
        },
    });
};
export const findExistingFriendRequest = async (userId1, userId2) => {
    return await db.friend_request.findFirst({
        where: {
            OR: [
                { senderId: userId1, receiverId: userId2 },
                { senderId: userId2, receiverId: userId1 },
            ],
        },
    });
};
export const createFriendRequest = async (senderId, receiverId) => {
    return await db.friend_request.create({
        data: {
            senderId,
            receiverId,
        },
    });
};
export const findFriendRequestById = async (requestId, userId) => {
    return await db.friend_request.findFirst({
        where: {
            id: requestId,
            receiverId: userId,
        },
    });
};
export const deleteFriendRequest = async (requestId) => {
    return await db.friend_request.delete({
        where: { id: requestId },
    });
};
export const createFriendship = async (userId, friendId) => {
    return await db.friend.create({
        data: {
            userId,
            friendId,
        },
    });
};
export const findFriendship = async (userId, friendId) => {
    return await db.friend.findFirst({
        where: { userId, friendId },
    });
};
export const getFriendshipDetails = async (userId, friendId) => {
    return await db.friend.findFirst({
        where: { userId, friendId },
        include: {
            friend: {
                select: {
                    username: true,
                    avatar: true,
                    gang: {
                        select: {
                            name: true,
                        },
                    },
                },
            },
        },
    });
};
export const deleteFriendships = async (userId1, userId2) => {
    return await db.friend.deleteMany({
        where: {
            OR: [
                { userId: userId1, friendId: userId2 },
                { userId: userId2, friendId: userId1 },
            ],
        },
    });
};
export const updateFriendshipNote = async (friendshipId, note) => {
    return await db.friend.update({
        where: { id: friendshipId },
        data: { note },
    });
};
export const updateUserStatusMessage = async (userId, message) => {
    return await db.user.update({
        where: { id: userId },
        data: {
            statusMessage: message,
            statusMessageUpdatedAt: message ? getNow() : null,
        },
        select: {
            statusMessage: true,
            statusMessageUpdatedAt: true,
        },
    });
};
export const updateUserPrivacySettings = async (userId, data) => {
    return await db.user.update({
        where: { id: userId },
        data,
        select: {
            showLastOnline: true,
        },
    });
};
export const getUserRivals = async (userId) => {
    return await db.rival.findMany({
        where: { userId },
        include: {
            rival: {
                select: {
                    id: true,
                    username: true,
                    level: true,
                    gangId: true,
                    avatar: true,
                    gang: {
                        select: {
                            name: true,
                        },
                    },
                    targetedBounties: {
                        where: { active: true },
                        select: {
                            id: true,
                            amount: true,
                        },
                    },
                },
            },
        },
    });
};
export const findExistingRival = async (userId, rivalId) => {
    return await db.rival.findFirst({
        where: { userId, rivalId },
    });
};
export const createRival = async (userId, rivalId) => {
    return await db.rival.create({
        data: {
            userId,
            rivalId,
        },
        include: {
            rival: {
                select: {
                    username: true,
                    avatar: true,
                    gangId: true,
                    gang: {
                        select: {
                            name: true,
                        },
                    },
                },
            },
        },
    });
};
export const deleteRival = async (rivalId) => {
    return await db.rival.delete({
        where: { id: rivalId },
    });
};
export const updateRivalNote = async (rivalId, note) => {
    return await db.rival.update({
        where: { id: rivalId },
        data: { note },
    });
};
