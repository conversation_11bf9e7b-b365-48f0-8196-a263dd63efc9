import { redisOptions } from "../../config/redisClient.js";
import { logger } from "../../utils/log.js";
import { Queue, Worker } from "bullmq";
const getRepeatOptions = (interval) => {
    const repeat = {};
    if (typeof interval === "number") {
        repeat.every = interval;
    }
    else if (typeof interval === "string") {
        repeat.pattern = interval;
    }
    return repeat;
};
const redisConfig = { connection: redisOptions };
async function createQueueAndWorker(queueName, tasks, jobConfig) {
    const queue = new Queue(queueName, redisConfig);
    const worker = new Worker(queueName, async (job) => {
        const jobFn = tasks[job.name];
        if (jobFn) {
            await jobFn();
        }
        else {
            throw new Error(`Unknown job type: ${job.name}`);
        }
    }, redisConfig);
    worker.on("completed", (job) => {
        logger.debug(`[SCHEDULER] Job completed: ${job.name}, Queue: ${queueName}`);
    });
    worker.on("failed", (job, err) => {
        logger.error(`[SCHEDULER] Job failed: ${job?.name}, Error: ${err.message}, Queue: ${queueName}`);
    });
    for (const [jobName, schedule] of Object.entries(jobConfig)) {
        const repeat = getRepeatOptions(schedule);
        await queue.upsertJobScheduler(jobName, repeat, {
            opts: { removeOnComplete: true, removeOnFail: true },
        });
    }
    return queue;
}
export default createQueueAndWorker;
