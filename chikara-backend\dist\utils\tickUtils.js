import gameConfig from "../config/gameConfig.js";
const { HEALING_TICK_INTERVAL, ENERGY_TICK_MS, AP_TICK_INTERVAL } = gameConfig;
const tickStartTime = Date.now();
let nextAPRegenTime = Date.now() + AP_TICK_INTERVAL;
export const GetNextHealingTick = function () {
    const currentTime = Date.now();
    const timeSinceLastTick = (currentTime - tickStartTime) % HEALING_TICK_INTERVAL;
    const lastTickTimestamp = currentTime - timeSinceLastTick;
    return lastTickTimestamp + HEALING_TICK_INTERVAL;
};
export const GetNextAPRegenTime = function () {
    return nextAPRegenTime;
};
export const SetNextAPRegenTime = function (time) {
    nextAPRegenTime = time;
};
