import { logAction } from "../../../../lib/actionLogger.js";
import { db } from "../../../../lib/db.js";
import { getEpochTime } from "../../../../utils/dateHelpers.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";
const User = db.user;
const UserAchievements = db.user_achievements;
const UserItem = db.user_item;
const ChatMessage = db.chat_message;
const GameStats = db.game_stats;
async function get24HrDailyStats(dailyStats) {
    const yesterdaysGameStatsJSON = await GameStats.findFirst({
        where: {
            stats_type: "daily_stats",
        },
        orderBy: {
            createdAt: "desc",
        },
    });
    if (yesterdaysGameStatsJSON && yesterdaysGameStatsJSON.info) {
        const yesterdaysGameStats = JSON.parse(yesterdaysGameStatsJSON.info);
        dailyStats.chatMessagesLast24h = dailyStats.totalChatMessages - yesterdaysGameStats.totalChatMessages;
        dailyStats.itemsCraftedLast24h = dailyStats.totalItemsCrafted - yesterdaysGameStats.totalItemsCrafted;
        dailyStats.yenInCirculationChange24h =
            dailyStats.totalYenInCirculation - yesterdaysGameStats.totalYenInCirculation;
        dailyStats.registrationsLast24H = dailyStats.totalUsers - yesterdaysGameStats.totalUsers;
        dailyStats.playersActiveLast24H = await User.count({
            where: {
                last_activity: {
                    gt: new Date(yesterdaysGameStats.date),
                },
            },
        });
    }
}
async function collectDailyStats() {
    logger.profile("collectDailyStats");
    try {
        const date = getEpochTime();
        const totalUsers = await User.count();
        const totalUserItems = await UserItem.count();
        const totalChatMessages = await ChatMessage.count();
        const totalItemsCrafted = await UserAchievements.aggregate({
            _sum: {
                craftsCompleted: true,
            },
        });
        const totalYenInBanks = await User.aggregate({
            _sum: {
                bank_balance: true,
            },
        });
        const totalYenInCash = await User.aggregate({
            _sum: {
                cash: true,
            },
        });
        const totalYenInCirculation = (totalYenInBanks._sum.bank_balance || 0) + (totalYenInCash._sum.cash || 0);
        const totalCoursesCompleted = await UserAchievements.aggregate({
            _sum: {
                coursesCompleted: true,
            },
        });
        const totalQuestsCompleted = await UserAchievements.aggregate({
            _sum: {
                questsCompleted: true,
            },
        });
        const totalPVPBattleWins = await UserAchievements.aggregate({
            _sum: {
                battleWins: true,
            },
        });
        const totalNPCBattleWins = await UserAchievements.aggregate({
            _sum: {
                npcBattleWins: true,
            },
        });
        const totalRoguelikeMapsCompleted = await UserAchievements.aggregate({
            _sum: {
                roguelikeMapsCompleted: true,
            },
        });
        const totalMarketItemsSold = await UserAchievements.aggregate({
            _sum: {
                marketItemsSold: true,
            },
        });
        const totalMarketMoneyMade = await UserAchievements.aggregate({
            _sum: {
                marketMoneyMade: true,
            },
        });
        const totalMuggingGain = await UserAchievements.aggregate({
            _sum: {
                totalMuggingGain: true,
            },
        });
        const totalMuggingLoss = await UserAchievements.aggregate({
            _sum: {
                totalMuggingLoss: true,
            },
        });
        const totalCasinoProfitLoss = await UserAchievements.aggregate({
            _sum: {
                totalCasinoProfitLoss: true,
            },
        });
        const totalDailyQuestsCompleted = await UserAchievements.aggregate({
            _sum: {
                dailyQuestsCompleted: true,
            },
        });
        const totalRoguelikeNodesCompleted = await UserAchievements.aggregate({
            _sum: {
                roguelikeNodesCompleted: true,
            },
        });
        const totalExamsCompleted = await UserAchievements.aggregate({
            _sum: {
                examsCompleted: true,
            },
        });
        const totalBountyRewards = await UserAchievements.aggregate({
            _sum: {
                totalBountyRewards: true,
            },
        });
        const totalMissionHours = await UserAchievements.aggregate({
            _sum: {
                totalMissionHours: true,
            },
        });
        const dailyStats = {
            date,
            totalUsers,
            totalUserItems,
            totalChatMessages,
            totalItemsCrafted: totalItemsCrafted._sum.craftsCompleted || 0,
            totalYenInBanks: totalYenInBanks._sum.bank_balance || 0,
            totalYenInCash: totalYenInCash._sum.cash || 0,
            totalYenInCirculation,
            totalCoursesCompleted: totalCoursesCompleted._sum.coursesCompleted || 0,
            totalQuestsCompleted: totalQuestsCompleted._sum.questsCompleted || 0,
            totalPVPBattleWins: totalPVPBattleWins._sum.battleWins || 0,
            totalNPCBattleWins: totalNPCBattleWins._sum.npcBattleWins || 0,
            totalRoguelikeMapsCompleted: totalRoguelikeMapsCompleted._sum.roguelikeMapsCompleted || 0,
            totalMarketItemsSold: totalMarketItemsSold._sum.marketItemsSold || 0,
            totalMarketMoneyMade: totalMarketMoneyMade._sum.marketMoneyMade || 0,
            totalMuggingGain: totalMuggingGain._sum.totalMuggingGain || 0,
            totalMuggingLoss: totalMuggingLoss._sum.totalMuggingLoss || 0,
            totalCasinoProfitLoss: totalCasinoProfitLoss._sum.totalCasinoProfitLoss || 0,
            totalDailyQuestsCompleted: totalDailyQuestsCompleted._sum.dailyQuestsCompleted || 0,
            totalRoguelikeNodesCompleted: totalRoguelikeNodesCompleted._sum.roguelikeNodesCompleted || 0,
            totalExamsCompleted: totalExamsCompleted._sum.examsCompleted || 0,
            totalBountyRewards: totalBountyRewards._sum.totalBountyRewards || 0,
            totalMissionHours: totalMissionHours._sum.totalMissionHours || 0,
        };
        await get24HrDailyStats(dailyStats);
        await GameStats.create({
            data: {
                playerId: 1,
                stats_type: "daily_stats",
                info: JSON.stringify(dailyStats),
            },
        });
        logAction({
            action: "DAILY_STATS_COLLECTED",
            userId: 1,
            info: {},
        });
    }
    catch (error) {
        LogErrorStack({ message: "Failed to process daily stats:", error });
    }
    logger.profile("collectDailyStats");
}
export default collectDailyStats;
