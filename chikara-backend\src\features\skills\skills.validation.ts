import { z } from "zod";

// Start mining validation
export const startMiningSchema = z.object({
    difficulty: z.enum(["easy", "medium", "hard", "expert"]).optional().default("easy"),
});

// Process swing validation
export const processSwingSchema = z.object({
    hitPosition: z.number().min(0).max(100, "Hit position must be between 0 and 100"),
    targetPosition: z.number().min(0).max(100, "Target position must be between 0 and 100"),
});

// Start scavenging validation
export const generateScavengingGridSchema = z.object({
    difficultyTier: z
        .number()
        .int()
        .min(1, "Difficulty tier must be at least 1")
        .max(12, "Difficulty tier must be at most 12")
        .optional(),
});

// Reveal cell validation
export const revealCellSchema = z.object({
    sessionId: z.string().min(1, "Session ID is required"),
    row: z.number().int().min(0, "Row must be non-negative"),
    col: z.number().int().min(0, "Column must be non-negative"),
});

// End session validation
export const endSessionSchema = z.object({
    sessionId: z.string().min(1, "Session ID is required"),
});

// Dev grid validation
export const devGridSchema = z.object({
    sessionId: z.string().min(1, "Session ID is required"),
});

// Reset grid validation
export const resetGridSchema = z.object({
    sessionId: z.string().min(1, "Session ID is required"),
});

// Export all schemas as a single object for consistency with other validation files
const skillsSchema = {
    startMining: startMiningSchema,
    processSwing: processSwingSchema,
    generateScavengingGrid: generateScavengingGridSchema,
    revealCell: revealCellSchema,
    endSession: endSessionSchema,
    devGrid: devGridSchema,
    resetGrid: resetGridSchema,
};

export default skillsSchema;
