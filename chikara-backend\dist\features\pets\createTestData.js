import { logger } from "../../utils/log.js";
const petsData = [
    {
        name: "Cat",
        species: "cat",
        maxLevel: 20,
        evolution_stages: [
            {
                stage: "egg",
                image: "static/Pets/generated-image.png",
                buffs: [
                    { name: "Agility", description: "+10% Movement Speed", icon: "running" },
                    { name: "Night Vision", description: "+15% Vision in Dark Areas", icon: "eye" },
                ],
            },
            {
                stage: "teen",
                image: "https://example.com/teen-cat.jpg",
                buffs: [
                    { name: "Agility", description: "+15% Movement Speed", icon: "running" },
                    { name: "Night Vision", description: "+20% Vision in Dark Areas", icon: "eye" },
                ],
            },
        ],
    },
    {
        name: "Dog",
        species: "dog",
        maxLevel: 30,
        evolution_stages: [
            {
                stage: "egg",
                image: "/images/pets/dog.jpg",
                buffs: [
                    { name: "Loyalty", description: "+10% Companion Damage", icon: "heart" },
                    { name: "<PERSON><PERSON>", description: "+15% Item Find Rate", icon: "nose" },
                ],
            },
            {
                stage: "adult",
                image: "/images/pets/adult-dog.jpg",
                buffs: [
                    { name: "Loyalty", description: "+20% Companion Damage", icon: "heart" },
                    { name: "Scent", description: "+25% Item Find Rate", icon: "nose" },
                    { name: "Guard", description: "-10% Damage Taken", icon: "shield" },
                ],
            },
        ],
    },
    {
        name: "Monkey",
        species: "monkey",
        maxLevel: 25,
        evolution_stages: [
            {
                stage: "egg",
                image: "chikara-assets/static/Pets/monkey_1.png",
                buffs: [
                    { name: "Dexterity", description: "+10% Crafting Speed", icon: "hand" },
                    { name: "Mischief", description: "+5% Critical Hit Chance", icon: "star" },
                ],
            },
            {
                stage: "baby",
                image: "/images/pets/baby-monkey.jpg",
                buffs: [
                    { name: "Dexterity", description: "+12% Crafting Speed", icon: "hand" },
                    { name: "Mischief", description: "+8% Critical Hit Chance", icon: "star" },
                ],
            },
        ],
    },
    {
        name: "Rabbit",
        species: "rabbit",
        maxLevel: 20,
        evolution_stages: [
            {
                stage: "egg",
                image: "static/Pets/bunny_1.png",
                buffs: [],
            },
            {
                stage: "baby",
                image: "static/Pets/bunny_2.png",
                buffs: [
                    { name: "Luck", description: "+10% Chance for Rare Drops", icon: "clover" },
                    { name: "Jump", description: "+15% Jump Height", icon: "arrow-up" },
                ],
            },
            {
                stage: "teen",
                image: "static/Pets/bunny_3.png",
                buffs: [
                    { name: "Luck", description: "+10% Chance for Rare Drops", icon: "clover" },
                    { name: "Jump", description: "+15% Jump Height", icon: "arrow-up" },
                ],
            },
        ],
    },
    {
        name: "Beetle",
        species: "beetle",
        maxLevel: 15,
        evolution_stages: [
            {
                stage: "egg",
                image: "/images/pets/beetle.jpg",
                buffs: [
                    { name: "Armor", description: "+12% Physical Defense", icon: "shield" },
                    { name: "Persistence", description: "+8% Stamina Regeneration", icon: "battery" },
                ],
            },
            {
                stage: "adult",
                image: "/images/pets/adult-beetle.jpg",
                buffs: [
                    { name: "Armor", description: "+18% Physical Defense", icon: "shield" },
                    { name: "Persistence", description: "+15% Stamina Regeneration", icon: "battery" },
                    { name: "Tough Shell", description: "-8% Environmental Damage", icon: "mountain" },
                ],
            },
        ],
    },
    {
        name: "Rat",
        species: "rat",
        maxLevel: 20,
        evolution_stages: [
            {
                stage: "egg",
                image: "/images/pets/rat.jpg",
                buffs: [
                    { name: "Scavenge", description: "+15% Material Gathering Speed", icon: "magnet" },
                    { name: "Stealth", description: "+10% Sneak Success Rate", icon: "mask" },
                ],
            },
            {
                stage: "teen",
                image: "/images/pets/teen-rat.jpg",
                buffs: [
                    { name: "Scavenge", description: "+20% Material Gathering Speed", icon: "magnet" },
                    { name: "Stealth", description: "+15% Sneak Success Rate", icon: "mask" },
                    {
                        name: "Adaptability",
                        description: "+10% Resistance to Status Effects",
                        icon: "shield-alt",
                    },
                ],
            },
        ],
    },
    {
        name: "Ferret",
        species: "ferret",
        maxLevel: 25,
        evolution_stages: [
            {
                stage: "egg",
                image: "/images/pets/ferret.jpg",
                buffs: [
                    { name: "Reflexes", description: "+12% Dodge Chance", icon: "bolt" },
                    { name: "Treasure Hunter", description: "+10% Chance to Find Hidden Items", icon: "gem" },
                ],
            },
        ],
    },
    {
        name: "Assembler Drone",
        species: "drone",
        maxLevel: 30,
        evolution_stages: [
            {
                stage: "egg",
                image: "/images/pets/assembler-drone.jpg",
                buffs: [
                    { name: "Auto-Craft", description: "+20% Crafting Speed for Basic Items", icon: "tools" },
                    { name: "Resource Scan", description: "+15% Resource Detection Range", icon: "radar" },
                ],
            },
            {
                stage: "upgraded",
                image: "/images/pets/upgraded-drone.jpg",
                buffs: [
                    { name: "Auto-Craft", description: "+30% Crafting Speed for Basic Items", icon: "tools" },
                    { name: "Resource Scan", description: "+25% Resource Detection Range", icon: "radar" },
                    { name: "Efficiency", description: "-15% Resource Cost for Crafting", icon: "cog" },
                ],
            },
        ],
    },
    {
        name: "Scrap Robot",
        species: "robot",
        maxLevel: 35,
        evolution_stages: [
            {
                stage: "egg",
                image: "/images/pets/scrap-robot.jpg",
                buffs: [
                    { name: "Salvage", description: "+25% Resources from Scrapping Items", icon: "recycle" },
                    { name: "Reinforcement", description: "+10% Durability to Equipped Items", icon: "hammer" },
                ],
            },
            {
                stage: "upgraded",
                image: "/images/pets/upgraded-robot.jpg",
                buffs: [
                    { name: "Salvage", description: "+35% Resources from Scrapping Items", icon: "recycle" },
                    { name: "Reinforcement", description: "+20% Durability to Equipped Items", icon: "hammer" },
                    { name: "Repair", description: "+10% Auto-Repair of Damaged Items", icon: "wrench" },
                ],
            },
        ],
    },
];
const userPetsData = [
    {
        userId: 1,
        petId: 1,
        name: "Whiskers",
        level: 8,
        happiness: 85,
        energy: 70,
        xp: 2450,
        nextLevelXp: 3000,
        isActive: true,
        evolution: {
            current: "egg",
            next: "baby",
            progress: 0,
            requiredLevel: 5,
        },
    },
    {
        userId: 2,
        petId: 2,
        name: "Rex",
        level: 12,
        happiness: 90,
        energy: 85,
        xp: 5200,
        nextLevelXp: 6000,
        isActive: false,
        evolution: {
            current: "egg",
            next: "baby",
            progress: 0,
            requiredLevel: 5,
        },
    },
    {
        userId: 3,
        petId: 3,
        name: "Bananas",
        level: 5,
        happiness: 75,
        energy: 90,
        xp: 1200,
        nextLevelXp: 2000,
        isActive: false,
        evolution: {
            current: "egg",
            next: "baby",
            progress: 0,
            requiredLevel: 5,
        },
    },
];
async function createTestData() {
    try {
        logger.info("Test data created successfully!");
        return { message: "Test data created successfully" };
    }
    catch (error) {
        logger.error("Error creating test data:" + error);
        throw error;
    }
}
export default createTestData;
