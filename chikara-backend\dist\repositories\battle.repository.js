import { db } from "../lib/db.js";
export const recordBattleOutcome = async (attackerId, defenderId, victory) => {
    await db.battle_log.create({
        data: {
            attackerId: Number.parseInt(attackerId),
            defenderId: Number.parseInt(defenderId),
            victory,
        },
    });
};
export const countBattleWinsAgainstTarget = async (attackerId, defenderId, today) => {
    return await db.battle_log.count({
        where: {
            attackerId: attackerId,
            defenderId: defenderId,
            victory: true,
            createdAt: {
                gte: today,
            },
        },
    });
};
export const findUserItemById = async (itemId) => {
    return await db.user_item.findUnique({
        where: {
            id: itemId,
        },
        include: {
            item: true,
        },
    });
};
export const findBountyByTargetId = async (targetId) => {
    return await db.bounty.findFirst({
        where: {
            targetId: targetId,
        },
    });
};
