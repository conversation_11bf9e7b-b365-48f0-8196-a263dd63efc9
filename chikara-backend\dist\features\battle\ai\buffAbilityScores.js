import { HEALTH_THRESHOLD, PRIORITY_MULTIPLIERS, calculateMeleeDamage } from "./battle.ai.js";
export const evaluateSelfHarmAbility = (aiPlayer, enemy, healthPercentage, battleProgress, isActive) => {
    let score = 1.5;
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 0.2;
    }
    else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 0.7;
    }
    else if (healthPercentage > 0.8) {
        score *= 1.3;
    }
    if (battleProgress < 0.3) {
        score *= 1.5;
    }
    else if (battleProgress > 0.7) {
        score *= 0.8;
    }
    const baseDamage = calculateMeleeDamage(aiPlayer, enemy);
    if (baseDamage * 2 >= enemy.currentHealth) {
        score *= 0.6;
    }
    if (aiPlayer.attributes.strength > 70 || aiPlayer.attributes.dexterity > 70) {
        score *= 1.4;
    }
    if (isActive) {
        score *= 0.3;
    }
    return score;
};
export const evaluateHighGuardAbility = (aiPlayer, enemy, healthPercentage, battleProgress, isActive) => {
    let score = 1.2;
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 2.5;
    }
    else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 1.8;
    }
    else if (healthPercentage > 0.8) {
        score *= 0.6;
    }
    const incomingDamage = calculateMeleeDamage(enemy, aiPlayer);
    if (incomingDamage > aiPlayer.currentHealth * 0.3) {
        score *= 2.0;
    }
    if (aiPlayer.attributes.defence > 60) {
        score *= 1.3;
    }
    if (isActive) {
        score *= 0.2;
    }
    if (battleProgress > 0.8) {
        score *= 0.5;
    }
    if (healthPercentage < 0.3 && aiPlayer.currentStamina >= 80) {
        score *= 0.7;
    }
    return score;
};
export const evaluateRageAbility = (aiPlayer, enemy, healthPercentage, battleProgress, isActive) => {
    let score = 1.8;
    if (aiPlayer.attributes.strength > 60) {
        score *= 1.5;
    }
    if (battleProgress < 0.4) {
        score *= 1.4;
    }
    if (aiPlayer.attributes.strength > aiPlayer.attributes.dexterity) {
        score *= 1.6;
    }
    else {
        score *= 0.7;
    }
    if (isActive) {
        score *= 0.2;
    }
    const baseDamage = calculateMeleeDamage(aiPlayer, enemy);
    const buffedDamage = baseDamage * (1 + 0.6);
    if (buffedDamage >= enemy.currentHealth && baseDamage < enemy.currentHealth) {
        score *= PRIORITY_MULTIPLIERS.KILLING_BLOW;
    }
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 0.7;
    }
    return score;
};
export const evaluateBuffAbility = (ability, aiPlayer, enemy) => {
    let score = 1.0;
    const healthPercentage = aiPlayer.currentHealth / aiPlayer.maxHealth;
    const enemyHealthPercentage = enemy.currentHealth / enemy.maxHealth;
    const battleProgress = 1 - enemyHealthPercentage;
    const isBuffActive = (buffName) => {
        return !!(aiPlayer.statusEffects &&
            aiPlayer.statusEffects[buffName]?.turns &&
            aiPlayer.statusEffects[buffName].turns > 0);
    };
    switch (ability.name) {
        case "self_harm": {
            const isActive = isBuffActive("self_harm");
            score = evaluateSelfHarmAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
        case "high_guard": {
            const isActive = isBuffActive("high_guard");
            score = evaluateHighGuardAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
        case "rage": {
            const isActive = isBuffActive("rage");
            score = evaluateRageAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
    }
    if (aiPlayer.currentStamina < 40) {
        score *= 0.8;
    }
    return score;
};
