import * as UserHelper from "../features/user/user.helpers.js";
import { auth } from "../lib/auth.js";
import { setRequestContext } from "../lib/requestContext.js";
import { fromNodeHeaders } from "better-auth/node";
import { randomUUID } from "node:crypto";
const generateCorrelationId = () => {
    return randomUUID();
};
const sessionMiddleware = async (req, res, next) => {
    const session = await auth.api.getSession({
        headers: fromNodeHeaders(req.headers),
    });
    const ipAddress = req.ip || req.connection.remoteAddress || req.headers["x-forwarded-for"];
    const userAgent = req.headers["user-agent"];
    const correlationId = generateCorrelationId();
    if (!session) {
        req.session = null;
        setRequestContext({
            ipAddress,
            userAgent,
            correlationId,
        }, () => next());
        return;
    }
    const fetchedUser = await UserHelper.GetUserByIdWithAssociations(Number.parseInt(session.user.id));
    req.user = fetchedUser;
    req.session = session.session;
    setRequestContext({
        userId: session.user.id,
        ipAddress,
        userAgent,
        sessionId: session.session.id,
        correlationId,
    }, () => next());
};
export default sessionMiddleware;
