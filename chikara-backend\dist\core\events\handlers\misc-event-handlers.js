import { logger, LogErrorStack } from "../../../utils/log.js";
import * as AchievementService from "../../achievement.service.js";
import * as QuestService from "../../quest.service.js";
import * as UserRepository from "../../../repositories/user.repository.js";
export const handleMissionCompletedEvent = async (payload) => {
    try {
        const { userId, missionId, hours } = payload;
        const user = await UserRepository.getUserById(userId);
        if (!user) {
            logger.error(`User not found for mission completed event: ${userId}`);
            return;
        }
        await QuestService.handleMissionComplete(userId);
        await AchievementService.UpdateUserAchievement(user.id, "totalMissionHours", hours);
        logger.debug(`Processed mission completed event objectives for user ${userId}, mission ${missionId || "unknown"}`);
    }
    catch (error) {
        LogErrorStack({ message: "Error handling mission completed event objectives", error });
    }
};
export const handleShrineDonationMadeEvent = async (payload) => {
    try {
        const { userId, amount } = payload;
        const user = await UserRepository.getUserById(userId);
        if (!user) {
            logger.error(`User not found for shrine donation event: ${userId}`);
            return;
        }
        await QuestService.handleShrineDonation(userId, amount);
        logger.debug(`Processed shrine donation event objectives for user ${userId}, amount ${amount}`);
    }
    catch (error) {
        LogErrorStack({ message: "Error handling shrine donation event objectives", error });
    }
};
export const handleSuggestionVotedEvent = async (payload) => {
    try {
        const { userId, suggestionId } = payload;
        await QuestService.handleSuggestionVote(userId, suggestionId);
        await AchievementService.UpdateUserAchievement(userId, "suggestionsVoted");
        logger.debug(`Processed suggestion voted event objectives for user ${userId}, suggestion ${suggestionId}`);
    }
    catch (error) {
        LogErrorStack({ message: "Error handling suggestion voted event objectives", error });
    }
};
export const handleEncounterCompletedEvent = async (payload) => {
    try {
        const { userId, encounterId, location } = payload;
        await QuestService.handleCharacterEncounter(userId, encounterId, location);
        await AchievementService.UpdateUserAchievement(userId, "encountersCompleted");
        logger.debug(`Processed encounter completed event objectives for user ${userId}, encounter ${encounterId}, location ${location}`);
    }
    catch (error) {
        LogErrorStack({ message: "Error handling encounter completed event objectives", error });
    }
};
export const handleStatsTrainedEvent = async (payload) => {
    try {
        const { userId, amount } = payload;
        await QuestService.handleStatsTraining(userId, amount);
        logger.debug(`Processed stats trained event objectives for user ${userId}, amount ${amount}`);
    }
    catch (error) {
        LogErrorStack({ message: "Error handling stats trained event objectives", error });
    }
};
export const handleGamblingPerformedEvent = async (payload) => {
    try {
        const { userId, gameType, amount } = payload;
        await QuestService.handleGambling(userId, gameType, amount);
        logger.debug(`Processed gambling performed event objectives for user ${userId}, gameType ${gameType}, amount ${amount}`);
    }
    catch (error) {
        LogErrorStack({ message: "Error handling gambling performed event objectives", error });
    }
};
