import fs from "node:fs";
import path from "node:path";
import { fileURLToPath } from "node:url";
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ENV = (process.env.NODE_ENV || "development");
const CERT_PATHS = {
    development: {
        key: path.resolve(__dirname, "../../certs/selfsigned.key"),
        cert: path.resolve(__dirname, "../../certs/selfsigned.crt"),
    },
    staging: {
        key: path.resolve(__dirname, "../../certs/chikara-selfsigned.key"),
        cert: path.resolve(__dirname, "../../certs/chikara-selfsigned.crt"),
    },
    production: {
        key: path.resolve(__dirname, "../../certs/battleacademy.key"),
        cert: path.resolve(__dirname, "../../certs/battleacademy.crt"),
    },
};
function isValidEnv(env) {
    return env === "development" || env === "staging" || env === "production";
}
const currentEnv = isValidEnv(ENV) ? ENV : "development";
export const credentials = {
    key: fs.readFileSync(CERT_PATHS[currentEnv].key),
    cert: fs.readFileSync(CERT_PATHS[currentEnv].cert),
};
