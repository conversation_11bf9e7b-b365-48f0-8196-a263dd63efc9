import { SkillType } from "@prisma/client";
export const CRAFTING_SKILLS = [
    {
        skill: SkillType.fabrication,
        name: "Fabrication",
        description: "Creating/repairing basic weapons & heavy gear from raw/salvaged materials.",
        primaryStat: "strength",
        itemTypes: ["weapon", "shield", "heavy armor", "melee upgrades"],
    },
    {
        skill: SkillType.outfitting,
        name: "Outfitting",
        description: "Crafting/repairing protective wear, utility gear, bags from lighter materials.",
        primaryStat: "dexterity",
        itemTypes: ["light armor", "head", "chest", "hands", "legs", "feet", "utility gear"],
    },
    {
        skill: SkillType.chemistry,
        name: "Chemistry",
        description: "Mixing consumables, buffs, debuffs, poisons from botanical/chemical sources.",
        primaryStat: "intelligence",
        itemTypes: ["consumable", "potion", "poison", "medicine", "drug"],
    },
    {
        skill: SkillType.electronics,
        name: "Electronics",
        description: "Crafting/installing advanced mods, gadgets, complex repairs involving circuitry/mechanics.",
        primaryStat: "intelligence",
        itemTypes: ["ranged", "gadget", "tech upgrade", "electronic device"],
    },
];
export function getCraftingSkillInfo(skill) {
    return CRAFTING_SKILLS.find((s) => s.skill === skill);
}
export function getAllCraftingSkills() {
    return CRAFTING_SKILLS;
}
export function getCraftingSkillsByStat(stat) {
    return CRAFTING_SKILLS.filter((skill) => skill.primaryStat === stat);
}
export function calculateCraftedItemQuality(skillLevel, statValue) {
    const baseQuality = 1.0;
    const skillBonus = skillLevel * 0.01;
    const statBonus = statValue * 0.005;
    return baseQuality + skillBonus + statBonus;
}
