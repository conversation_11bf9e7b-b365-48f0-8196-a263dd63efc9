import { BattleType } from "@/features/battle/types/battle";
import { AppRouterClient } from "@/lib/orpc";

// types/user.ts

// Gang type definition
export interface Gang {
    id: number;
    name: string;
    about: string;
    avatar: string | null;
    treasury_balance: number;
    hideout_level: number;
    materialsResource: number;
    essenceResource: number;
    dailyEssenceGained: number;
    toolsResource: number;
    techResource: number;
    weeklyRespect: number;
    totalRespect: number;
    gangMOTD: string | null;
    createdAt: string;
    updatedAt: string;
    ownerId: number;
}

export type UserType = "admin" | "student" | "prefect";

export type UserResponseData = Awaited<ReturnType<AppRouterClient["user"]["getCurrentUserInfo"]>>;

// User type definition
export interface User {
    // Time-based states
    battleValidUntil: string | null;
    chatBannedUntil: string | null;
    banExpires: string | null;
    jailedUntil: string | null;
    hospitalisedUntil: string | null;
    courseEnds: string | null;
    profileDetailBanUntil: string | null;
    lastEnergyTick: string | null;
    nextAPTick: string;
    nextHPTick: string;

    // Basic user info
    id: number;
    username: string;
    about: string;
    email: string;
    userType: UserType;
    avatar: string;
    profileBanner: string | null;
    createdAt: string;
    statusMessage: string | null;
    statusMessageUpdatedAt: string | null;

    // Resources and currency
    cash: number;
    bank_balance: number;
    energy: number;
    actionPoints: number;
    maxActionPoints: number;
    gangCreds: number;

    // Character stats
    level: number;
    xp: number;
    xpForNextLevel: number;
    health: number;
    currentHealth: number;
    currentStamina: number;
    talentPoints: number;

    // Job and activities
    jobLevel: number;
    jobPayoutHour: number;
    blockNextJobPayout: boolean;
    roguelikeLevel: number;

    // Status reasons
    jailReason: string | null;
    hospitalisedHealingType: string | null;
    hospitalisedReason: string | null;

    // Progress
    courseIndex: number | null;
    completedCourses: string | null;
    class: string;
    classPoints: number;

    // Missions and achievements
    currentMission: string | null;
    missionEnds: string | null;
    weeklyBuyLimitRemaining: number;
    dailyQuestsRewardClaimed: string | null;
    defeatedNpcs: string | null;

    // Settings and flags
    pushNotificationsEnabled: boolean;
    lastNewsIDRead: number;
    showLastOnline: boolean;

    // Relations and equipment
    gangId: number;
    jobId: number;
    referrerId: number | null;

    // Nested objects
    gang: Gang[];

    // Game config version
    gameConfigVersion: number;

    // Battle related
    battleAggressor: boolean;
    activeBattleType: BattleType;
    currentMapLocation: "shibuya" | "shinjuku" | "bunkyo" | "chiyoda" | "minato" | null;

    // Travel related
    travelingTo?: "shibuya" | "shinjuku" | "bunkyo" | "chiyoda" | "minato" | null;
    travelStartTime?: string | null;
    travelEndTime?: string | null;
    travelMethod?: "walk" | "bus" | null;

    // New fields
    focus: number;
    dailyFatigueUsed: number;
    lastFatigueReset: Date | null;
    fatigue?: number;
}

export type UserStat = "strength" | "intelligence" | "dexterity" | "defence" | "endurance" | "vitality";

export interface TrainStatRequest {
    stat: UserStat;
    focusAmount: number;
}

export interface TrainStatResponse {
    statProgress: {
        stat: string;
        expGained: number;
        leveledUp: boolean;
        levelsGained: number;
        currentLevel: number;
        currentExp: number;
        expToNextLevel: number;
        previousLevel: number;
    };
    focusRemaining: number;
    dailyFatigueRemaining: number;
}
