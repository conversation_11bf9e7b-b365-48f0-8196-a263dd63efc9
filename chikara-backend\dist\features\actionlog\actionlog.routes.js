import { GetAllActionsAffectingUser, GetMostRecentActions, GetUserActions, } from "./actionlog.controller.js";
import actionLogSchema from "./actionlog.validation.js";
import { IsAdmin } from "../../middleware/authMiddleware.js";
import validate from "../../middleware/validate.js";
import routeHandler from "../../utils/routeHandler.js";
import express from "express";
const router = express.Router();
router.get("/actionlog", IsAdmin, validate(actionLogSchema.getMostRecentActions), routeHandler(async (req) => {
    const { search, limit } = req.query;
    return await GetMostRecentActions(limit ? Number.parseInt(limit) : 100, search);
}));
router.post("/actionlog/user-actions", IsAdmin, validate(actionLogSchema.getUserActions), routeHandler(async (req) => {
    const { userId, search, limit } = req.body;
    return await GetUserActions(userId, limit ? Number.parseInt(limit) : 100, search);
}));
router.post("/actionlog/all-actions/affecting-user", IsAdmin, validate(actionLogSchema.getAllActionsAffectingUser), routeHandler(async (req) => {
    const { userId, search, limit } = req.body;
    return await GetAllActionsAffectingUser(userId, limit ? Number.parseInt(limit) : 100, search);
}));
export default router;
