import * as UserRepository from "../../repositories/user.repository.js";
import { BATTLE_STATE } from "./helpers/battle.constants.js";
import * as BattleRepository from "../../repositories/battle.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import { LogErrorStack, logger } from "../../utils/log.js";
import { updateBattleState, validateBattleState, sanitizeBattleStateForFrontend } from "./battle.state.js";
import { battleConfig, levelGatesConfig } from "../../config/gameConfig.js";
import { getToday } from "../../utils/dateHelpers.js";
import { initiateBattle } from "./logic/battle.initiation.js";
import { processBattleRound } from "./logic/battle.round.js";
import { rooftopNpcs } from "../../data/uniqueNpcs.js";
const { ROOFTOP_BATTLES_LEVEL_GATE } = levelGatesConfig.public;
const { DAILY_USER_ATTACK_LIMIT } = battleConfig.public;
const antiBullyCheck = async (attackerId, defenderId) => {
    const today = getToday();
    const count = await BattleRepository.countBattleWinsAgainstTarget(attackerId, defenderId, today);
    if (count >= DAILY_USER_ATTACK_LIMIT) {
        return false;
    }
    return true;
};
export const initiatePVPBattle = async (attackerId, defenderId) => {
    const currentUser = await UserRepository.getUserById(attackerId);
    const target = await UserRepository.getUserById(defenderId);
    if (!currentUser || !target) {
        return { error: "User not found" };
    }
    if (!(await antiBullyCheck(attackerId, defenderId)) && currentUser.userType !== "admin") {
        return { error: "Attacked this user too many times today" };
    }
    return await initiateBattle(currentUser, target, "pvp");
};
export const processAttack = async (userId, action) => {
    logger.profile("processAttack");
    logger.debug(`processAttack called for userId: ${userId}, action: ${action}`);
    const userIdStr = userId.toString();
    const { battleState, playerState, targetState, error } = await validateBattleState(userIdStr);
    if (error) {
        logger.debug(`Battle state validation failed: ${error}`);
        return { error };
    }
    if (!battleState) {
        LogErrorStack({
            message: `Battle state not found for userId: ${userId}`,
            error: new Error(`Battle state not found for userId: ${userId}`),
        });
        return { error: "Battle state not found" };
    }
    if (!targetState) {
        LogErrorStack({
            message: `Target state not found for userId: ${userId}`,
            error: new Error(`Target state not found for userId: ${userId}`),
        });
        return { error: "Target state not found" };
    }
    if (battleState.state === BATTLE_STATE.FINISHED || targetState.currentHealth <= 0) {
        logger.debug(`Battle is already over for userId: ${userId}`);
        return { error: "Battle is already over" };
    }
    logger.debug(`Processing battle round for userId: ${userId}`);
    const result = await processBattleRound(battleState, playerState, targetState, action);
    if (!result || "error" in result) {
        LogErrorStack({
            message: `Result not found for userId: ${userId}`,
            error: new Error(`Result not found for userId: ${userId}`),
        });
        return { error: "Result not found" };
    }
    logger.debug(`Updating battle state in Redis for userId: ${userId}`);
    await updateBattleState(battleState.id, result.battleState);
    const currentRoundLogs = result.battleState.combatLog.filter((log) => log.round === result.battleState.currentRound - 1);
    logger.debug(`Sanitizing battle state for userId: ${userId}`);
    const sanitizedBattleState = sanitizeBattleStateForFrontend(result.battleState);
    sanitizedBattleState.combatLog = currentRoundLogs;
    logger.profile("processAttack");
    logger.debug(`processAttack completed for userId: ${userId}`);
    return {
        data: {
            ...result,
            battleState: sanitizedBattleState,
        },
    };
};
export const getBattleStatus = async (userId) => {
    const userIdStr = userId.toString();
    const { battleState, playerState, targetState, error } = await validateBattleState(userIdStr);
    if (error) {
        return { error };
    }
    if (!playerState || !targetState) {
        LogErrorStack({
            message: "Invalid battle state - missing player data",
            error: new Error("Invalid battle state - missing player data"),
        });
        return { error: "Invalid battle state" };
    }
    const sanitizedBattleState = sanitizeBattleStateForFrontend(battleState);
    return { data: sanitizedBattleState };
};
export const listRooftopBattles = async (userId) => {
    try {
        const currentUser = await UserRepository.getUserById(userId);
        const defeatedNpcs = currentUser?.defeatedNpcs || [];
        const npcsWithStatus = await Promise.all(rooftopNpcs.map(async (npc) => {
            const processedNpc = {
                ...npc,
                defeated: defeatedNpcs.includes(npc.id),
            };
            if (npc.itemRewardId) {
                const item = await ItemRepository.findItemById(npc.itemRewardId);
                return item ? { ...processedNpc, item } : processedNpc;
            }
            return processedNpc;
        }));
        return {
            data: npcsWithStatus,
        };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to list rooftop battles" };
    }
};
export const initiateRooftopBattle = async (attackerId, npcID) => {
    const npc = rooftopNpcs[npcID - 1];
    if (!npc) {
        return { error: "NPC not found" };
    }
    const currentUser = await UserRepository.getUserById(attackerId);
    if (!currentUser) {
        return { error: "User not found" };
    }
    if (currentUser?.level && currentUser.level < ROOFTOP_BATTLES_LEVEL_GATE) {
        return { error: "Level too low!" };
    }
    const defeatedNpcs = currentUser?.defeatedNpcs || [];
    if (defeatedNpcs.includes(npcID)) {
        return { error: "You've already defeated this NPC." };
    }
    const target = { ...npc };
    return await initiateBattle(currentUser, target, "pve-rooftop");
};
