import * as CraftingRepository from "../../repositories/crafting.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import { db } from "../../lib/db.js";
import { LogErrorStack } from "../../utils/log.js";
import { CraftingSkills } from "@prisma/client";

export interface CreateRecipeData {
    cost: number;
    craftTime: number;
    isUnlockable?: boolean;
    requiredSkillType?: string | null;
    requiredSkillLevel?: number;
    inputItems?: { id: number; amount: number }[];
    outputItems?: { id: number; amount?: number }[];
}

export interface EditRecipeData {
    id: number;
    cost?: number;
    craftTime?: number;
    isUnlockable?: boolean;
    requiredSkillType?: string | null;
    requiredSkillLevel?: number;
    inputItems?: { id: number; amount: number }[];
    outputItems?: { id: number; amount?: number }[];
}

export const createRecipe = async (recipeData: CreateRecipeData) => {
    try {
        const result = await db.$transaction(async (tx) => {
            // Create the base recipe
            const recipe = await CraftingRepository.createRecipeWithTransaction(
                {
                    cost: recipeData.cost,
                    craftTime: recipeData.craftTime,
                    isUnlockable: recipeData.isUnlockable || false,
                    requiredSkillType: (recipeData.requiredSkillType as CraftingSkills) || null,
                    requiredSkillLevel: recipeData.requiredSkillLevel || 0,
                },
                tx
            );

            // Add input items
            if (recipeData.inputItems && Array.isArray(recipeData.inputItems)) {
                for (const inputItem of recipeData.inputItems) {
                    const item = await ItemRepository.findItemById(inputItem.id);
                    if (!item) {
                        throw new Error(`Input item with ID ${inputItem.id} not found`);
                    }

                    await CraftingRepository.addItemToRecipe(
                        recipe,
                        item.id,
                        {
                            count: inputItem.amount ?? 1,
                            itemType: "input",
                            crafting_recipe: { connect: { id: recipe.id } },
                            item: { connect: { id: item.id } },
                        },
                        tx
                    );
                }
            }

            // Add output items
            if (recipeData.outputItems && Array.isArray(recipeData.outputItems)) {
                for (const outputItem of recipeData.outputItems) {
                    const item = await ItemRepository.findItemById(outputItem.id);
                    if (!item) {
                        throw new Error(`Output item with ID ${outputItem.id} not found`);
                    }

                    await CraftingRepository.addItemToRecipe(
                        recipe,
                        item.id,
                        {
                            count: outputItem.amount || 1,
                            itemType: "output",
                            crafting_recipe: { connect: { id: recipe.id } },
                            item: { connect: { id: item.id } },
                        },
                        tx
                    );
                }
            }

            return recipe;
        });

        return { data: result };
    } catch (error) {
        LogErrorStack({ message: "Error when adding items to crafting recipe:", error });
        return { error: "Invalid items", statusCode: 400 };
    }
};

export const editRecipe = async (recipeData: EditRecipeData) => {
    try {
        if (!recipeData.id) {
            return { error: "Recipe ID is required", statusCode: 400 };
        }

        const existingRecipe = await CraftingRepository.findRecipeById(recipeData.id);
        if (!existingRecipe) {
            return { error: "Recipe not found", statusCode: 404 };
        }

        const result = await db.$transaction(async (tx) => {
            // Update recipe base data
            await CraftingRepository.updateRecipe(
                existingRecipe,
                {
                    cost: recipeData.cost ?? existingRecipe.cost,
                    craftTime: recipeData.craftTime ?? existingRecipe.craftTime,
                    isUnlockable: recipeData.isUnlockable ?? existingRecipe.isUnlockable,
                    requiredSkillType:
                        (recipeData.requiredSkillType as CraftingSkills) ?? existingRecipe.requiredSkillType,
                    requiredSkillLevel: recipeData.requiredSkillLevel ?? existingRecipe.requiredSkillLevel,
                },
                tx
            );

            // If items have changed, delete all current items and re-add them
            if (recipeData.inputItems || recipeData.outputItems) {
                // Delete all existing recipe items
                await CraftingRepository.deleteRecipeItems(existingRecipe.id, tx);

                // Add input items
                if (recipeData.inputItems && Array.isArray(recipeData.inputItems)) {
                    for (const inputItem of recipeData.inputItems) {
                        const item = await ItemRepository.findItemById(inputItem.id);

                        if (!item) {
                            throw new Error(`Input item with ID ${inputItem.id} not found`);
                        }

                        await CraftingRepository.addItemToRecipe(
                            existingRecipe,
                            item.id,
                            {
                                count: inputItem.amount || 1,
                                itemType: "input",
                                crafting_recipe: { connect: { id: existingRecipe.id } },
                                item: { connect: { id: item.id } },
                            },
                            tx
                        );
                    }
                }

                // Add output items
                if (recipeData.outputItems && Array.isArray(recipeData.outputItems)) {
                    for (const outputItem of recipeData.outputItems) {
                        const item = await ItemRepository.findItemById(outputItem.id);
                        if (!item) {
                            throw new Error(`Output item with ID ${outputItem.id} not found`);
                        }

                        await CraftingRepository.addItemToRecipe(
                            existingRecipe,
                            item.id,
                            {
                                count: outputItem.amount || 1,
                                itemType: "output",
                                crafting_recipe: { connect: { id: existingRecipe.id } },
                                item: { connect: { id: item.id } },
                            },
                            tx
                        );
                    }
                }
            }

            return existingRecipe;
        });

        return { data: result };
    } catch (error) {
        LogErrorStack({ message: "Error when editing crafting recipe:", error });
        return { error: "Invalid items", statusCode: 400 };
    }
};

export const deleteRecipe = async (recipeId: number) => {
    await CraftingRepository.deleteRecipeById(recipeId);
    return { data: "Recipe deleted" };
};
