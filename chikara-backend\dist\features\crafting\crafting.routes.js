import * as CraftingAdmin from "./crafting.admin.js";
import * as CraftingController from "./crafting.controller.js";
import craftingSchema from "./crafting.validation.js";
import { isLoggedInAuth, canMakeStateChangesAuth, adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
export const craftingRouter = {
    getCraftingQueue: isLoggedInAuth.handler(async ({ context }) => {
        const response = await CraftingController.getCraftingQueue(context.user.id);
        return handleResponse(response);
    }),
    getRecipes: isLoggedInAuth.handler(async ({ context }) => {
        const response = await CraftingController.recipeList(context.user.id);
        return handleResponse(response);
    }),
    craftItem: canMakeStateChangesAuth
        .input(craftingSchema.craftItem)
        .handler(async ({ input, context }) => {
        const response = await CraftingController.craftItem(context.user, input);
        return handleResponse(response);
    }),
    completeCraft: canMakeStateChangesAuth
        .input(craftingSchema.completeCraft)
        .handler(async ({ input, context }) => {
        const response = await CraftingController.completeCraft(context.user.id, input.id);
        return handleResponse(response);
    }),
    cancelCraft: canMakeStateChangesAuth
        .input(craftingSchema.cancelCraft)
        .handler(async ({ input, context }) => {
        const response = await CraftingController.cancelCraft(context.user.id, input.id);
        return handleResponse(response);
    }),
    getAdminRecipeList: adminAuth.handler(async ({ context }) => {
        const response = await CraftingController.recipeList(context.user.id, true);
        return handleResponse(response);
    }),
    createRecipe: adminAuth
        .input(craftingSchema.createRecipe)
        .handler(async ({ input }) => {
        const response = await CraftingAdmin.createRecipe(input);
        return handleResponse(response);
    }),
    editRecipe: adminAuth
        .input(craftingSchema.editRecipe)
        .handler(async ({ input }) => {
        const response = await CraftingAdmin.editRecipe(input);
        return handleResponse(response);
    }),
    deleteRecipe: adminAuth
        .input(craftingSchema.deleteRecipe)
        .handler(async ({ input }) => {
        const response = await CraftingAdmin.deleteRecipe(input.id);
        return handleResponse(response);
    }),
};
