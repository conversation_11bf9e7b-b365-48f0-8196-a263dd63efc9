import Button from "@/components/Buttons/Button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import YourGang from "@/features/gang/components/YourGang";
import { useGetFriendList, useGetRivalsList } from "@/features/social/api";
import useGetFriendRequests from "@/features/social/api/useGetFriendRequests";
import FriendsList from "@/features/social/components/FriendsList";
import RivalsList from "@/features/social/components/RivalsList";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { Skull, Users } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";

export default function Social() {
    const { data: currentUser } = useFetchCurrentUser();
    const [activeTab, setActiveTab] = useState("gang");

    const { data: friendsList, isLoading: isLoadingFriends } = useGetFriendList();
    const { data: rivalsList, isLoading: isLoadingRivals } = useGetRivalsList();
    const { data: friendRequests, isLoading: isLoadingRequests } = useGetFriendRequests();

    return (
        <div className="w-full md:mx-auto md:max-w-4xl">
            <div className="relative mb-2 flex h-12 justify-between border-black border-y-2 bg-[#343549] p-1 px-4 pb-1.5 md:rounded-t-lg -m-2">
                <h1 className="my-auto font-body font-semibold text-lg text-stroke-s-sm text-white">Social</h1>

                <div className="absolute bottom-0 left-0 h-1 w-full bg-[#272839]"></div>
            </div>

            <Tabs defaultValue="gang" value={activeTab} className="mt-2" onValueChange={setActiveTab}>
                <TabsList className="w-full h-12 bg-indigo-900/20 p-1">
                    <TabsTrigger
                        value="gang"
                        className={cn(
                            "flex-1 h-full data-[state=active]:bg-indigo-700 data-[state=active]:text-white",
                            "data-[state=inactive]:text-blue-100 data-[state=inactive]:hover:bg-white/[0.12] data-[state=inactive]:hover:text-white"
                        )}
                    >
                        Your Gang
                    </TabsTrigger>
                    <TabsTrigger
                        value="friends"
                        className={cn(
                            "flex-1 h-full data-[state=active]:bg-indigo-700 data-[state=active]:text-white",
                            "data-[state=inactive]:text-blue-100 data-[state=inactive]:hover:bg-white/[0.12] data-[state=inactive]:hover:text-white",
                            "relative"
                        )}
                    >
                        <div className="flex items-center justify-center gap-2">
                            <Users className="size-4" />
                            <span>Friends</span>
                            {friendRequests && friendRequests.length > 0 && (
                                <span className="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full h-4 min-w-4 flex items-center justify-center">
                                    {friendRequests.length}
                                </span>
                            )}
                        </div>
                    </TabsTrigger>
                    <TabsTrigger
                        value="rivals"
                        className={cn(
                            "flex-1 h-full data-[state=active]:bg-indigo-700 data-[state=active]:text-white",
                            "data-[state=inactive]:text-blue-100 data-[state=inactive]:hover:bg-white/[0.12] data-[state=inactive]:hover:text-white"
                        )}
                    >
                        <div className="flex items-center justify-center gap-2">
                            <Skull className="size-4" />
                            <span>Rivals</span>
                        </div>
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="gang" className="mt-2">
                    {currentUser?.gangId ? (
                        <YourGang currentUser={currentUser} />
                    ) : (
                        <div className="p-4 text-center bg-gray-800 rounded-lg border border-gray-700">
                            <h3 className="text-xl font-bold text-gray-200 mb-2">You&apos;re not in a gang yet!</h3>
                            <p className="text-gray-400 mb-4">
                                Join or create a gang to unlock gang features and benefits.
                            </p>
                            <Link to="/ganglist">
                                <Button onClick={() => {}}>Find a Gang</Button>
                            </Link>
                        </div>
                    )}
                </TabsContent>

                <TabsContent value="friends" className="mt-2">
                    <FriendsList
                        friendsList={friendsList}
                        friendRequests={friendRequests}
                        isLoading={isLoadingFriends}
                        isLoadingRequests={isLoadingRequests}
                        statusMessage={currentUser?.statusMessage}
                        showOnlineStatus={currentUser?.showLastOnline ?? true}
                        statusMessageUpdatedAt={
                            currentUser?.statusMessageUpdatedAt ? new Date(currentUser.statusMessageUpdatedAt) : null
                        }
                    />
                </TabsContent>

                <TabsContent value="rivals" className="mt-2">
                    <RivalsList rivalsList={rivalsList} isLoading={isLoadingRivals} />
                </TabsContent>
            </Tabs>
        </div>
    );
}
