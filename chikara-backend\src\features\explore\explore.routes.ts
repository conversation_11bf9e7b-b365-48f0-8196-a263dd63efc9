import { canMakeStateChangesAuth, isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as ExploreController from "./explore.controller.js";
import exploreSchema from "./explore.validation.js";

export const exploreRouter = {
    getMapByLocation: isLoggedInAuth.handler(async ({ context }) => {
        const { id, currentMapLocation } = context.user;
        const response = await ExploreController.getExploreMapByLocation(id, currentMapLocation);
        return handleResponse(response);
    }),

    interactWithNode: canMakeStateChangesAuth
        .input(exploreSchema.interactWithNode)
        .handler(async ({ input, context }) => {
            const response = await ExploreController.interactWithNode(context.user.id, input.nodeId, input.isStatic);
            return handleResponse(response);
        }),

    completeNode: canMakeStateChangesAuth.input(exploreSchema.completeNode).handler(async ({ input, context }) => {
        const response = await ExploreController.completeNode(context.user.id, input.nodeId);
        return handleResponse(response);
    }),

    changeMapLocation: canMakeStateChangesAuth.input(exploreSchema.changeMap).handler(async ({ input, context }) => {
        const response = await ExploreController.changeMapLocation(context.user.id, input.location, input.method);
        return handleResponse(response);
    }),

    getTravelStatus: isLoggedInAuth.input(exploreSchema.getTravelStatus).handler(async ({ context }) => {
        const response = await ExploreController.getTravelStatus(context.user.id);
        return handleResponse(response);
    }),

    makeScavengeChoice: canMakeStateChangesAuth
        .input(exploreSchema.scavengeChoice)
        .handler(async ({ input, context }) => {
            const response = await ExploreController.makeScavengeChoice(
                context.user.id,
                input.nodeId,
                input.choiceIndex
            );
            return handleResponse(response);
        }),

    processMiningOperation: canMakeStateChangesAuth
        .input(exploreSchema.processMining)
        .handler(async ({ input, context }) => {
            const response = await ExploreController.processMiningOperation(context.user.id, input.nodeId);
            return handleResponse(response);
        }),

    processForagingOperation: canMakeStateChangesAuth
        .input(exploreSchema.processForaging)
        .handler(async ({ input, context }) => {
            const response = await ExploreController.processForagingOperation(context.user.id, input.nodeId);
            return handleResponse(response);
        }),
};

export default exploreRouter;
