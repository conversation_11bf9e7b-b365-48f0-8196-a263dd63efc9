import { getRedisItem, redisClient } from "../../../../config/redisClient.js";
import { handleBattleTimeout } from "../../../../features/battle/battle.state.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";
export async function checkTimeoutBattles() {
    try {
        const battleKeys = await redisClient.keys(`battle:*`);
        if (battleKeys.length === 0) {
            return;
        }
        const battleStates = await Promise.all(battleKeys.map(async (key) => {
            const battleData = (await getRedisItem(key));
            return battleData ?? null;
        }));
        const now = Date.now();
        const timedOutBattles = battleStates.filter((battle) => battle !== null && battle.state === "in_progress" && now > battle.validUntil);
        for (const battle of timedOutBattles) {
            await handleBattleTimeout(battle);
            logger.debug(`Processed timeout for battle ${battle.id}`);
        }
        if (timedOutBattles.length > 0) {
            logger.debug(`Processed ${timedOutBattles.length} timed out battles`);
        }
    }
    catch (error) {
        LogErrorStack({
            error,
            message: "Error checking for timed out battles",
        });
    }
}
export default checkTimeoutBattles;
