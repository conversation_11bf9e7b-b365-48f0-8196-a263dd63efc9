import { HEALTH_THRESHOLD, calculateMeleeDamage } from "./battle.ai.js";
import { STATUS_ABILITIES } from "../logic/battle.abilities.js";
export const evaluateSleepAbility = (aiPlayer, enemy, healthPercentage, battleProgress, isActive) => {
    let score = 2.0;
    if (isActive) {
        return 0.1;
    }
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 1.8;
    }
    else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 1.4;
    }
    const incomingDamage = calculateMeleeDamage(enemy, aiPlayer);
    if (incomingDamage > aiPlayer.currentHealth * 0.3) {
        score *= 1.7;
    }
    if (battleProgress > 0.7) {
        score *= 0.8;
    }
    if (aiPlayer.currentStamina < 100) {
        score *= 0.9;
    }
    if (aiPlayer.abilities &&
        aiPlayer.abilities.filter((ab) => ab.name.includes("bash") || ab.name.includes("headbutt")).length > 1) {
        score *= 0.8;
    }
    if (aiPlayer.attributes.intelligence > 60) {
        score *= 1.3;
    }
    return score;
};
export const evaluateStunAbility = (aiPlayer, enemy, healthPercentage, battleProgress, isActive) => {
    let score = 2.2;
    if (isActive) {
        return 0.1;
    }
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 2.0;
    }
    else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 1.5;
    }
    const incomingDamage = calculateMeleeDamage(enemy, aiPlayer);
    if (incomingDamage > aiPlayer.currentHealth * 0.25) {
        score *= 1.8;
    }
    if (battleProgress < 0.3) {
        score *= 1.3;
    }
    else if (battleProgress > 0.7) {
        score *= 0.9;
    }
    if (aiPlayer.attributes.strength > 60) {
        score *= 1.4;
    }
    if (enemy.currentHealth <= calculateMeleeDamage(aiPlayer, enemy) * 1.5) {
        score *= 0.5;
    }
    return score;
};
export const evaluateCrippleAbility = (aiPlayer, enemy, healthPercentage, battleProgress, isActive) => {
    let score = 1.7;
    if (isActive) {
        return 0.2;
    }
    if (battleProgress < 0.4) {
        score *= 1.4;
    }
    else if (battleProgress > 0.7) {
        score *= 0.8;
    }
    if (enemy.attributes.defence > 60) {
        score *= 1.8;
    }
    else if (enemy.attributes.defence > 40) {
        score *= 1.4;
    }
    if (aiPlayer.attributes.strength > 60) {
        score *= 1.3;
    }
    if (enemy.statusEffects && enemy.statusEffects.high_guard?.turns && enemy.statusEffects.high_guard.turns > 0) {
        score *= 1.6;
    }
    if (aiPlayer.abilities &&
        aiPlayer.abilities.some((ab) => ab.name.includes("bash") || ab.name.includes("headbutt"))) {
        score *= 1.5;
    }
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 0.7;
    }
    if (enemy.currentHealth <= calculateMeleeDamage(aiPlayer, enemy) * 1.5) {
        score *= 0.5;
    }
    return score;
};
export const evaluateShockwaveAbility = (aiPlayer, enemy, healthPercentage, battleProgress, isActive) => {
    let score = 1.9;
    if (isActive) {
        return 0.1;
    }
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 1.8;
    }
    else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 1.4;
    }
    const incomingDamage = calculateMeleeDamage(enemy, aiPlayer);
    if (incomingDamage > aiPlayer.currentHealth * 0.25) {
        score *= 1.7;
    }
    if (battleProgress < 0.3) {
        score *= 1.2;
    }
    else if (battleProgress > 0.7) {
        score *= 0.9;
    }
    if (aiPlayer.attributes.defence > 60) {
        score *= 1.5;
    }
    if (enemy.currentHealth <= calculateMeleeDamage(aiPlayer, enemy) * 1.5) {
        score *= 0.5;
    }
    return score;
};
export const evaluateDisarmAbility = (aiPlayer, enemy, healthPercentage, battleProgress, isActive) => {
    let score = 2.1;
    if (isActive) {
        return 0.1;
    }
    const enemyHasWeapon = enemy.equipment && (enemy.equipment.weapon || enemy.equipment.ranged);
    if (!enemyHasWeapon) {
        return 0.1;
    }
    if ((enemy.equipment?.weapon?.damage && enemy.equipment?.weapon?.damage > 20) ||
        (enemy.equipment?.ranged?.damage && enemy.equipment?.ranged?.damage > 15)) {
        score *= 1.8;
    }
    if (battleProgress < 0.4) {
        score *= 1.4;
    }
    else if (battleProgress > 0.7) {
        score *= 0.8;
    }
    if (enemy.abilities && enemy.abilities.length < 3) {
        score *= 1.6;
    }
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 1.7;
    }
    else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 1.3;
    }
    return score;
};
export const evaluateExhaustAbility = (aiPlayer, enemy, healthPercentage, battleProgress, isActive) => {
    let score = 1.8;
    if (isActive) {
        return 0.2;
    }
    if (enemy.attributes.strength > 60) {
        score *= 1.7;
    }
    if (battleProgress < 0.3) {
        score *= 1.5;
    }
    else if (battleProgress > 0.7) {
        score *= 0.7;
    }
    if (enemy.currentHealth < enemy.maxHealth * 0.4) {
        score *= 0.8;
    }
    if (aiPlayer.attributes.dexterity > 60) {
        score *= 1.3;
    }
    if (enemy.attributes.strength > enemy.attributes.dexterity * 1.3) {
        score *= 1.6;
    }
    if (healthPercentage < HEALTH_THRESHOLD.CRITICAL) {
        score *= 1.4;
    }
    else if (healthPercentage < HEALTH_THRESHOLD.LOW) {
        score *= 1.2;
    }
    return score;
};
export const evaluateDebuffAbility = (ability, aiPlayer, enemy) => {
    let score = 1.0;
    const healthPercentage = aiPlayer.currentHealth / aiPlayer.maxHealth;
    const enemyHealthPercentage = enemy.currentHealth / enemy.maxHealth;
    const battleProgress = 1 - enemyHealthPercentage;
    const isDebuffActive = (debuffName) => {
        return (enemy.statusEffects && enemy.statusEffects[debuffName]?.turns && enemy.statusEffects[debuffName].turns > 0);
    };
    switch (ability.name) {
        case "cripple": {
            const isActive = !!isDebuffActive("cripple");
            score = evaluateCrippleAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
        case "stun": {
            const isActive = !!isDebuffActive("stun");
            score = evaluateStunAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
        case "shockwave": {
            const isActive = !!isDebuffActive("shockwave");
            score = evaluateShockwaveAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
        case "sleep": {
            const isActive = !!isDebuffActive("sleep");
            score = evaluateSleepAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
        case "disarm": {
            const isActive = !!isDebuffActive("disarm");
            score = evaluateDisarmAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
        case "exhaust": {
            const isActive = !!isDebuffActive("exhaust");
            score = evaluateExhaustAbility(aiPlayer, enemy, healthPercentage, battleProgress, isActive);
            break;
        }
    }
    if (aiPlayer.currentStamina < 40) {
        score *= 0.8;
    }
    const battleLengthEstimate = enemy.currentHealth / calculateMeleeDamage(aiPlayer, enemy);
    const abilityTurns = STATUS_ABILITIES.find((s) => s.name === ability.name)?.turns;
    if (abilityTurns && battleLengthEstimate < abilityTurns - 1) {
        score *= 0.7;
    }
    if (enemy.currentHealth < calculateMeleeDamage(aiPlayer, enemy) * 1.5) {
        score *= 0.5;
    }
    return score;
};
