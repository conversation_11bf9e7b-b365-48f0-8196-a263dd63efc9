import { logger, LogErrorStack } from "../../../utils/log.js";
import * as AchievementService from "../../achievement.service.js";
import * as QuestService from "../../quest.service.js";
import * as UserRepository from "../../../repositories/user.repository.js";
export const handleNPCBattleWonEvent = async (payload) => {
    try {
        const { userId, creatureId, location, turns, damageTaken, percentDmgTaken, isBoss } = payload;
        const user = await UserRepository.getUserById(userId);
        if (!user) {
            logger.error(`User not found for NPC battle won event: ${userId}`);
            return;
        }
        await QuestService.handleDefeatNPC(userId, creatureId, location);
        if (turns) {
            await QuestService.handleDefeatNPCInTurns(userId, turns);
        }
        if (percentDmgTaken) {
            await QuestService.handleDefeatNPCWithLowDamage(userId, percentDmgTaken);
        }
        if (isBoss) {
            await QuestService.handleDefeatBoss(userId);
        }
        await QuestService.handleWinBattle(userId);
        await AchievementService.UpdateUserAchievement(userId, "npcBattleWins");
        logger.debug(`Processed NPC battle won event objectives for user ${userId}`);
    }
    catch (error) {
        LogErrorStack({ message: "Error handling NPC battle won event objectives", error });
    }
};
export const handlePVPBattleWonEvent = async (payload) => {
    try {
        const { userId, targetId, targetLevel, targetUsername, postBattleAction } = payload;
        const user = await UserRepository.getUserById(userId);
        const target = await UserRepository.getUserById(targetId);
        if (!user || !target) {
            logger.error(`User or target not found for PVP battle won event: user ${userId}, target ${targetId}`);
            return;
        }
        await QuestService.handlePvPKill(userId, targetLevel);
        if (postBattleAction) {
            await QuestService.handlePvPPostBattleChoice(userId, targetId, postBattleAction);
        }
        await QuestService.handleDefeatSpecificPlayer(userId, targetId);
        await QuestService.handleDefeatPlayerByUsername(userId, targetUsername);
        await QuestService.handleWinBattle(userId);
        await AchievementService.UpdateUserAchievement(userId, "battleWins");
        logger.debug(`Processed PVP battle won event objectives for user ${userId}`);
    }
    catch (error) {
        LogErrorStack({ message: "Error handling PVP battle won event objectives", error });
    }
};
