import * as LeaderboardController from "./leaderboard.controller.js";
import authHelper from "../../middleware/authMiddleware.js";
import { routeHandler } from "../../utils/routeHandler.js";
import { Router } from "express";
const router = Router();
router.get("/list", authHelper.IsLoggedIn, routeHandler(async () => {
    return await LeaderboardController.getLeaderBoards();
}));
export default router;
