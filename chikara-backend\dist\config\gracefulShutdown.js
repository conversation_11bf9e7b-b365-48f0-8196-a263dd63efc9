import logger from "./logger.js";
import { redisClient } from "./redisClient.js";
import { closeSockets } from "./socket.js";
import { db } from "../lib/db.js";
import expiryQueueService from "../queues/expiryValues/queue.js";
import { closeScheduler } from "../queues/scheduler/scheduler.js";
import gracefulShutdownHandler from "http-graceful-shutdown";
export async function performGracefulShutdown() {
    logger.debug("Server is shutting down gracefully...");
    await closeSockets();
    logger.debug("WebSocket connections closed");
    await closeScheduler();
    logger.debug("BullMQ scheduler closed");
    await expiryQueueService.closeQueues();
    logger.debug("Expiry queues closed");
    await redisClient.destroy();
    logger.debug("Redis connection closed");
    await db.$disconnect();
    logger.debug("Database connection closed");
}
export function configureGracefulShutdown(server, isDevelopment, timeout = 30000, signals = "SIGINT SIGTERM") {
    gracefulShutdownHandler(server, {
        signals,
        timeout,
        development: isDevelopment,
        onShutdown: performGracefulShutdown,
        finally: () => {
            logger.info("Graceful server shutdown complete");
        },
    });
}
export default {
    performGracefulShutdown,
    configureGracefulShutdown,
};
