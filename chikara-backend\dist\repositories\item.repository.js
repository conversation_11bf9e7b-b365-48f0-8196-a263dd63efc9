import { db } from "../lib/db.js";
import { getNow } from "../utils/dateHelpers.js";
export const findAllItems = async () => {
    return await db.item.findMany();
};
export const findUpgradeItems = async () => {
    return await db.item.findMany({
        where: { itemType: "upgrade" },
    });
};
export const createItem = async (data) => {
    return await db.item.create({ data });
};
export const findItemById = async (id) => {
    return await db.item.findUnique({
        where: { id },
    });
};
export const updateItem = async (id, data) => {
    return await db.item.update({
        where: { id },
        data,
    });
};
export const findUserItemWithItem = async (id) => {
    return await db.user_item.findUnique({
        where: { id },
        include: { item: true },
    });
};
export const findUserItemById = async (userItemId, userId) => {
    return await db.user_item.findUnique({
        where: { id: userItemId, userId },
        include: { item: true },
    });
};
export const findItemByName = async (name) => {
    return await db.item.findFirst({
        where: { name },
        select: { id: true },
    });
};
export const createGlobalMessages = async (senderId, message, userIds) => {
    const createdAt = getNow();
    const updatedAt = getNow();
    return await db.private_message.createMany({
        data: userIds.map((userId) => ({
            message,
            senderId,
            receiverId: userId,
            read: false,
            isGlobal: true,
            createdAt,
            updatedAt,
        })),
    });
};
export const findAllUserIds = async () => {
    return await db.user.findMany({
        select: { id: true },
    });
};
export const updateUserHealth = async (userId, data) => {
    return await db.user.update({
        where: { id: userId },
        data,
    });
};
export const findGangById = async (gangId) => {
    return await db.gang.findUnique({
        where: { id: gangId },
    });
};
export const updateGangResources = async (gangId, materialsToAdd = 0, toolsToAdd = 0) => {
    const gang = await findGangById(gangId);
    if (!gang)
        return null;
    return await db.gang.update({
        where: { id: gangId },
        data: {
            materialsResource: (gang.materialsResource || 0) + materialsToAdd,
            toolsResource: (gang.toolsResource || 0) + toolsToAdd,
        },
    });
};
export const updateGangMemberResources = async (gangMemberId, materialsToAdd = 0, toolsToAdd = 0) => {
    const member = await db.gang_member.findUnique({
        where: { id: gangMemberId },
    });
    if (!member)
        return null;
    return await db.gang_member.update({
        where: { id: gangMemberId },
        data: {
            weeklyMaterials: (member.weeklyMaterials || 0) + materialsToAdd,
            weeklyTools: (member.weeklyTools || 0) + toolsToAdd,
        },
    });
};
export const findGangMember = async (userId) => {
    return await db.gang_member.findFirst({
        where: { userId: userId },
    });
};
export const findItemsByIds = async (itemIds) => {
    return await db.item.findMany({
        where: {
            id: { in: itemIds },
        },
    });
};
export const findItemsByIdsForScavenging = async (itemIds) => {
    return await db.item.findMany({
        where: {
            id: { in: itemIds },
        },
        select: {
            id: true,
            name: true,
            image: true,
            itemType: true,
            rarity: true,
            cashValue: true,
        },
    });
};
export const findItemNamesById = async (itemIds) => {
    if (!itemIds || itemIds.length === 0) {
        return {};
    }
    const items = await db.item.findMany({
        where: { id: { in: itemIds } },
        select: { id: true, name: true },
    });
    return items.reduce((acc, item) => {
        acc[item.id] = item.name;
        return acc;
    }, {});
};
export const findItemWithCashValue = async (itemId) => {
    return await db.item.findUnique({
        where: {
            id: itemId,
            cashValue: { not: null },
        },
    });
};
export const bulkCreateItems = async (items) => {
    return await db.item.createMany({ data: items }).then(() => db.item.findMany());
};
