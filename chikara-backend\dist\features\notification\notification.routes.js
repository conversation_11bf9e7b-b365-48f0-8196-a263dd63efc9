import * as NotificationController from "./notification.controller.js";
import authHelper from "../../middleware/authMiddleware.js";
import { routeHandler } from "../../utils/routeHandler.js";
import { Router } from "express";
const router = Router();
router.get("/list", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await NotificationController.getNotificationList(req.user.id, Number(req.query.limit));
}));
router.get("/unreadNotifications", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await NotificationController.getNumberOfUnreadNotifications(req.user.id);
}));
router.post("/read", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await NotificationController.MarkNotificationRead(req.user.id, req.body.notificationId);
}));
router.post("/notifications/save-token", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await NotificationController.saveFCMToken(req.user.id, req.body.token);
}));
router.post("/update-push-notification-settings", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await NotificationController.UpdatePushNotificationSettings(req.user.id, req.body.pushEnabled);
}));
export default router;
