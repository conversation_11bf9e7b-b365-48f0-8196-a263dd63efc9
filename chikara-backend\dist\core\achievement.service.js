import { db } from "../lib/db.js";
import { logger } from "../utils/log.js";
const UserAchievements = db.user_achievements;
export const UpdateUserAchievement = async (userId, achievementType, amount = 1) => {
    try {
        const userAchievements = await UserAchievements.upsert({
            where: { userId },
            create: { userId },
            update: {},
        });
        if (!userAchievements) {
            throw new Error(`No achievement record found or created`);
        }
        if (!(achievementType in userAchievements)) {
            throw new Error(`Invalid achievement type: ${achievementType}`);
        }
        const updatedAchievements = await UserAchievements.update({
            where: { userId },
            data: {
                [achievementType]: {
                    increment: amount,
                },
            },
        });
        return updatedAchievements;
    }
    catch (error) {
        logger.error(`Failed to increment achievement ${achievementType} for user ${userId}: ` + error);
        throw error;
    }
};
export const GetUserAchievement = async (userId, achievementType) => {
    try {
        const userAchievements = await UserAchievements.findUnique({
            where: { userId },
        });
        if (!userAchievements) {
            return 0;
        }
        if (!(achievementType in userAchievements)) {
            throw new Error(`Invalid achievement type: ${achievementType}`);
        }
        return userAchievements[achievementType];
    }
    catch (error) {
        logger.error(`Failed to get achievement ${achievementType} for user ${userId}: ` + error);
        throw error;
    }
};
export default { UpdateUserAchievement, GetUserAchievement };
