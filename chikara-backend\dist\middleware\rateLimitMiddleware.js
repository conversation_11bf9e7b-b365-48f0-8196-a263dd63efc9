import rateLimit from "express-rate-limit";
import ms from "ms";
export const globalLimiter = rateLimit({
    windowMs: ms("15m"),
    limit: 500,
    standardHeaders: true,
    legacyHeaders: false,
    message: { status: 429, error: "Too many requests, please try again later." },
    skip: (req) => {
        return req.path === "/live" || req.path === "/health";
    },
});
export const authLimiter = rateLimit({
    windowMs: ms("15m"),
    limit: 5,
    standardHeaders: true,
    legacyHeaders: false,
    message: { status: 429, error: "Too many login attempts, please try again later." },
});
export const apiLimiter = rateLimit({
    windowMs: ms("10m"),
    limit: 50,
    standardHeaders: true,
    legacyHeaders: false,
    message: { status: 429, error: "Too many API requests, please try again later." },
});
export default globalLimiter;
