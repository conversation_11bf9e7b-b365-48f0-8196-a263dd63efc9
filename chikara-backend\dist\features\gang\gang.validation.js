import z from "zod";
const createGangSchema = z.object({
    name: z.string().min(3).max(30),
    description: z.string().max(200).optional(),
});
const updateGangSchema = z.object({
    name: z.string().min(3).max(30).optional(),
    description: z.string().max(200).optional(),
    about: z.string().max(1000).optional(),
    motd: z.string().max(1000).optional(),
});
const updateGangInfoSchema = z.object({
    about: z.string().max(1000).optional(),
    motd: z.string().max(1000).optional(),
    gang_avatar: z.instanceof(File).optional(),
});
const inviteMemberSchema = z.object({
    userId: z.number().positive(),
});
const kickMemberSchema = z.object({
    userId: z.number().positive(),
});
const getGangInfoSchema = z.object({
    gangId: z.number().positive(),
});
const getGangLogsSchema = z.object({
    gangId: z.number().positive(),
});
const getMemberSharesSchema = z.object({
    gangId: z.number().positive(),
});
const requestInviteSchema = z.object({
    gangId: z.number().positive(),
});
const acceptInviteSchema = z.object({
    inviteId: z.number().positive(),
});
const declineInviteSchema = z.object({
    inviteId: z.number().positive(),
});
const assignRankSchema = z.object({
    targetUserId: z.number().positive(),
    rank: z.number().int().min(1).max(6),
});
const updatePayoutSharesSchema = z.object({
    shares: z.array(z.object({
        userId: z.number().positive(),
        payoutShare: z.number().min(0),
    })),
});
const gangValidation = {
    createGang: createGangSchema,
    updateGang: updateGangSchema,
    updateGangInfo: updateGangInfoSchema,
    inviteMember: inviteMemberSchema,
    kickMember: kickMemberSchema,
    getGangInfo: getGangInfoSchema,
    getGangLogs: getGangLogsSchema,
    getMemberShares: getMemberSharesSchema,
    requestInvite: requestInviteSchema,
    acceptInvite: acceptInviteSchema,
    declineInvite: declineInviteSchema,
    assignRank: assignRankSchema,
    updatePayoutShares: updatePayoutSharesSchema,
};
export default gangValidation;
