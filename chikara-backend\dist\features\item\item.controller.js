import * as InventoryService from "../../core/inventory.service.js";
import { MAX_ITEM_LEVEL_REQ, MAX_ITEM_UPGRADE_LEVEL, UPGRADE_CORE_TIERS, UPGRADE_CORE_VALUES, UPGRADE_LEVEL_REQUIREMENTS, WEAPON_UPGRADE_LEVEL_REQUIREMENTS, } from "./item.constants.js";
import { IsItemUpgradeable } from "./item.helpers.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { LogErrorStack } from "../../utils/log.js";
import { db } from "../../lib/db.js";
export const getUpgradeItems = async () => {
    try {
        const upgradeItems = await ItemRepository.findUpgradeItems();
        return { data: upgradeItems };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Unexpected server error", statusCode: 500 };
    }
};
const calculateSuccessRate = (upgradeLevel, itemLevel, totalCoreValue, itemType) => {
    const upgradeRequirements = itemType === "Weapon" ? WEAPON_UPGRADE_LEVEL_REQUIREMENTS : UPGRADE_LEVEL_REQUIREMENTS;
    const requiredValue = itemLevel < MAX_ITEM_LEVEL_REQ
        ? upgradeRequirements[upgradeLevel]
        : upgradeRequirements[upgradeLevel];
    return Math.min(totalCoreValue / requiredValue, 1);
};
export const upgradeItem = async (userId, upgradeCores, itemId) => {
    const itemInstanceId = Number.parseInt(itemId);
    try {
        const itemToUpgrade = await ItemRepository.findUserItemWithItem(itemInstanceId);
        if (!itemToUpgrade || itemToUpgrade.count < 1 || !itemToUpgrade.item) {
            return { error: "Item not found!", statusCode: 400 };
        }
        if (!IsItemUpgradeable(itemToUpgrade.item)) {
            return { error: "Item is not upgradeable", statusCode: 400 };
        }
        const weaponTypes = ["weapon", "ranged", "offhand"];
        const coreItemType = weaponTypes.includes(itemToUpgrade.item.itemType) ? "Weapon" : "Armor";
        let totalCoreValue = 0;
        for (const coreType in upgradeCores) {
            if (Object.prototype.hasOwnProperty.call(upgradeCores, coreType)) {
                const core = upgradeCores[coreType];
                const inputItem = await ItemRepository.findItemById(core.id);
                if (!inputItem) {
                    return { error: `Upgrade Core (ID: ${core.id}) not found`, statusCode: 404 };
                }
                if (!inputItem.name.includes(coreItemType)) {
                    return { error: "Invalid upgrade core type", statusCode: 400 };
                }
                const upgradeCoreTier = UPGRADE_CORE_TIERS[coreType];
                if (!upgradeCoreTier) {
                    return { error: "Invalid upgrade core", statusCode: 400 };
                }
                if (!core.count || core.count < 1) {
                    return { error: `Please enter a valid amount of upgrade cores for ${coreType}`, statusCode: 400 };
                }
                if (!(await InventoryService.UserHasNumberOfItem(userId, core.id, core.count))) {
                    return { error: `You don't own this amount of ${coreType} upgrade cores`, statusCode: 400 };
                }
                totalCoreValue += UPGRADE_CORE_VALUES[upgradeCoreTier] * core.count;
            }
        }
        if (itemToUpgrade.upgradeLevel >= MAX_ITEM_UPGRADE_LEVEL) {
            return { error: "Item is already at maximum level", statusCode: 400 };
        }
        const upgradeLevel = itemToUpgrade.upgradeLevel + 1;
        const successRate = calculateSuccessRate(upgradeLevel, itemToUpgrade.item.level, totalCoreValue, coreItemType);
        const isSuccess = Math.random() < successRate;
        const result = await db.$transaction(async (tx) => {
            for (const coreType in upgradeCores) {
                if (Object.prototype.hasOwnProperty.call(upgradeCores, coreType)) {
                    const core = upgradeCores[coreType];
                    await InventoryService.SubtractItemFromUser({ userId, itemId: core.id, amount: core.count, tx });
                }
            }
            const coresUsedStr = `SM: ${upgradeCores?.small?.count} MD: ${upgradeCores?.medium?.count} LG: ${upgradeCores?.large?.count} GI: ${upgradeCores?.giant?.count}`;
            if (isSuccess) {
                const itemToAdd = itemToUpgrade.item.id;
                await InventoryService.SubtractUserItemFromUser(itemInstanceId, 1, tx);
                const newItemInstance = await InventoryService.AddItemToUser({
                    userId,
                    itemId: itemToAdd,
                    amount: 1,
                    isTradeable: true,
                    upgradeLevel,
                    tx,
                });
                return { success: true, newItemInstance, coresUsedStr };
            }
            return { success: false, coresUsedStr };
        });
        if (result.success && result.newItemInstance) {
            logAction({
                action: "ITEM_UPGRADED",
                userId: userId,
                info: {
                    itemId: itemToUpgrade.item.id,
                    itemName: itemToUpgrade.item.name,
                    upgradeLevel: result.newItemInstance.upgradeLevel,
                    successChance: successRate * 100,
                    coresUsed: result.coresUsedStr,
                },
            });
            return { data: { message: "Upgrade successful", newItemInstance: result.newItemInstance } };
        }
        logAction({
            action: "ITEM_UPGRADE_FAILED",
            userId: userId,
            info: {
                itemId: itemToUpgrade.item.id,
                itemName: itemToUpgrade.item.name,
                upgradeLevel: itemToUpgrade.upgradeLevel + 1,
                successChance: successRate * 100,
                coresUsed: result.coresUsedStr,
            },
        });
        return { data: { message: "Upgrade failed", itemToUpgrade } };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to upgrade item", statusCode: 400 };
    }
};
