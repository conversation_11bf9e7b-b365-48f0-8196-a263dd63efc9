import LoadingState from "@/components/LoadingState";
import { formatDistanceToNow } from "date-fns";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import useRemoveFriend from "../api/useRemoveFriend";
import useRespondToFriendRequest from "../api/useRespondToFriendRequest";
import useUpdateFriendNote from "../api/useUpdateFriendNote";
import type { Friend, FriendRequest } from "../types/social";
import StatusMessageSetting from "./StatusMessageSetting";
import { AcceptButton, DeclineButton, EditNoteButton, MessageButton, RemoveButton } from "./common/ActionButtons";
import { ConfirmRemoveDialog } from "./common/ConfirmRemoveDialog";
import { EmptyStateMessage } from "./common/EmptyStateMessage";
import { ListSection } from "./common/ListSection";
import { NoteDialog } from "./common/NoteDialog";
import { UserInfo } from "./common/UserInfo";

interface FriendItemProps {
    friend: Friend;
}

function FriendItem({ friend }: FriendItemProps) {
    const navigate = useNavigate();
    const [noteDialogOpen, setNoteDialogOpen] = useState(false);
    const [confirmRemoveOpen, setConfirmRemoveOpen] = useState(false);

    const { mutate: removeFriend, isPending: isRemovePending } = useRemoveFriend();
    const { mutate: updateNote, isPending: isUpdateNotePending } = useUpdateFriendNote();

    // Check if user is online - assuming within the last 10 minutes
    const isOnline = new Date().getTime() - new Date(friend.friend.last_activity).getTime() < 10 * 60 * 1000;

    // Format the last activity time
    const lastActivityFormatted = friend.friend.last_activity
        ? formatDistanceToNow(new Date(friend.friend.last_activity), { addSuffix: true })
        : "Unknown";

    const handleRemoveFriend = () => {
        removeFriend({ friendId: friend.friendId });
    };

    const handleUpdateNote = (note: string) => {
        updateNote({ friendId: friend.friendId, note });
    };

    const startConversation = (userId: number) => {
        navigate(`/inbox/${userId}`);
    };

    if (!friend) return null;

    return (
        <div className="flex items-center justify-between p-2 rounded-lg bg-gray-900/50 hover:bg-gray-900/80 transition-colors">
            <UserInfo
                showOnlineStatus
                user={friend.friend}
                note={friend.note}
                isOnline={isOnline}
                avatarType="friend"
            />

            <div className="flex items-center gap-2">
                {!isOnline && <span className="text-gray-400 text-xs lg:text-sm">{lastActivityFormatted}</span>}
                <MessageButton onClick={() => startConversation(friend.friend.id)} />
                <EditNoteButton onClick={() => setNoteDialogOpen(true)} />
                <RemoveButton onClick={() => setConfirmRemoveOpen(true)} />
            </div>

            {/* Note Dialog */}
            <NoteDialog
                open={noteDialogOpen}
                initialNote={friend.note || ""}
                isPending={isUpdateNotePending}
                title="Friend Note"
                onOpenChange={setNoteDialogOpen}
                onSave={handleUpdateNote}
            />

            {/* Confirm Remove Dialog */}
            <ConfirmRemoveDialog
                open={confirmRemoveOpen}
                isPending={isRemovePending}
                title="Remove Friend"
                itemType="Friend"
                username={friend.friend.username}
                onOpenChange={setConfirmRemoveOpen}
                onConfirm={handleRemoveFriend}
            />
        </div>
    );
}

interface FriendRequestItemProps {
    request: FriendRequest;
}

function FriendRequestItem({ request }: FriendRequestItemProps) {
    const { mutate: respondToRequest, isPending } = useRespondToFriendRequest();

    const handleAccept = () => {
        respondToRequest({ requestId: request.id, accept: true });
    };

    const handleDecline = () => {
        respondToRequest({ requestId: request.id, accept: false });
    };

    const requestDate = formatDistanceToNow(new Date(request.createdAt), { addSuffix: true });

    return (
        <div className="flex items-center justify-between p-2 rounded-lg bg-gray-900/50 hover:bg-gray-900/80 transition-colors">
            <div className="flex items-center gap-2">
                <UserInfo user={request.sender} avatarType="friend" />
            </div>
            <div className="text-xs text-gray-400 ml-auto mr-2 lg:mr-4">{requestDate}</div>

            <div className="flex items-center gap-2">
                <AcceptButton disabled={isPending} onClick={handleAccept} />
                <DeclineButton disabled={isPending} onClick={handleDecline} />
            </div>
        </div>
    );
}

interface FriendsListProps {
    friendsList: Friend[] | undefined;
    friendRequests?: FriendRequest[] | undefined;
    isLoading: boolean;
    isLoadingRequests?: boolean;
    statusMessage: string | null;
    statusMessageUpdatedAt: Date | null;
    showOnlineStatus: boolean;
}

export default function FriendsList({
    friendsList,
    friendRequests = [],
    isLoading,
    isLoadingRequests = false,
    statusMessage,
    statusMessageUpdatedAt,
    showOnlineStatus,
}: FriendsListProps) {
    return (
        <div className="px-2 py-1.5">
            <LoadingState isLoading={isLoading || isLoadingRequests}>
                <div className="space-y-4">
                    <StatusMessageSetting
                        statusMessage={statusMessage}
                        statusMessageUpdatedAt={statusMessageUpdatedAt}
                        showOnlineStatus={showOnlineStatus}
                    />

                    {/* Friend Requests Section */}
                    {friendRequests && friendRequests.length > 0 && (
                        <ListSection title="Friend Requests" count={friendRequests.length} colorScheme="blue">
                            {friendRequests.map((request) => (
                                <FriendRequestItem key={request.id} request={request} />
                            ))}
                        </ListSection>
                    )}

                    {/* Friends Section */}
                    {friendsList?.length ? (
                        <ListSection title="Friends" count={friendsList.length} colorScheme="blue">
                            {friendsList.map((friend) => (
                                <FriendItem key={friend.id} friend={friend} />
                            ))}
                        </ListSection>
                    ) : (
                        <EmptyStateMessage itemType="Friends" />
                    )}
                </div>
            </LoadingState>
        </div>
    );
}
