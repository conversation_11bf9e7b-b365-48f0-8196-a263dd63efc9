import * as DropChanceService from "../dropchance/dropchance.controller.js";
import * as ItemControllerAdmin from "./item.admin.js";
import * as ItemController from "./item.controller.js";
import itemSchema from "./item.validation.js";
import * as UniqueItemController from "./uniqueitem.service.js";
import * as authHelper from "../../middleware/authMiddleware.js";
import { validate } from "../../middleware/validate.js";
import { routeHandler } from "../../utils/routeHandler.js";
import express from "express";
const router = express.Router();
router.get("/upgrade-items", authHelper.IsLoggedIn, validate(itemSchema.getUpgradeItems), routeHandler(async () => {
    return await ItemController.getUpgradeItems();
}));
router.post("/upgrade-item", authHelper.IsLoggedInAndCanMakeStateChanges, validate(itemSchema.upgradeItem), routeHandler(async (req) => {
    const { upgradeCores, itemId } = req.body;
    return await ItemController.upgradeItem(req.user.id, upgradeCores, itemId);
}));
router.post("/unique/deathnote", authHelper.IsLoggedInAndCanMakeStateChanges, validate(itemSchema.deathNote), routeHandler(async (req) => {
    const { userId, injuryName, injuryType } = req.body;
    return await UniqueItemController.useDeathNote(req.user.id, userId, injuryName, injuryType);
}));
router.post("/unique/lifenote", authHelper.IsLoggedInAndCanMakeStateChanges, validate(itemSchema.lifeNote), routeHandler(async (req) => {
    const { userId } = req.body;
    return await UniqueItemController.useLifeNote(req.user.id, userId);
}));
router.post("/unique/megaphone", authHelper.IsLoggedInAndCanMakeStateChanges, validate(itemSchema.megaphone), routeHandler(async (req) => {
    const { message } = req.body;
    return await UniqueItemController.useMegaPhone(req.user.id, message);
}));
router.post("/unique/kompromat", authHelper.IsLoggedInAndCanMakeStateChanges, validate(itemSchema.kompromat), routeHandler(async (req) => {
    const { userId, reason } = req.body;
    return await UniqueItemController.useKompromat(req.user.id, userId, reason);
}));
router.post("/unique/daily-chest", authHelper.IsLoggedInAndCanMakeStateChanges, routeHandler(async (req) => {
    return await UniqueItemController.useDailyChest(req.user.id);
}));
router.post("/unique/materials-crate", authHelper.IsLoggedInAndCanMakeStateChanges, routeHandler(async (req) => {
    return await UniqueItemController.useRawMaterialsCrate(req.user.id);
}));
router.post("/unique/tools-crate", authHelper.IsLoggedInAndCanMakeStateChanges, routeHandler(async (req) => {
    return await UniqueItemController.useToolsCrate(req.user.id);
}));
router.get("/unique/daily-chest-items", authHelper.IsLoggedIn, routeHandler(async () => {
    return await UniqueItemController.getDailyChestItems();
}));
router.get("/list", authHelper.IsAdmin, validate(itemSchema.getAll), routeHandler(async () => {
    return await ItemControllerAdmin.itemList();
}));
router.post("/create", authHelper.IsAdmin, validate(itemSchema.create), routeHandler(async (req) => {
    return await ItemControllerAdmin.createItem(req.body, req.user.id);
}));
router.post("/update", authHelper.IsAdmin, validate(itemSchema.update), routeHandler(async (req) => {
    return await ItemControllerAdmin.editItem(req.body);
}));
router.get("/drop-tables", authHelper.IsAdmin, routeHandler(async () => {
    return await DropChanceService.getDropTables();
}));
export default router;
