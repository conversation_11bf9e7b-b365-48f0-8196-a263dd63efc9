import gameConfig, { publicConfig } from "../../config/gameConfig.js";
import * as EquipmentService from "../../core/equipment.service.js";
import * as InventoryService from "../../core/inventory.service.js";
import { emitStatsTrained } from "../../core/events/game-event.service.js";
import * as UserService from "../../core/user.service.js";
import * as BattleHelpers from "../battle/helpers/battle.helpers.js";
import * as TalentHelper from "../talents/talents.helpers.js";
import * as QuestService from "../../core/quest.service.js";
import * as UserHelper from "./user.helpers.js";
import * as UserRepository from "../../repositories/user.repository.js";
import * as SkillsService from "../../core/skills.service.js";
import { logAction } from "../../lib/actionLogger.js";
import * as imagesHelper from "../../utils/images.js";
import { LogErrorStack } from "../../utils/log.js";
import { ItemTypes } from "@prisma/client";
import * as ItemRepository from "../../repositories/item.repository.js";
import * as CraftingRepository from "../../repositories/crafting.repository.js";
const { FOCUS_TO_EXP_RATIO, DAILY_FATIGUE_CAP } = gameConfig;
const processProfileUpdate = async (currentUser, input) => {
    const updateValues = {};
    const detailsChangedLog = [];
    if (input.about && input.about !== currentUser.about) {
        if (input.about.length > 500) {
            return { error: "Description is too long", statusCode: 400 };
        }
        updateValues.about = input.about;
        detailsChangedLog.push("Updated profile description");
    }
    if (input.username && input.username !== currentUser.username) {
        if ((await UserRepository.findUserByUsername(input.username)) !== null) {
            return { error: "Username is taken", statusCode: 400 };
        }
        updateValues.username = input.username;
        detailsChangedLog.push(`Updated username from ${currentUser.username} to ${input.username}`);
    }
    if (input.avatarData) {
        const avatar = await imagesHelper.saveAsWebp(input.avatarData, imagesHelper.UploadType.AVATAR, currentUser.avatar || undefined);
        if (avatar) {
            updateValues.avatar = avatar;
            detailsChangedLog.push(`Updated avatar to ${avatar}`);
        }
        else {
            return { error: "Invalid avatar file type", statusCode: 400 };
        }
    }
    if (input.bannerData) {
        if (!(await QuestService.hasUserCompletedBannerQuest())) {
            return { error: "Profile banner quest not completed", statusCode: 400 };
        }
        const profileBanner = await imagesHelper.saveAsWebp(input.bannerData, imagesHelper.UploadType.PROFILE_BANNER, currentUser.profileBanner || undefined);
        if (profileBanner) {
            updateValues.profileBanner = profileBanner;
            detailsChangedLog.push(`Updated profile banner to ${profileBanner}`);
        }
        else {
            return { error: "Invalid banner file type", statusCode: 400 };
        }
    }
    return { updateValues, detailsChangedLog };
};
export const getCurrentUserInfo = async (userId) => {
    const user = await UserRepository.getUserWithAchievements(userId);
    if (!user) {
        return { error: "User not found" };
    }
    const battleState = await BattleHelpers.IsUserInBattle(user);
    const userData = UserHelper.GetSafeUserDict({ ...user });
    if (battleState) {
        const battleAggressor = Number.parseInt(battleState.aggressorId) === user.id;
        if (battleAggressor) {
            userData.battleValidUntil = battleState.validUntil;
            userData.activeBattleType = battleState.battleType;
            userData.battleAggressor = battleAggressor;
        }
    }
    userData.gameConfigVersion = gameConfig.version;
    userData.xpForNextLevel = UserService.getXpForNextLevel(user);
    return { data: userData };
};
export const getUserInfo = async (userId) => {
    const user = await UserRepository.getUserProfile(userId);
    if (!user) {
        return { error: "User not found" };
    }
    return { data: user };
};
export const getInventory = async (userId) => {
    const inventory = await UserRepository.findUserInventory(userId);
    return { data: inventory };
};
export const getEquippedItems = async (userId) => {
    const equippedItems = await EquipmentService.GetEquippedItems(userId);
    return { data: equippedItems };
};
export const getTradeableInventory = async (userId) => {
    const currentUser = await UserRepository.findTradeableInventory(userId);
    const userItems = currentUser?.user_item || [];
    return { data: userItems };
};
export const userList = async () => {
    const users = await UserRepository.getAllUsers();
    return { data: users };
};
export const UpdateProfileDetails = async (userId, description, username, files) => {
    const currentUser = await UserHelper.GetUserByIdWithAssociations(userId);
    if (!currentUser) {
        return { error: "User not found" };
    }
    if (currentUser.profileDetailBanUntil && currentUser.profileDetailBanUntil > Date.now()) {
        return {
            error: "You are currently banned from updating your profile details",
            statusCode: 400,
        };
    }
    try {
        const input = {};
        if (description) {
            input.about = description;
        }
        if (username) {
            input.username = username;
        }
        if (files?.avatar && files.avatar.length > 0) {
            const avatarFile = files.avatar[0];
            input.avatarData = avatarFile;
        }
        if (files?.banner && files.banner.length > 0) {
            const bannerFile = files.banner[0];
            input.bannerData = bannerFile;
        }
        const processResult = await processProfileUpdate(currentUser, input);
        if ("error" in processResult) {
            return processResult;
        }
        const { updateValues, detailsChangedLog } = processResult;
        const result = await UserRepository.updateUserProfile(currentUser.id, updateValues);
        for (const detail of detailsChangedLog) {
            logAction({
                action: "UPDATED_ACCOUNT_DETAILS",
                userId: currentUser.id,
                info: {
                    detail,
                },
            });
        }
        return { data: UserHelper.GetSafeUserDict(result) };
    }
    catch (error) {
        LogErrorStack({ error });
        const message = error instanceof Error ? error.message : "Something went wrong updating profile details";
        if (message.includes("Unsupported file type") ||
            message.includes("Image dimensions too large") ||
            message.includes("File type mismatch")) {
            return { error: message, statusCode: 400 };
        }
        return { error: "Failed to update profile details", statusCode: 500 };
    }
};
export const updateProfileDetailsORPC = async (userId, input) => {
    const currentUser = await UserHelper.GetUserByIdWithAssociations(userId);
    if (!currentUser) {
        return { error: "User not found" };
    }
    if (currentUser.profileDetailBanUntil && currentUser.profileDetailBanUntil > Date.now()) {
        return {
            error: "You are currently banned from updating your profile details",
            statusCode: 400,
        };
    }
    try {
        const helperInput = {};
        if (input.about) {
            helperInput.about = input.about;
        }
        if (input.username) {
            helperInput.username = input.username;
        }
        if (input.avatar) {
            const buffer = Buffer.from(await input.avatar.arrayBuffer());
            helperInput.avatarData = {
                buffer: buffer,
                mimetype: input.avatar.type,
                originalname: input.avatar.name,
                size: input.avatar.size,
            };
        }
        if (input.banner) {
            const buffer = Buffer.from(await input.banner.arrayBuffer());
            helperInput.bannerData = {
                buffer: buffer,
                mimetype: input.banner.type,
                originalname: input.banner.name,
                size: input.banner.size,
            };
        }
        const processResult = await processProfileUpdate(currentUser, helperInput);
        if ("error" in processResult) {
            return processResult;
        }
        const { updateValues, detailsChangedLog } = processResult;
        const result = await UserRepository.updateUserProfile(currentUser.id, updateValues);
        for (const detail of detailsChangedLog) {
            logAction({
                action: "UPDATED_ACCOUNT_DETAILS",
                userId: currentUser.id,
                info: {
                    detail,
                },
            });
        }
        return { data: UserHelper.GetSafeUserDict(result) };
    }
    catch (error) {
        LogErrorStack({ error });
        const message = error instanceof Error ? error.message : "Something went wrong updating profile details";
        if (message.includes("Unsupported file type") ||
            message.includes("Image dimensions too large") ||
            message.includes("File type mismatch")) {
            return { error: message, statusCode: 400 };
        }
        return { error: "Failed to update profile details", statusCode: 500 };
    }
};
export const train = async (user, stat, focusAmount) => {
    const userStatLevel = await SkillsService.getSkillLevel(user.id, stat);
    if (userStatLevel >= 100) {
        return {
            error: "This stat is already at maximum level!",
            statusCode: 400,
        };
    }
    const currentUser = await UserRepository.getUserById(user.id);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    if (currentUser.focus < focusAmount) {
        return {
            error: "Not enough focus! Earn more focus through gameplay activities.",
            statusCode: 400,
        };
    }
    const dailyFatigueUsed = currentUser.dailyFatigueUsed || 0;
    if (dailyFatigueUsed + focusAmount > DAILY_FATIGUE_CAP) {
        const remainingFatigue = DAILY_FATIGUE_CAP - dailyFatigueUsed;
        return {
            error: `You can only spend ${remainingFatigue} more focus on training today! Daily limit resets at midnight.`,
            statusCode: 400,
        };
    }
    try {
        const baseExpGain = Math.floor(focusAmount * FOCUS_TO_EXP_RATIO);
        const expResult = await SkillsService.addSkillExp(user.id, stat, baseExpGain);
        const statsToUpdate = {};
        statsToUpdate.focus = currentUser.focus - focusAmount;
        statsToUpdate.dailyFatigueUsed = dailyFatigueUsed + focusAmount;
        const updatedUser = await UserRepository.updateUserStats({ id: currentUser.id }, statsToUpdate);
        logAction({
            action: "TRAINED_STAT",
            userId: updatedUser.id,
            info: {
                stat: stat,
                focusSpent: focusAmount,
                expGained: baseExpGain,
                leveledUp: expResult.leveledUp,
                newLevel: expResult.currentLevel,
            },
        });
        await emitStatsTrained({
            userId: currentUser.id,
            amount: focusAmount,
        });
        return {
            data: {
                statProgress: {
                    stat: stat,
                    expGained: baseExpGain,
                    leveledUp: expResult.leveledUp,
                    levelsGained: expResult.levelsGained,
                    currentLevel: expResult.currentLevel,
                    currentExp: expResult.currentExp,
                    expToNextLevel: expResult.expToNextLevel,
                    previousLevel: expResult.previousLevel,
                },
                focusRemaining: updatedUser.focus,
                dailyFatigueRemaining: DAILY_FATIGUE_CAP - updatedUser.dailyFatigueUsed,
            },
        };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to update stats", statusCode: 400 };
    }
};
export const getStatusEffects = async (userId) => {
    try {
        const effects = await UserRepository.findUserStatusEffects(userId);
        return { data: effects };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to get user status effects", error });
        return { error: "Failed to get user status effects", statusCode: 400 };
    }
};
export const equipItem = async (userId, userItemId) => {
    try {
        const user = await UserRepository.getUserById(userId);
        if (!user) {
            return { error: "User not found", statusCode: 404 };
        }
        if (user.jailedUntil !== null) {
            return { error: "Can't equip items while jailed!", statusCode: 400 };
        }
        if (user.hospitalisedUntil != null) {
            return {
                error: "Can't equip items while hospitalised!",
                statusCode: 400,
            };
        }
        const userItem = await UserRepository.findUserItemById(userItemId);
        if (!userItem || userItem.userId !== userId) {
            return { error: "User does not have that item", statusCode: 400 };
        }
        const item = userItem.item;
        if (!item) {
            return { error: "Item not found", statusCode: 400 };
        }
        if (item.level > user.level) {
            return { error: "Level too low", statusCode: 400 };
        }
        if (item.itemType == ItemTypes.offhand && !(await TalentHelper.UserHasOffensiveOffhandsTalent(userId))) {
            return { error: "Not talented enough", statusCode: 400 };
        }
        if (item.itemType == ItemTypes.shield && !(await TalentHelper.UserHasShieldBearerTalent(userId))) {
            return { error: "Not talented enough", statusCode: 400 };
        }
        const success = await UserHelper.EquipItemToUser(user, userItem);
        if (!success) {
            return { error: "Cannot equip item to user", statusCode: 400 };
        }
        logAction({
            action: "EQUIPPED_ITEM",
            userId: user.id,
            info: {
                itemId: item.id,
                itemName: item.name,
            },
        });
        return { data: "Item equipped" };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to equip", statusCode: 400 };
    }
};
export const unequipItem = async (userId, slot) => {
    try {
        await EquipmentService.UnequipItemFromSlot(userId, slot);
        return { data: "Item unequipped" };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to unequip item", statusCode: 400 };
    }
};
export const useItem = async (userId, userItemId) => {
    try {
        const userItem = await UserRepository.findUserItemById(userItemId);
        if (!userItem || userItem.userId !== userId) {
            return { error: "User does not have that item", statusCode: 400 };
        }
        const itemId = userItem.itemId;
        if (itemId === null || itemId === undefined || !(await InventoryService.UserHasItem(userId, itemId))) {
            return { error: "User does not have that item", statusCode: 400 };
        }
        const currentUser = await UserRepository.getUserById(userId);
        const item = await ItemRepository.findItemById(itemId);
        if (!currentUser) {
            return { error: "User not found" };
        }
        if (!item) {
            return { error: "Item not found" };
        }
        if (item.level > currentUser.level) {
            return { error: "Level too low", statusCode: 400 };
        }
        if (item.itemType !== ItemTypes.consumable &&
            item.itemType !== ItemTypes.recipe &&
            item.itemType !== ItemTypes.pet) {
            return { error: "Item type is not usable", statusCode: 400 };
        }
        if (await BattleHelpers.IsUserInBattle(currentUser)) {
            return { error: "Cannot do this while in battle", statusCode: 400 };
        }
        if (currentUser.jailedUntil !== null) {
            return { error: "Can't use items while jailed!", statusCode: 400 };
        }
        if (currentUser.hospitalisedUntil != null) {
            return { error: "Can't use items while hospitalised!", statusCode: 400 };
        }
        let info = {};
        if (item.itemType == ItemTypes.recipe) {
            if (!item.recipeUnlockId) {
                return { error: "Item invalid", statusCode: 400 };
            }
            const recipeToUnlock = await CraftingRepository.findCraftingRecipeById(item.recipeUnlockId);
            if (!recipeToUnlock) {
                return { error: "Item invalid", statusCode: 400 };
            }
            const existingUnlock = await UserRepository.findUserRecipe(currentUser.id, recipeToUnlock.id);
            if (existingUnlock) {
                return { error: "Recipe already unlocked", statusCode: 400 };
            }
            await UserRepository.createUserRecipe(currentUser.id, recipeToUnlock.id);
            info.recipeUnlocked = true;
        }
        if (item.itemType == ItemTypes.consumable) {
            info = await UserHelper.useConsumable(item, currentUser, info);
        }
        if (item.itemType == ItemTypes.pet) {
            info = await UserHelper.usePetItem(item, currentUser, info);
        }
        await InventoryService.SubtractUserItemFromUser(userItemId, 1);
        logAction({
            action: "USED_ITEM",
            userId: currentUser.id,
            info: {
                itemId: item.id,
                itemName: item.name,
                recipeUnlocked: item.recipeUnlockId || undefined,
            },
        });
        return {
            data: {
                currentHealth: currentUser.currentHealth,
                energy: currentUser.energy,
                actionPoints: currentUser.actionPoints,
                info,
            },
        };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to use item", statusCode: 400 };
    }
};
export const getGameConfig = () => {
    try {
        return { data: publicConfig };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to send config", statusCode: 400 };
    }
};
export const linkDiscord = async (_token, _userId) => {
    return { error: "Not implemented" };
};
export const setLastNewsIDRead = async (userId, newsId) => {
    try {
        await UserRepository.updateLastNewsIDRead(userId, newsId);
        return { data: "Success" };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to set last news ID read", statusCode: 400 };
    }
};
export const getAllUserSkills = async (userId, skills) => {
    const skillsInfo = {};
    for (const skill of skills) {
        skillsInfo[skill] = await SkillsService.getSkillInfo(userId, skill);
    }
    return { data: skillsInfo };
};
