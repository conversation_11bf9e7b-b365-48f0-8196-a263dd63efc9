import gameConfig from "../../../config/gameConfig.js";
const { BASE_STAMINA, STAMINA_PER_ENDURANCE_LEVEL } = gameConfig;
export const STAMINA_REGEN_PER_INTELLIGENCE_LEVEL = 0.005;
export const getStaminaRegenModifier = (intelligenceLevel) => {
    return 1 + (intelligenceLevel - 1) * STAMINA_REGEN_PER_INTELLIGENCE_LEVEL;
};
export const getEffectiveMaxStamina = (enduranceLevel) => {
    return BASE_STAMINA + enduranceLevel * STAMINA_PER_ENDURANCE_LEVEL;
};
export const getCriticalHitChance = (dexterityLevel) => {
    return Math.min((dexterityLevel - 1) * 0.003, 0.3);
};
export const getEvasionChance = (enduranceLevel) => {
    return Math.min((enduranceLevel - 1) * 0.0025, 0.25);
};
export const getArmorPenetration = (strengthLevel) => {
    return Math.min((strengthLevel - 1) * 0.002, 0.2);
};
export const getUnavoidableForce = (strengthLevel) => {
    return Math.max(1 - (strengthLevel - 1) * 0.005, 0.5);
};
export const getDotResistance = (defenceLevel) => {
    return Math.min((defenceLevel - 1) * 0.004, 0.4);
};
export const getImpactResistance = (intelligenceLevel) => {
    return Math.min((intelligenceLevel - 1) * 0.003, 0.3);
};
export const getInitiativeModifier = (vitalityLevel) => {
    return Math.min((vitalityLevel - 1) * 0.002, 0.2);
};
export const getHealingImprovement = (vitalityLevel) => {
    return 1 + Math.min((vitalityLevel - 1) * 0.003, 0.3);
};
export const getStrengthPercentageModifier = (strengthLevel) => {
    return 1 + (strengthLevel - 1) * 0.05;
};
export const getDexterityPercentageModifier = (dexterityLevel) => {
    return 1 + (dexterityLevel - 1) * 0.05;
};
export const getDefencePercentageReduction = (defenceLevel) => {
    const percentageReduction = (defenceLevel - 1) * 0.015;
    return Math.min(percentageReduction, 0.6);
};
export const getDefenceFlatReduction = (defenceLevel) => {
    return defenceLevel - 1;
};
