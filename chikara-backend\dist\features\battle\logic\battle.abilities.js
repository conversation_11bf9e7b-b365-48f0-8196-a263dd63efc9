import { GetDamage } from "../helpers/battle.damage.js";
import { ATTACK_TYPE_RANGED, RAGE_ABILITY_NAME, CRIPPLE_ABILITY_NAME, STUN_ABILITY_NAME, HEADBUTT_ABILITY_NAME, SHIELD_BASH_ABILITY_NAME, SHOCKWAVE_ABILITY_NAME, EXHAUST_ABILITY_NAME, HEAL_OVER_TIME_ABILITY_NAME, SLEEP_ABILITY_NAME, SELF_HARM_ABILITY_NAME, SPRAY_ABILITY_NAME, TOXIC_DART_ABILITY_NAME, DISARM_ABILITY_NAME, GIANT_KILLING_SLINGSHOT_ABILITY_NAME, HIGH_GUARD_ABILITY_NAME, } from "../helpers/battle.constants.js";
export const STATUS_ABILITIES = [
    { name: <PERSON><PERSON>_ABILITY_NAME, turns: 4, target: "self" },
    { name: HEAL_OVER_TIME_ABILITY_NAME, turns: 3, target: "self" },
    { name: SELF_HARM_ABILITY_NAME, turns: 3, target: "self" },
    { name: HIGH_GUARD_ABILITY_NAME, turns: 3, target: "self" },
    { name: CRIPPLE_ABILITY_NAME, turns: 3, target: "enemy" },
    { name: STUN_ABILITY_NAME, turns: 3, target: "enemy" },
    { name: SHOCKWAVE_ABILITY_NAME, turns: 3, target: "enemy" },
    { name: EXHAUST_ABILITY_NAME, turns: 3, target: "enemy" },
    { name: SLEEP_ABILITY_NAME, turns: 3, target: "enemy" },
    { name: TOXIC_DART_ABILITY_NAME, turns: 3, target: "enemy" },
    { name: DISARM_ABILITY_NAME, turns: 2, target: "enemy" },
];
const findAbilityByName = (abilities, abilityName) => {
    if (!abilities)
        return null;
    return abilities.find((ability) => ability.name === abilityName) || null;
};
export const GetToxicDartDamage = (target, abilities) => {
    const ability = findAbilityByName(abilities, TOXIC_DART_ABILITY_NAME);
    if (!ability || !ability.currentModifier)
        return 0;
    const currentModifier = ability.currentModifier || 0;
    const currentHpDamage = target.currentHealth * currentModifier;
    const secondaryModifier = ability.secondaryModifier || 0;
    return secondaryModifier + currentHpDamage;
};
export const GetGiantKillingSlingshotDamage = (target, healthyCasterTalentActive, abilityDmgDebuff, abilities) => {
    const ability = findAbilityByName(abilities, GIANT_KILLING_SLINGSHOT_ABILITY_NAME);
    if (!ability || !ability.currentModifier)
        return 1;
    const damage = Math.round(target.currentHealth * ability.currentModifier);
    const finalDamage = healthyCasterTalentActive ? damage * 1.5 : damage;
    return Math.round(Math.max(1, finalDamage * (1 - abilityDmgDebuff)));
};
export const GetHeadbuttDamage = (target, healthyCasterTalentActive, abilityDmgDebuff, abilities) => {
    const ability = findAbilityByName(abilities, HEADBUTT_ABILITY_NAME);
    if (!ability || !ability.currentModifier)
        return 1;
    const damage = Math.round(target.maxHealth * ability.currentModifier);
    const finalDamage = healthyCasterTalentActive ? damage * 1.5 : damage;
    return Math.round(Math.max(1, finalDamage * (1 - abilityDmgDebuff)));
};
export const GetShieldBashDamage = (user, healthyCasterTalentActive, abilityDmgDebuff, abilities) => {
    const ability = findAbilityByName(abilities, SHIELD_BASH_ABILITY_NAME);
    if (!ability || !ability.currentModifier)
        return 1;
    const secondaryModifier = ability.secondaryModifier || 0;
    const currentModifier = ability.currentModifier || 0;
    const damage = secondaryModifier + Math.round(user.attributes.defence * currentModifier);
    const finalDamage = healthyCasterTalentActive ? damage * 1.5 : damage;
    return Math.round(Math.max(1, finalDamage * (1 - abilityDmgDebuff)));
};
export const GetSprayDamage = async (user, target, ammoToUse, abilityDmgDebuff, abilities) => {
    const ability = findAbilityByName(abilities, SPRAY_ABILITY_NAME);
    if (!ability || !ability.currentModifier)
        return 1;
    const sprayDamage = Math.round((await GetDamage(user, target, ATTACK_TYPE_RANGED)) * (0.7 + ability.currentModifier * ammoToUse));
    return Math.round(Math.max(1, sprayDamage * (1 - abilityDmgDebuff)));
};
export const ApplySelfHarmDamage = (user, abilities) => {
    const ability = findAbilityByName(abilities, SELF_HARM_ABILITY_NAME);
    if (!ability || !ability.secondaryModifier)
        return 0;
    const damageAmount = Math.floor(user.maxHealth * ability.secondaryModifier);
    user.currentHealth = Math.max(0, user.currentHealth - damageAmount);
    return damageAmount;
};
