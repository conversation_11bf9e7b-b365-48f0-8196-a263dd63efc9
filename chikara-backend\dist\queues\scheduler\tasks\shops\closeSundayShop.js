import { db } from "../../../../lib/db.js";
import { LogErrorStack } from "../../../../utils/log.js";
export async function closeSundayShop() {
    try {
        await db.shop.update({
            where: { id: 6 },
            data: { disabled: true },
        });
    }
    catch (error) {
        LogErrorStack({ message: "Failed to close Sunday shop:", error });
    }
}
export default closeSundayShop;
