import z from "zod";
const feedPetSchema = z.object({
    petId: z.number().min(1),
    foodType: z.string().min(1),
    quantity: z.number().min(1).max(10),
});
const playPetSchema = z.object({
    petId: z.number().min(1),
    gameType: z.enum(["fetch", "tug", "chase"]),
    duration: z.number().min(5).max(60),
});
const trainPetSchema = z.object({
    petId: z.number().min(1),
    trainingType: z.enum(["agility", "obedience", "tricks"]),
    duration: z.number().min(15).max(120),
});
const customizePetSchema = z.object({
    petId: z.number().min(1),
    customization: z
        .object({
        name: z.string().optional(),
        appearance: z.object({
            color: z.string().optional(),
            accessory: z.string().optional(),
        }).optional(),
    })
        .optional(),
});
const evolvePetSchema = z.object({
    petId: z.number().min(1),
});
const petsSchema = {
    feedPetSchema,
    playPetSchema,
    trainPetSchema,
    customizePetSchema,
    evolvePetSchema,
};
export default petsSchema;
