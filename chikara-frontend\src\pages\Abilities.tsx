import EquipAbilityInfoModal from "@/features/talents/components/EquipAbilityInfoModal";
import { APIROUTES } from "@/helpers/apiRoutes";
import axios from "@/helpers/axiosInstance";
import useGetUnlockedTalents from "@/features/talents/api/useGetUnlockedTalents";
import { cn } from "@/lib/utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import type {
    Ability,
    TalentNameType,
    UnlockedTalents,
    UserTalent,
    TalentInfo,
} from "@/features/talents/types/talents";
import { orpc } from "@/lib/orpc";
import { talentData } from "@/features/talents/data/talentData";
import useGetEquippedAbilities from "@/features/talents/api/useGetEquippedAbilities";

export default function Abilities() {
    const { data } = useGetUnlockedTalents({
        queryKey: APIROUTES.TALENTS.GETUNLOCKEDTALENTS,
        select: (d: UnlockedTalents) => ({
            talentList: d?.talentList?.filter((talent: UserTalent) => talent.talentInfo.staminaCost > 0),
            treePoints: d?.treePoints,
        }),
    });
    const { data: equippedAbilitiesData } = useGetEquippedAbilities();
    const [abilitySelected, setAbilitySelected] = useState<number | null>(null);
    const [abilityInfo, setAbilityInfo] = useState<Ability | null>(null);
    const queryClient = useQueryClient();

    // Extract equipped ability IDs and abilities from the new structure
    const equippedAbilityIds = equippedAbilitiesData
        ? [
              equippedAbilitiesData[0]?.id,
              equippedAbilitiesData[1]?.id,
              equippedAbilitiesData[2]?.id,
              equippedAbilitiesData[3]?.id,
          ]
        : [];

    const equippedAbilityObjects = equippedAbilitiesData
        ? [equippedAbilitiesData[0], equippedAbilitiesData[1], equippedAbilitiesData[2], equippedAbilitiesData[3]]
        : [];

    const equipAbility = useMutation({
        mutationFn: async (slot: number) => {
            if (!abilitySelected) return;

            const response = await axios.post(APIROUTES.TALENTS.EQUIPABILITY, {
                talentId: abilitySelected,
                slot: slot,
            });
            return response;
        },

        onSuccess: () => {
            queryClient.invalidateQueries(orpc.talents.getEquippedAbilities.queryOptions());
            setAbilitySelected(null);
            setAbilityInfo(null);
        },
    });

    const unequipAbility = useMutation({
        mutationFn: async (id: number) => {
            const slot = equippedAbilityIds.findIndex((x) => x === id) + 1;
            const jsonPostBody = JSON.stringify({
                slot: slot,
            });

            const response = await axios.post(`${APIROUTES.TALENTS.UNEQUIPABILITY}`, jsonPostBody);
            return response;
        },
        onSuccess: () => {
            queryClient.invalidateQueries(orpc.talents.getEquippedAbilities.queryOptions());
            setAbilitySelected(null);
            setAbilityInfo(null);
        },
    });

    const clickSelectedAbility = (talentInfo: TalentInfo) => {
        const ability: Ability = {
            ...talentInfo,
            image: talentInfo.image ?? undefined,
        };
        if (!abilitySelected) {
            setAbilitySelected(ability.id);
            setAbilityInfo(ability);
        } else {
            // eslint-disable-next-line no-lonely-if
            if (abilitySelected === ability.id) {
                setAbilitySelected(null);
                setAbilityInfo(null);
            } else {
                setAbilitySelected(ability.id);
                setAbilityInfo(ability);
            }
        }
    };

    const abilitiesUnlocked = data?.talentList?.map((talent) => talent.talentInfo);

    return (
        <div className="p-2 md:mx-auto md:max-w-6xl">
            <p className="text-slate-300">Equipped Abilities</p>
            {abilitySelected && <div className="overlayDim"></div>}
            <div className="mx-2 mt-2 grid grid-cols-2 grid-rows-2 gap-4 rounded-lg border-2 border-slate-500 bg-blue-900 p-2 shadow-xl">
                {equippedAbilityObjects.map((ability: any, i: number) => (
                    <div
                        key={i}
                        className={cn(
                            abilitySelected && "cursor-pointer ring-2 ring-blue-300 hover:ring-blue-200",
                            "z-40 flex h-20 w-full rounded-md bg-slate-800"
                        )}
                        onClick={() => abilitySelected && equipAbility.mutate(i + 1)}
                    >
                        {!ability ? (
                            <p className="m-auto text-slate-300">Empty</p>
                        ) : (
                            <img
                                className="m-auto w-20 cursor-pointer rounded-full p-2"
                                src={talentData[ability.name as TalentNameType]?.image || ability?.image || ""}
                                alt=""
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setAbilityInfo(ability);
                                }}
                            />
                        )}
                    </div>
                ))}
            </div>

            <p className="my-2 text-slate-300">Available Abilities</p>
            <div className="grid grid-cols-5 gap-4">
                {abilitiesUnlocked?.map(
                    (ability: TalentInfo) =>
                        ability &&
                        !equippedAbilityIds?.includes(ability?.id) && (
                            <div key={ability.id} className="z-40">
                                <img
                                    alt=""
                                    src={
                                        talentData[ability.name as TalentNameType]?.image ||
                                        (ability.image ?? undefined) ||
                                        ""
                                    }
                                    className={cn(
                                        abilitySelected === ability.id && "z-40 ring-4 ring-blue-500",
                                        "w-20 cursor-pointer rounded-full"
                                    )}
                                    onClick={() => clickSelectedAbility(ability)}
                                />
                            </div>
                        )
                )}
            </div>
            {abilityInfo && (
                <EquipAbilityInfoModal
                    abilityInfo={abilityInfo}
                    setAbilityInfo={setAbilityInfo}
                    setAbilitySelected={setAbilitySelected}
                    unequipAbility={unequipAbility}
                    abilitySelected={abilitySelected}
                />
            )}
        </div>
    );
}
