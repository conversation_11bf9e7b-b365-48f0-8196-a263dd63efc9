import { db } from "../lib/db.js";
const EXP_BASE = 100;
const EXP_MULTIPLIER = 1.5;
const MAX_SKILL_LEVEL = 100;
export function calculateExpForLevel(level) {
    if (level <= 1)
        return 0;
    if (level > MAX_SKILL_LEVEL)
        return Number.MAX_SAFE_INTEGER;
    return Math.floor(EXP_BASE * Math.pow(level - 1, EXP_MULTIPLIER));
}
export function calculateTotalExpForLevel(level) {
    let totalExp = 0;
    for (let i = 1; i <= level; i++) {
        totalExp += calculateExpForLevel(i);
    }
    return totalExp;
}
export function getExpNeededForNextLevel(currentLevel, currentExp) {
    if (currentLevel >= MAX_SKILL_LEVEL)
        return Infinity;
    const nextLevelExp = calculateExpForLevel(currentLevel + 1);
    return Math.max(0, nextLevelExp - currentExp);
}
export async function getUserSkill(userId, skillType) {
    const userSkill = await db.user_skill.upsert({
        where: {
            userId_skillType: {
                userId,
                skillType,
            },
        },
        update: {},
        create: {
            userId,
            skillType,
            level: 1,
            experience: 0,
            talentPoints: 0,
        },
    });
    return userSkill;
}
export async function getAllUserSkills(userId) {
    return await db.user_skill.findMany({
        where: { userId },
    });
}
export async function addSkillExp(userId, skillType, expAmount) {
    return await db.$transaction(async (tx) => {
        const currentSkill = await tx.user_skill.findUnique({
            where: {
                userId_skillType: {
                    userId,
                    skillType,
                },
            },
        });
        const currentLevel = currentSkill?.level ?? 1;
        const currentExp = currentSkill?.experience ?? 0;
        let newExp = currentExp + expAmount;
        let newLevel = currentLevel;
        let leveledUp = false;
        let levelsGained = 0;
        while (newLevel < MAX_SKILL_LEVEL && newExp >= calculateExpForLevel(newLevel + 1)) {
            newExp -= calculateExpForLevel(newLevel + 1);
            newLevel++;
            leveledUp = true;
            levelsGained++;
        }
        await tx.user_skill.upsert({
            where: {
                userId_skillType: {
                    userId,
                    skillType,
                },
            },
            update: {
                level: newLevel,
                experience: newExp,
                updatedAt: new Date(),
            },
            create: {
                userId,
                skillType,
                level: newLevel,
                experience: newExp,
                talentPoints: 0,
            },
        });
        return {
            leveledUp,
            levelsGained,
            previousLevel: currentLevel,
            currentLevel: newLevel,
            currentExp: newExp,
            expToNextLevel: getExpNeededForNextLevel(newLevel, newExp),
        };
    });
}
export async function getSkillLevel(userId, skillType) {
    const userSkill = await getUserSkill(userId, skillType);
    return userSkill.level;
}
export const formatSkillInfo = (userSkill) => {
    const level = userSkill.level;
    const isMaxLevel = level >= MAX_SKILL_LEVEL;
    return {
        level,
        experience: userSkill.experience,
        expToNextLevel: isMaxLevel ? Infinity : getExpNeededForNextLevel(level, userSkill.experience),
        talentPoints: userSkill.talentPoints,
        maxTalentPoints: level,
    };
};
export async function getSkillInfo(userId, skill) {
    const userSkill = await getUserSkill(userId, skill);
    return formatSkillInfo(userSkill);
}
export async function addSkillTalentPoints(userId, skillType, points) {
    const updatedSkill = await db.user_skill.upsert({
        where: {
            userId_skillType: { userId, skillType },
        },
        update: {
            talentPoints: { increment: points },
            updatedAt: new Date(),
        },
        create: {
            userId,
            skillType,
            level: 1,
            experience: 0,
            talentPoints: points,
        },
    });
    return updatedSkill.talentPoints ?? 0;
}
export async function getSkillTalentPoints(userId, skillType) {
    const userSkill = await getUserSkill(userId, skillType);
    return userSkill.talentPoints ?? 0;
}
