import { buildClient } from "@xata.io/client";
const tables = [
    {
        name: "audit_logs",
        columns: [
            { name: "action", type: "string" },
            { name: "info", type: "json" },
            { name: "logType", type: "string" },
            { name: "userId", type: "int" },
            { name: "metadata", type: "json" },
        ],
    },
];
const DatabaseClient = buildClient();
const defaultOptions = {
    databaseURL: "https://jhowards-s-workspace-ak3kik.eu-west-1.xata.sh/db/actionlogs-chikara",
};
export class XataClient extends DatabaseClient {
    constructor(options) {
        super({ ...defaultOptions, ...options }, tables);
    }
}
let instance;
export const getXataClient = () => {
    if (instance)
        return instance;
    instance = new XataClient();
    return instance;
};
