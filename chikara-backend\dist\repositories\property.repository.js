import { db } from "../lib/db.js";
export const findHousingList = async () => {
    return await db.property.findMany({
        where: { propertyType: "housing" },
    });
};
export const findPropertyById = async (propertyId) => {
    return await db.property.findUnique({
        where: { id: propertyId },
    });
};
export const findUserPropertyByUserAndPropertyId = async (userId, propertyId) => {
    return await db.user_property.findFirst({
        where: {
            userId,
            propertyId,
        },
    });
};
export const findUserPropertyByUserAndPropertyIdWithProperty = async (userId, propertyId) => {
    return await db.user_property.findFirst({
        where: {
            userId,
            propertyId,
        },
        include: {
            property: true,
        },
    });
};
export const findFirstUserProperty = async (userId) => {
    return await db.user_property.findFirst({
        where: { userId },
    });
};
export const findPrimaryUserProperty = async (userId) => {
    return await db.user_property.findFirst({
        where: {
            userId,
            isPrimary: true,
        },
    });
};
export const executePropertyPurchase = async (userId, propertyId, currentCash, propertyCost, isFirstProperty) => {
    return await db.$transaction([
        db.user.update({
            where: { id: userId },
            data: {
                cash: currentCash - propertyCost,
            },
        }),
        db.user_property.create({
            data: {
                userId,
                propertyId,
                isPrimary: isFirstProperty,
                furniture: [],
                customization: {},
            },
        }),
    ]);
};
export const executePropertySale = async (userPropertyId, userId, currentCash, sellPrice) => {
    return await db.$transaction([
        db.user_property.delete({
            where: {
                id: userPropertyId,
            },
        }),
        db.user.update({
            where: { id: userId },
            data: {
                cash: currentCash + sellPrice,
            },
        }),
    ]);
};
export const findUserPropertiesWithDetails = async (userId) => {
    return await db.user_property.findMany({
        where: { userId },
        include: {
            property: true,
        },
        orderBy: [{ isPrimary: "desc" }, { purchaseDate: "asc" }],
    });
};
export const executeSetPrimaryProperty = async (targetPropertyId, currentPrimaryId) => {
    const transactions = [
        db.user_property.update({
            where: { id: targetPropertyId },
            data: { isPrimary: true },
        }),
    ];
    if (currentPrimaryId) {
        transactions.push(db.user_property.update({
            where: { id: currentPrimaryId },
            data: { isPrimary: false },
        }));
    }
    return await db.$transaction(transactions);
};
