import * as NotificationService from "../../core/notification.service.js";
import * as PrivateMessageRepository from "../../repositories/privatemessage.repository.js";
import { NotificationTypes } from "../../types/notification.js";
import { containsBlacklistedWords, getRejectedMessageError } from "../../utils/contentFilter.js";
import { LogErrorStack } from "../../utils/log.js";
export const getChatHistory = async (userId) => {
    try {
        const messages = await PrivateMessageRepository.findAllMessages(userId);
        return { data: messages };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to fetch chat history:", error });
        return { error: "Failed to fetch chat history", statusCode: 400 };
    }
};
export const getNumberOfUnreadMessages = async (userId) => {
    try {
        const unreadCount = await PrivateMessageRepository.countUnreadMessages(userId);
        return { data: { unread: unreadCount } };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to get unread messages:", error });
        return { error: "Failed to get unread messages", statusCode: 400 };
    }
};
export const SendMessage = async (senderId, receiverId, message) => {
    try {
        if (senderId == receiverId) {
            return { error: "Stop talking to yourself", statusCode: 400 };
        }
        if (containsBlacklistedWords(message)) {
            return { error: getRejectedMessageError(), statusCode: 400 };
        }
        await PrivateMessageRepository.createMessage(senderId, receiverId, message);
        NotificationService.NotifyUser(receiverId, NotificationTypes.message, { senderId: senderId });
        return { data: "Message sent successfully" };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to send private message:", error });
        return { error: "Failed to send message", statusCode: 400 };
    }
};
export const MarkMessageRead = async (userId, messageId) => {
    try {
        const message = await PrivateMessageRepository.findMessageById(messageId);
        if (!message || message.receiverId != userId) {
            return { error: "Invalid message", statusCode: 400 };
        }
        await PrivateMessageRepository.updateMessageRead(message);
        return { data: "Message marked as read" };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to mark message read:", error });
        return { error: "Failed to mark message read", statusCode: 400 };
    }
};
export default {
    getChatHistory,
    getNumberOfUnreadMessages,
    SendMessage,
    MarkMessageRead,
};
