import { PRIORITY_MULTIPLIERS } from "./battle.ai.js";
import { GetGiantKillingSlingshotDamage, GetHeadbuttDamage, GetShieldBashDamage, GetSprayDamage, GetToxicDartDamage, } from "../logic/battle.abilities.js";
import * as TalentHelper from "../../talents/talents.helpers.js";
export const evaluateHeadbuttAbility = (aiPlayer, enemy) => {
    let score = 1.5;
    if (enemy.maxHealth > 150) {
        score *= 1.5;
    }
    if (aiPlayer.currentStamina < 100) {
        score *= 0.8;
    }
    return score;
};
export const evaluateShieldBashAbility = (aiPlayer) => {
    let score = 1.2;
    if (aiPlayer.attributes.defence > 60) {
        score *= 1.8;
    }
    else if (aiPlayer.attributes.defence > 40) {
        score *= 1.3;
    }
    return score;
};
export const evaluateSprayAbility = (player, enemy, ammoToUse) => {
    let score = 1.0;
    if (ammoToUse >= 4) {
        score *= 2.5;
    }
    else if (ammoToUse >= 2) {
        score *= 1.5;
    }
    return score;
};
export const evaluateGiantKillingShotshotAbility = (player, enemy) => {
    let score = 1.3;
    const healthPercentage = enemy.currentHealth / enemy.maxHealth;
    if (healthPercentage > 0.8) {
        score *= 2.0;
    }
    else if (healthPercentage < 0.3) {
        score *= 0.6;
    }
    return score;
};
export const evaluateDamageAbility = async (ability, aiPlayer, defender) => {
    let score = 1.0;
    let damage = 0;
    const healthyCasterTalentActive = await TalentHelper.HealthyCasterTalentActiveForUser(aiPlayer);
    const abilityDmgDebuff = aiPlayer.statusEffects["ability_damage_debuff"] || 0;
    switch (ability.name) {
        case "headbutt": {
            damage = GetHeadbuttDamage(defender, healthyCasterTalentActive, abilityDmgDebuff, aiPlayer.abilities);
            score = evaluateHeadbuttAbility(aiPlayer, defender);
            break;
        }
        case "shield_bash": {
            damage = GetShieldBashDamage(aiPlayer, healthyCasterTalentActive, abilityDmgDebuff, aiPlayer.abilities);
            score = evaluateShieldBashAbility(aiPlayer);
            break;
        }
        case "spray": {
            const ammoToUse = aiPlayer.ammo || 0;
            if (ammoToUse < 2 || !aiPlayer.equipment.ranged) {
                return 0;
            }
            damage = await GetSprayDamage(aiPlayer, defender, ammoToUse, abilityDmgDebuff, aiPlayer.abilities);
            score = evaluateSprayAbility(aiPlayer, defender, ammoToUse);
            break;
        }
        case "giant_killing_slingshot": {
            damage = GetGiantKillingSlingshotDamage(defender, healthyCasterTalentActive, abilityDmgDebuff, aiPlayer.abilities);
            score = evaluateGiantKillingShotshotAbility(aiPlayer, defender);
            break;
        }
        case "toxic_dart": {
            damage = GetToxicDartDamage(defender, aiPlayer.abilities);
            break;
        }
    }
    if (damage >= defender.currentHealth) {
        score *= PRIORITY_MULTIPLIERS.KILLING_BLOW;
    }
    else if (damage > defender.currentHealth * 0.4) {
        score *= PRIORITY_MULTIPLIERS.HIGH_DAMAGE_ABILITY;
    }
    return score;
};
