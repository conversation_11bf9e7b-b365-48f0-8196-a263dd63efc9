import { APIROUTES } from "@/helpers/apiRoutes";
import { handlePost } from "@/helpers/axiosInstance";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

type UnequipConsumableResponse = {
    message: string;
};

/**
 * Custom hook to unequip a consumable from a slot
 */
export const useUnequipConsumable = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (slot: string) => {
            const data = await handlePost<UnequipConsumableResponse>(APIROUTES.USER.UNEQUIPCONSUMABLE, { slot });
            return data;
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: APIROUTES.USER.EQUIPPEDCONSUMABLES });
            toast.success("Consumable unequipped");

            if (onSuccessCallback) {
                onSuccessCallback();
            }
        },
        onError: (error: Error) => {
            toast.error(error.message || "Failed to unequip consumable");
        },
    });
};

export default useUnequipConsumable;
