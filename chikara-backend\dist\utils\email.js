import { LogErrorStack } from "./log.js";
import nodemailer from "nodemailer";
const SMTP_USER = "<EMAIL>";
const SMTP_PASS = "f?tpdw4L";
const EMAIL_FROM = '"Chikara Battle Academy" <<EMAIL>>';
const transporter = nodemailer.createTransport({
    host: "smtp.zoho.eu",
    port: 465,
    secure: true,
    auth: {
        user: SMTP_USER,
        pass: SMTP_PASS,
    },
});
export const PasswordResetEmail = async (details) => {
    const { email, resetUrl, resetToken } = details;
    try {
        const resetLink = resetUrl
            ? `${resetUrl}?token=${resetToken}`
            : `https://app.battleacademy.io/passwordReset?token=${resetToken}`;
        await transporter.sendMail({
            from: EMAIL_FROM,
            to: email,
            subject: "Chikara Academy Password Reset",
            text: `Please follow this link to reset the password for your account: ${resetLink}`,
            html: `<b>Please follow this link to reset the password for your account: <a href="${resetLink}">Reset Password</a></b><br/> Token: ${resetToken}`,
        });
        return true;
    }
    catch (error) {
        LogErrorStack({ error });
        return false;
    }
};
export const RegistrationCodeEmail = async (email, code) => {
    const registerLink = `https://app.battleacademy.io/register?alphaKey=${code}`;
    await transporter.sendMail({
        from: EMAIL_FROM,
        to: email,
        subject: "Chikara Academy Alpha Test Invitation!",
        text: `Welcome to the Alpha Test!

We're thrilled to have you join the early test phase of Chikara Battle Academy. This is your chance to get an early look at the game!

Keep in mind, it's an early test version, so you might run into a few bugs. We'd love to get your feedback to help us improve. Please note that an account reset will occur after the Alpha phase.

Ready to start? Just sign up here to begin: ${registerLink}
Your Alpha Key: ${code}

Thanks for your support!

The Headmaster
https://battleacademy.io/`,
        html: `
        <div style="font-family: 'Arial', sans-serif; color: #383838; background-color: #0a0a0a; padding: 40px; border-radius: 8px; text-align: center; width: 90%; max-width: 600px; margin: auto;">
            <div style="padding: 20px; background: #9d50bb; background: linear-gradient(145deg, #6e48aa, #9d50bb); border-radius: 8px;">
                <img src="https://ik.imagekit.io/e0qbzc0rw/logo_WsWnrmBaj.png?updatedAt=*************" alt="Chikara Academy" style="max-width: 275px; height: auto; margin-bottom: 5px;">
                <h1 style="color: #ffffff; font-size: 24px; font-weight: bold; margin-bottom: 20px;">Welcome to the Alpha Test!</h1>
                <p style="color: #ffffff; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">
                    We're thrilled to have you join the early test phase of <strong>Chikara Battle Academy</strong>. Your feedback will help us enhance the game experience. Please note that an account reset will occur after the Alpha phase.
                </p>
                <a href="${registerLink}" style="background-color: #22a7f0; color: #ffffff; padding: 10px 20px; border-radius: 5px; text-decoration: none; font-weight: bold; display: inline-block; margin-bottom: 15px; font-size: 18px;">
                    Sign Up Now
                </a>
                <p style="color: #ffffff; font-size: 16px; line-height: 1.5; font-weight: bold;">Your Alpha Key: <span style="color: #ffffff; font-weight: bold;">${code}</span></p>
                <p style="color: #ffffff; font-size: 16px; line-height: 1.5; font-weight: normal; padding-top: 10px;">
                    Keep in mind, it's an early test version, so you might run into a few bugs. The content currently available does not reflect the final product.
                <footer style="color: #ffffff; font-size: 14px; line-height: 1.5; padding-top: 20px;">
                    <p style="color: #ffffff;">Thanks for your support!</p>
                    <p style="color: #ffffff;">The Headmaster</p>
                    <a href="https://discord.gg/V7RQ3XZZpg" style="color: #ffffff; text-decoration: underline; font-size: 18px;">Discord  | </a>
                    <a href="https://battleacademy.io/" style="color: #ffffff; text-decoration: underline; font-size: 18px;">Visit Our Website</a>
                </footer>
            </div>
        </div>
        `,
    });
};
export default {
    PasswordResetEmail,
    RegistrationCodeEmail,
};
