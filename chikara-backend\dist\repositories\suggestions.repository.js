import { db } from "../lib/db.js";
export const findAllSuggestions = async () => {
    return await db.suggestion.findMany({
        orderBy: { createdAt: "desc" },
        include: { user: { select: { username: true, avatar: true } } },
    });
};
export const findVoteHistoryByUserId = async (userId) => {
    return await db.suggestion_vote.findMany({
        where: { userId },
    });
};
export const findCommentsBySuggestionId = async (suggestionId) => {
    return await db.suggestion_comment.findMany({
        where: { suggestionId },
        select: { message: true, createdAt: true, userId: true, user: { select: { username: true, avatar: true } } },
        orderBy: { createdAt: "desc" },
    });
};
export const createSuggestion = async (suggestionData) => {
    return await db.suggestion.create({
        data: suggestionData,
    });
};
export const findSuggestionById = async (suggestionId) => {
    return await db.suggestion.findUnique({
        where: { id: suggestionId },
    });
};
export const createSuggestionComment = async (commentData) => {
    return await db.suggestion_comment.create({
        data: commentData,
    });
};
export const updateSuggestion = async (suggestion) => {
    return await db.suggestion.update({
        where: { id: suggestion.id },
        data: suggestion,
    });
};
export const findUserCommentsBySuggestionId = async (suggestionId, excludeUserIds) => {
    return await db.suggestion_comment.findMany({
        where: {
            suggestionId,
            userId: {
                notIn: excludeUserIds,
            },
        },
    });
};
export const findVoteByUserAndSuggestion = async (userId, suggestionId) => {
    return await db.suggestion_vote.findFirst({
        where: {
            AND: [{ userId }, { suggestionId }],
        },
    });
};
export const createVote = async (voteData) => {
    return await db.suggestion_vote.create({
        data: voteData,
    });
};
export const updateVote = async (vote) => {
    return await db.suggestion_vote.update({
        where: { id: vote.id },
        data: { voteType: vote.voteType },
    });
};
export const incrementSuggestionComments = async (suggestionId) => {
    const suggestion = await db.suggestion.findUnique({
        where: { id: suggestionId },
    });
    if (!suggestion)
        return null;
    return await db.suggestion.update({
        where: { id: suggestionId },
        data: { totalComments: { increment: 1 } },
    });
};
export const updateSuggestionVoteCounts = async (suggestionId, voteChange) => {
    const suggestion = await db.suggestion.findUnique({
        where: { id: suggestionId },
    });
    if (!suggestion)
        return null;
    let updateData = {};
    if (voteChange.type === "change") {
        if (voteChange.to === "upvote") {
            updateData = {
                downvotes: { decrement: 1 },
                upvotes: { increment: 1 },
            };
        }
        else {
            updateData = {
                upvotes: { decrement: 1 },
                downvotes: { increment: 1 },
            };
        }
    }
    else if (voteChange.type === "add") {
        if (voteChange.vote === "upvote") {
            updateData = { upvotes: { increment: 1 } };
        }
        else {
            updateData = { downvotes: { increment: 1 } };
        }
    }
    return await db.suggestion.update({
        where: { id: suggestionId },
        data: updateData,
    });
};
export const changeSuggestionState = async (suggestionId, newState) => {
    const suggestion = await db.suggestion.findUnique({
        where: { id: suggestionId },
    });
    if (!suggestion)
        return null;
    return await db.suggestion.update({
        where: { id: suggestionId },
        data: { state: newState },
    });
};
