import { beforeEach, describe, expect, it, vi } from "vitest";
import {
    generateRandomPosition,
    getStaticNodeReservedPositions,
    getNextStaticNodePosition,
    isValidGridPosition,
} from "../explore.helpers.js";
import { GRID_CONFIG, STATIC_NODE_RESERVED_POSITIONS } from "../explore.constants.js";
import * as ExploreRepository from "../../../repositories/explore.repository.js";
import type { MapNodePosition } from "../explore.types.js";

// Mock the repository
vi.mock("../../../repositories/explore.repository.js");
const mockedExploreRepository = vi.mocked(ExploreRepository);

describe("Explore Grid System", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("Grid Configuration", () => {
        it("should have correct 5x5 grid configuration", () => {
            expect(GRID_CONFIG.SIZE).toBe(5);
            expect(GRID_CONFIG.MIN_COORD).toBe(0);
            expect(GRID_CONFIG.MAX_COORD).toBe(4);
            expect(GRID_CONFIG.TOTAL_POSITIONS).toBe(25);
        });

        it("should have reserved positions for all locations", () => {
            const locations = ["shibuya", "shinjuku", "bunkyo", "chiyoda", "minato"] as const;
            
            for (const location of locations) {
                const reservedPositions = STATIC_NODE_RESERVED_POSITIONS[location];
                expect(reservedPositions).toBeDefined();
                expect(reservedPositions).toHaveLength(5);
                
                // All reserved positions should be valid grid coordinates
                for (const pos of reservedPositions) {
                    expect(pos.x).toBeGreaterThanOrEqual(GRID_CONFIG.MIN_COORD);
                    expect(pos.x).toBeLessThanOrEqual(GRID_CONFIG.MAX_COORD);
                    expect(pos.y).toBeGreaterThanOrEqual(GRID_CONFIG.MIN_COORD);
                    expect(pos.y).toBeLessThanOrEqual(GRID_CONFIG.MAX_COORD);
                }
            }
        });
    });

    describe("isValidGridPosition", () => {
        it("should validate grid positions correctly", () => {
            // Valid positions
            expect(isValidGridPosition({ x: 0, y: 0 })).toBe(true);
            expect(isValidGridPosition({ x: 4, y: 4 })).toBe(true);
            expect(isValidGridPosition({ x: 2, y: 2 })).toBe(true);

            // Invalid positions
            expect(isValidGridPosition({ x: -1, y: 0 })).toBe(false);
            expect(isValidGridPosition({ x: 0, y: -1 })).toBe(false);
            expect(isValidGridPosition({ x: 5, y: 0 })).toBe(false);
            expect(isValidGridPosition({ x: 0, y: 5 })).toBe(false);
            expect(isValidGridPosition({ x: -1, y: -1 })).toBe(false);
            expect(isValidGridPosition({ x: 5, y: 5 })).toBe(false);
        });
    });

    describe("getStaticNodeReservedPositions", () => {
        it("should return reserved positions for each location", () => {
            const shibuyaPositions = getStaticNodeReservedPositions("shibuya");
            expect(shibuyaPositions).toHaveLength(5);
            expect(shibuyaPositions).toEqual(STATIC_NODE_RESERVED_POSITIONS.shibuya);

            const shinjukuPositions = getStaticNodeReservedPositions("shinjuku");
            expect(shinjukuPositions).toHaveLength(5);
            expect(shinjukuPositions).toEqual(STATIC_NODE_RESERVED_POSITIONS.shinjuku);
        });

        it("should return a copy of the reserved positions", () => {
            const positions = getStaticNodeReservedPositions("shibuya");
            positions.push({ x: 99, y: 99 }); // Modify the returned array
            
            // Original should be unchanged
            expect(STATIC_NODE_RESERVED_POSITIONS.shibuya).toHaveLength(5);
            expect(getStaticNodeReservedPositions("shibuya")).toHaveLength(5);
        });
    });

    describe("getNextStaticNodePosition", () => {
        it("should return first available reserved position", async () => {
            // Mock no existing static nodes
            mockedExploreRepository.getStaticNodesByLocation.mockResolvedValue([]);

            const position = await getNextStaticNodePosition("shibuya");
            expect(position).toEqual(STATIC_NODE_RESERVED_POSITIONS.shibuya[0]);
        });

        it("should return next available position when some are occupied", async () => {
            // Mock one existing static node at first reserved position
            const existingNodes = [{
                id: 1,
                nodeType: "SHOP" as const,
                title: "Test Shop",
                description: "Test Description",
                position: STATIC_NODE_RESERVED_POSITIONS.shibuya[0],
                location: "shibuya" as const,
                shopId: 1,
                expiresAt: undefined,
            }];
            
            mockedExploreRepository.getStaticNodesByLocation.mockResolvedValue(existingNodes);

            const position = await getNextStaticNodePosition("shibuya");
            expect(position).toEqual(STATIC_NODE_RESERVED_POSITIONS.shibuya[1]);
        });

        it("should return null when all reserved positions are occupied", async () => {
            // Mock all reserved positions occupied
            const existingNodes = STATIC_NODE_RESERVED_POSITIONS.shibuya.map((pos, index) => ({
                id: index + 1,
                nodeType: "SHOP" as const,
                title: `Test Shop ${index + 1}`,
                description: "Test Description",
                position: pos,
                location: "shibuya" as const,
                shopId: index + 1,
                expiresAt: undefined,
            }));
            
            mockedExploreRepository.getStaticNodesByLocation.mockResolvedValue(existingNodes);

            const position = await getNextStaticNodePosition("shibuya");
            expect(position).toBeNull();
        });
    });

    describe("generateRandomPosition", () => {
        it("should place nodes with maximum spacing when grid is empty", async () => {
            // Mock empty grid
            mockedExploreRepository.getActivePlayerNodesByLocation.mockResolvedValue([]);
            mockedExploreRepository.getStaticNodesByLocation.mockResolvedValue([]);

            const position = await generateRandomPosition(1, "shibuya");

            expect(position).toBeDefined();
            expect(position?.x).toBeGreaterThanOrEqual(0);
            expect(position?.x).toBeLessThanOrEqual(4);
            expect(position?.y).toBeGreaterThanOrEqual(0);
            expect(position?.y).toBeLessThanOrEqual(4);
        });

        it("should avoid reserved positions for static nodes", async () => {
            // Mock empty grid
            mockedExploreRepository.getActivePlayerNodesByLocation.mockResolvedValue([]);
            mockedExploreRepository.getStaticNodesByLocation.mockResolvedValue([]);

            const position = await generateRandomPosition(1, "shibuya");
            
            // Position should not be in reserved positions for shibuya
            const reservedPositions = STATIC_NODE_RESERVED_POSITIONS.shibuya;
            const isReserved = reservedPositions.some(
                reserved => reserved.x === position?.x && reserved.y === position?.y
            );
            expect(isReserved).toBe(false);
        });

        it("should maximize distance from existing nodes", async () => {
            // Mock one existing node at position (1, 1)
            const existingPlayerNodes = [{
                id: 1,
                nodeType: "BATTLE" as const,
                title: "Test Battle",
                description: "Test Description",
                position: { x: 1, y: 1 },
                status: "available" as const,
                location: "shibuya" as const,
                expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
            }];
            
            mockedExploreRepository.getActivePlayerNodesByLocation.mockResolvedValue(existingPlayerNodes);
            mockedExploreRepository.getStaticNodesByLocation.mockResolvedValue([]);

            const position = await generateRandomPosition(1, "shibuya");

            // The algorithm should place the new node as far as possible from (1, 1)
            // In a 5x5 grid, the farthest positions from (1, 1) are the corners
            // But some corners might be reserved, so we check for reasonable spacing
            expect(position).toBeDefined();
            if (position) {
                const distance = Math.abs(position.x - 1) + Math.abs(position.y - 1);
                expect(distance).toBeGreaterThanOrEqual(3); // Should be reasonably far
            }
        });

        it("should return null when no positions are available", async () => {
            // Mock a full grid scenario
            const allPositions: MapNodePosition[] = [];
            for (let x = 0; x <= 4; x++) {
                for (let y = 0; y <= 4; y++) {
                    allPositions.push({ x, y });
                }
            }

            // Create mock nodes for all positions
            const existingPlayerNodes = allPositions.slice(0, 15).map((pos, index) => ({
                id: index + 1,
                nodeType: "BATTLE" as const,
                title: `Test Battle ${index + 1}`,
                description: "Test Description",
                position: pos,
                status: "available" as const,
                location: "shibuya" as const,
                expiresAt: new Date(Date.now() + 3600000),
            }));

            const existingStaticNodes = allPositions.slice(15, 25).map((pos, index) => ({
                id: index + 16,
                nodeType: "SHOP" as const,
                title: `Test Shop ${index + 1}`,
                description: "Test Description",
                position: pos,
                location: "shibuya" as const,
                shopId: index + 1,
                expiresAt: undefined,
            }));

            mockedExploreRepository.getActivePlayerNodesByLocation.mockResolvedValue(existingPlayerNodes);
            mockedExploreRepository.getStaticNodesByLocation.mockResolvedValue(existingStaticNodes);

            const position = await generateRandomPosition(1, "shibuya");
            expect(position).toBeNull();
        });

        it("should handle different locations correctly", async () => {
            // Mock empty grid for different locations
            mockedExploreRepository.getActivePlayerNodesByLocation.mockResolvedValue([]);
            mockedExploreRepository.getStaticNodesByLocation.mockResolvedValue([]);

            const locations = ["shibuya", "shinjuku", "bunkyo", "chiyoda", "minato"] as const;
            
            for (const location of locations) {
                const position = await generateRandomPosition(1, location);
                expect(position).toBeDefined();
                expect(isValidGridPosition(position!)).toBe(true);
                
                // Should not be in reserved positions for this location
                const reservedPositions = STATIC_NODE_RESERVED_POSITIONS[location];
                const isReserved = reservedPositions.some(
                    reserved => reserved.x === position?.x && reserved.y === position?.y
                );
                expect(isReserved).toBe(false);
            }
        });
    });
});
