import { db } from "../lib/db.js";
import { QuestProgressStatus } from "@prisma/client";
export const getUserQuestProgress = async (userId, questId) => {
    return await db.quest_progress.findFirst({
        where: { userId, questId },
    });
};
export const getCreatureById = async (targetId) => {
    return await db.creature.findUnique({
        where: { id: targetId },
        select: {
            id: true,
            name: true,
            boss: true,
            image: true,
        },
    });
};
export const getCompleteQuestList = async () => {
    return await db.quest.findMany({
        include: {
            quest_objective: true,
        },
    });
};
export const getAvailableQuestsForUser = async (userId, userLevel) => {
    return await db.quest.findMany({
        where: {
            levelReq: { lte: userLevel },
            disabled: false,
            NOT: {
                quest_progress: {
                    some: {
                        userId: userId,
                        questStatus: {
                            in: [
                                QuestProgressStatus.in_progress,
                                QuestProgressStatus.complete,
                                QuestProgressStatus.ready_to_complete,
                            ],
                        },
                    },
                },
            },
            OR: [
                { requiredQuestId: null },
                {
                    requiredQuestId: {
                        not: null,
                    },
                    quest: {
                        quest_progress: {
                            some: {
                                userId: userId,
                                questStatus: QuestProgressStatus.complete,
                            },
                        },
                    },
                },
            ],
        },
        include: {
            quest_reward: {
                include: {
                    item: true,
                },
            },
            quest_objective: {
                include: {
                    creature: true,
                    item: true,
                    quest_objective_progress: {
                        where: {
                            userId: userId,
                        },
                    },
                },
            },
            quest_progress: {
                where: {
                    userId: userId,
                },
            },
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
        },
    });
};
export const getActiveQuestsForUser = async (userId, userLevel) => {
    return await db.quest.findMany({
        where: {
            levelReq: { lte: userLevel },
            disabled: false,
            quest_progress: {
                some: {
                    userId: userId,
                    questStatus: {
                        in: [QuestProgressStatus.in_progress, QuestProgressStatus.ready_to_complete],
                    },
                },
            },
            OR: [
                { requiredQuestId: null },
                {
                    requiredQuestId: {
                        not: null,
                    },
                    quest: {
                        quest_progress: {
                            some: {
                                userId: userId,
                                questStatus: QuestProgressStatus.complete,
                            },
                        },
                    },
                },
            ],
        },
        include: {
            quest_reward: {
                include: {
                    item: true,
                },
            },
            quest_objective: {
                include: {
                    creature: true,
                    item: true,
                    quest_objective_progress: {
                        where: {
                            userId: userId,
                        },
                    },
                },
            },
            quest_progress: {
                where: {
                    userId: userId,
                },
            },
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
        },
    });
};
export const getCompletedQuestsForUser = async (userId) => {
    return await db.quest.findMany({
        where: {
            quest_progress: {
                some: {
                    userId: userId,
                    questStatus: QuestProgressStatus.complete,
                },
            },
        },
        include: {
            quest_reward: {
                include: {
                    item: true,
                },
            },
            quest_objective: {
                include: {
                    creature: true,
                    item: true,
                    quest_objective_progress: {
                        where: {
                            userId: userId,
                        },
                    },
                },
            },
            quest_progress: {
                where: {
                    userId: userId,
                },
            },
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
        },
    });
};
export const getUserQuestProgressList = async (userId, activeOnly) => {
    const filter = { userId };
    if (activeOnly) {
        filter.questStatus = QuestProgressStatus.in_progress;
    }
    return await db.quest_progress.findMany({
        where: filter,
        orderBy: { updatedAt: "desc" },
        include: {
            quest: {
                include: {
                    quest_objective: true,
                },
            },
        },
    });
};
export const getQuestsWithUserProgress = async (user) => {
    const { id: userId } = user;
    return await db.quest.findMany({
        where: {
            levelReq: {
                lte: user.level,
            },
            disabled: false,
        },
        include: {
            quest_progress: {
                where: {
                    userId: userId,
                },
            },
            quest_reward: {
                include: {
                    item: true,
                },
            },
            quest_objective: {
                include: {
                    quest_objective_progress: {
                        where: {
                            userId: userId,
                        },
                    },
                },
            },
        },
    });
};
export const getFullQuestDetails = async (userId, userLevel, onlyStoryQuests = false) => {
    return await db.quest.findMany({
        where: {
            levelReq: { lte: userLevel },
            disabled: { not: true },
            isStoryQuest: onlyStoryQuests ? true : undefined,
            NOT: {
                quest_progress: {
                    some: {
                        userId: userId,
                        questStatus: QuestProgressStatus.complete,
                    },
                },
            },
            OR: [
                { requiredQuestId: null },
                {
                    requiredQuestId: {
                        not: null,
                    },
                    quest: {
                        quest_progress: {
                            some: {
                                userId: userId,
                                questStatus: QuestProgressStatus.complete,
                            },
                        },
                    },
                },
            ],
        },
        include: {
            quest_reward: {
                include: {
                    item: true,
                },
            },
            quest_objective: {
                include: {
                    creature: true,
                    item: true,
                    quest_objective_progress: {
                        where: {
                            userId: userId,
                        },
                    },
                },
            },
            quest_progress: {
                where: {
                    userId: userId,
                },
            },
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
        },
        orderBy: {
            levelReq: "asc",
        },
    });
};
export const getQuestById = async (questId) => {
    return await db.quest.findUnique({
        where: { id: questId },
        include: {
            quest_reward: {
                include: {
                    item: true,
                },
            },
            quest_objective: true,
        },
    });
};
export const createQuestProgress = async (userId, questId, questStatus) => {
    return await db.quest_progress.create({
        data: {
            user: { connect: { id: userId } },
            quest: { connect: { id: questId } },
            questStatus,
        },
    });
};
export const getQuestByName = async (name) => {
    return await db.quest.findFirst({
        where: { name },
        include: {
            quest_objective: true,
        },
    });
};
export const updateQuestProgress = async (id, updateData) => {
    return await db.quest_progress.update({
        where: { id },
        data: updateData,
    });
};
export const updateQuestObjectiveProgress = async (id, updateData) => {
    return await db.quest_objective_progress.update({
        where: { id },
        data: updateData,
    });
};
export const createQuestObjectiveProgress = async (userId, questObjectiveId, count, status) => {
    return await db.quest_objective_progress.create({
        data: {
            userId,
            questObjectiveId,
            count,
            status,
        },
    });
};
export const findQuestProgressWithQuest = async (userId, activeOnly = false) => {
    const filter = { userId };
    if (activeOnly) {
        filter.questStatus = QuestProgressStatus.in_progress;
    }
    return await db.quest_progress.findMany({
        where: filter,
        orderBy: { updatedAt: "desc" },
        include: {
            quest: {
                include: {
                    quest_objective: { include: { quest_objective_progress: true } },
                },
            },
        },
    });
};
export const findFullUserQuestProgress = async (userId) => {
    const questProgress = await db.quest_progress.findMany({
        where: { userId },
        orderBy: { updatedAt: "desc" },
    });
    const allObjectiveProgress = await db.quest_objective_progress.findMany({
        where: { userId },
        include: { quest_objective: { select: { questId: true } } },
    });
    const fullProgress = questProgress.map((progress) => {
        const questId = progress.questId;
        const questObjectiveProgress = allObjectiveProgress.filter((p) => p.quest_objective.questId === questId);
        return {
            ...progress,
            quest_objective_progress: questObjectiveProgress,
        };
    });
    return fullProgress;
};
export const findQuestObjectivesByType = async (questType) => {
    return await db.quest_objective.findMany({
        where: {
            objectiveType: questType,
        },
        include: {
            quest: true,
        },
    });
};
export const findQuestObjectiveProgressForUser = async (userId, questObjectiveId) => {
    return await db.quest_objective_progress.findFirst({
        where: {
            userId,
            questObjectiveId,
        },
    });
};
export const findUserQuestObjectiveProgress = async (userId, where) => {
    return await db.quest_objective_progress.findMany({
        where: {
            userId,
            status: QuestProgressStatus.in_progress,
            quest_objective: where,
        },
        include: {
            quest_objective: true,
        },
    });
};
export const findRequiredUserQuestObjectiveProgressForQuest = async (userId, questId) => {
    return await db.quest_objective_progress.findMany({
        where: {
            userId,
            quest_objective: { questId: questId, isRequired: true },
        },
        include: {
            quest_objective: true,
        },
    });
};
export const findTraderRep = async (userId, shopId) => {
    return await db.trader_rep.findFirst({
        where: { userId, shopId },
    });
};
export const createTraderRep = async (userId, shopId, rep) => {
    return await db.trader_rep.create({
        data: {
            userId,
            shopId,
            reputationLevel: rep,
        },
    });
};
export const saveTraderRep = async (traderRep) => {
    return await db.trader_rep.update({
        where: { id: traderRep.id },
        data: { reputationLevel: traderRep.reputationLevel },
    });
};
export const getQuestObjectiveProgressById = async (id) => {
    return await db.quest_objective_progress.findUnique({
        where: { id },
    });
};
export const getAllQuestsWithDetails = async () => {
    return await db.quest.findMany({
        include: {
            quest_objective: {
                include: {
                    creature: true,
                    item: true,
                },
            },
            quest_reward: {
                include: {
                    item: true,
                },
            },
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
            shop: true,
            quest: true,
        },
        orderBy: [{ chapterId: "asc" }, { orderInChapter: "asc" }, { id: "asc" }],
    });
};
export const createQuest = async (questData) => {
    return await db.quest.create({
        data: questData,
        include: {
            quest_objective: true,
            quest_reward: true,
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
        },
    });
};
export const updateQuest = async (questId, questData) => {
    return await db.quest.update({
        where: { id: questId },
        data: questData,
        include: {
            quest_objective: true,
            quest_reward: true,
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
        },
    });
};
export const deleteQuest = async (questId) => {
    return await db.quest.delete({
        where: { id: questId },
    });
};
export const reorderQuestsInChapter = async (chapterId, questIds) => {
    if (!questIds || questIds.length === 0) {
        throw new Error("Quest IDs array cannot be empty");
    }
    return await db.$transaction(async (tx) => {
        const questsInChapter = await tx.quest.findMany({
            where: {
                id: { in: questIds },
                chapterId: chapterId,
            },
            select: { id: true },
        });
        if (questsInChapter.length !== questIds.length) {
            throw new Error("Some quests do not belong to the specified chapter");
        }
        for (const [i, questId] of questIds.entries()) {
            await tx.quest.update({
                where: {
                    id: questId,
                    chapterId: chapterId,
                },
                data: { orderInChapter: -(i + 1) },
            });
        }
        for (const [i, questId] of questIds.entries()) {
            await tx.quest.update({
                where: {
                    id: questId,
                    chapterId: chapterId,
                },
                data: { orderInChapter: i + 1 },
            });
        }
    });
};
export const getAllObjectivesWithDetails = async () => {
    return await db.quest_objective.findMany({
        include: {
            quest: {
                include: {
                    story_chapter: {
                        include: {
                            story_season: true,
                        },
                    },
                },
            },
            creature: true,
            item: true,
        },
        orderBy: [{ questId: "asc" }],
    });
};
export const createObjective = async (objectiveData) => {
    return await db.quest_objective.create({
        data: objectiveData,
        include: {
            quest: true,
            creature: true,
            item: true,
        },
    });
};
export const updateObjective = async (objectiveId, objectiveData) => {
    return await db.quest_objective.update({
        where: { id: objectiveId },
        data: objectiveData,
        include: {
            quest: true,
            creature: true,
            item: true,
        },
    });
};
export const deleteObjective = async (objectiveId) => {
    return await db.quest_objective.delete({
        where: { id: objectiveId },
    });
};
export const getQuestObjectiveById = async (id) => {
    return await db.quest_objective.findUnique({
        where: { id },
        include: {
            item: true,
        },
    });
};
export const getNextStoryQuestInChapter = async (chapterId, currentOrder) => {
    return await db.quest.findFirst({
        where: {
            chapterId,
            orderInChapter: currentOrder + 1,
            isStoryQuest: true,
        },
        include: {
            quest_objective: true,
        },
    });
};
