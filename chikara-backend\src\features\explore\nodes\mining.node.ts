import type { ExploreNodeLocation } from "@prisma/client";
import * as InventoryService from "../../../core/inventory.service.js";
import * as NotificationService from "../../../core/notification.service.js";
import * as StatusEffectService from "../../../core/statuseffect.service.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import { logAction } from "../../../lib/actionLogger.js";
import type { ItemModel, StatusEffectModel, UserModel } from "../../../lib/db.js";
import { db } from "../../../lib/db.js";
import { NotificationTypes } from "../../../types/notification.js";
import { mapExploreLocationToLocationTypes } from "../explore.helpers.js";
import * as ExploreRepository from "../../../repositories/explore.repository.js";

/**
 * Mining node configuration
 */
export const MINING_CONFIG = {
    FAIL_CHANCE: 0.25, // 25% chance of bad outcome
    INJURY_CHANCE: 0.7, // 70% chance to get an injury on bad outcome / 30% chance to lose energy
    SUCCESS_BONUS_CHANCE: 0.2, // 20% chance to get bonus ore on success
    DEFAULT_ENERGY_COST: 3, // Energy cost for mining attempt
    MINING_TIMEOUT_MS: 2 * 60 * 1000, // 2 minutes to complete mining
    MINING_TYPES: ["ore", "gems", "crystals", "rare_metals"] as const,
    INJURY_TYPES: ["cave_in", "equipment_failure", "exhaustion"] as const,
} as const;

/**
 * Interface for mining result data
 */
export interface MiningResult {
    success: boolean;
    miningType: string;
    itemReward?: Omit<ItemModel, "createdAt" | "updatedAt">;
    itemQuantity?: number;
    bonusReward?: Omit<ItemModel, "createdAt" | "updatedAt">;
    bonusQuantity?: number;
    injury?: StatusEffectModel;
    experienceGained?: number;
    message?: string;
}

/**
 * Interface for mining operation data
 */
export interface MiningOperation {
    miningType: string;
    miningValidUntil: number;
    difficulty: "easy" | "medium" | "hard";
    energyCost: number;
}

/**
 * Get potential ore drops for mining in explore mode
 */
export const findExploreMiningDrops = async (userLevel: number, location: ExploreNodeLocation, miningType: string) => {
    const mappedLocation = mapExploreLocationToLocationTypes(location);

    return await db.drop_chance.findMany({
        where: {
            dropChanceType: "scavenge", // Use scavenge type for mining drops until mining type is added
            scavengeType: miningType, // Reusing scavengeType field for mining type
            location: {
                in: [mappedLocation, "any"],
            },
            minLevel: {
                lte: userLevel,
            },
            maxLevel: {
                gte: userLevel,
            },
            dropRate: {
                gt: 0,
            },
        },
        include: {
            item: true,
        },
        orderBy: {
            dropRate: "asc",
        },
    });
};

/**
 * Handle successful mining outcome - gives ore/gems
 */
export const handleMiningSuccess = async (
    user: UserModel,
    location: ExploreNodeLocation,
    miningType: string,
    difficulty: "easy" | "medium" | "hard"
) => {
    const potentialDrops = await findExploreMiningDrops(user.level, location, miningType);

    if (!potentialDrops || potentialDrops.length === 0) {
        return null;
    }

    // Pick a random item from the potential drops
    const randomIndex: number = Math.floor(Math.random() * potentialDrops.length);
    const selectedItem = potentialDrops[randomIndex];

    // Check if itemId and item are not null
    if (selectedItem.itemId === null || !selectedItem.item) {
        return null;
    }

    // Calculate quantity based on difficulty
    const difficultyMultiplier = difficulty === "easy" ? 1 : difficulty === "medium" ? 1.5 : 2;
    const baseQuantity = selectedItem.quantity;
    const finalQuantity = Math.max(1, Math.floor(baseQuantity * difficultyMultiplier));

    // Add the item to the user's inventory
    await InventoryService.AddItemToUser({
        userId: user.id,
        itemId: selectedItem.itemId,
        amount: finalQuantity,
        isTradeable: true,
    });

    // Calculate experience gained based on difficulty and item rarity
    const baseExp = 15;
    const diffExp = difficulty === "easy" ? 1 : difficulty === "medium" ? 1.5 : 2;
    const experienceGained = Math.floor(baseExp * diffExp);

    // Check for bonus reward
    let bonusResult = null;
    if (Math.random() <= MINING_CONFIG.SUCCESS_BONUS_CHANCE) {
        // 20% chance for bonus ore
        const bonusItem = potentialDrops[Math.floor(Math.random() * potentialDrops.length)];
        if (bonusItem.itemId && bonusItem.item) {
            const bonusQuantity = Math.max(1, Math.floor(bonusItem.quantity * 0.5));
            await InventoryService.AddItemToUser({
                userId: user.id,
                itemId: bonusItem.itemId,
                amount: bonusQuantity,
                isTradeable: true,
            });

            bonusResult = {
                bonusReward: bonusItem.item,
                bonusQuantity,
            };
        }
    }

    logAction({
        action: "EXPLORE_MINING_SUCCESS",
        userId: user.id,
        info: {
            itemId: selectedItem.item.id,
            itemName: selectedItem.item.name,
            quantity: finalQuantity,
            location: location,
            miningType: miningType,
            difficulty: difficulty,
            experienceGained,
            bonusReward: bonusResult?.bonusReward?.name || null,
        },
    });

    return {
        itemReward: selectedItem.item,
        itemQuantity: finalQuantity,
        experienceGained,
        ...bonusResult,
    };
};

/**
 * Handle failed mining outcome - applies injury
 */
export const handleMiningFailure = async (
    currentUser: UserModel,
    location: ExploreNodeLocation,
    miningType: string,
    difficulty: "easy" | "medium" | "hard"
) => {
    // Apply mining-related injury
    const injuryTypes = ["Physical", "Mental"] as const;
    const injuryType = injuryTypes[Math.floor(Math.random() * injuryTypes.length)];

    const injury = await StatusEffectService.GetRandomInjury("Minor", injuryType);
    if (injury) {
        await StatusEffectService.ApplyStatusEffectToUser(currentUser, injury);

        NotificationService.NotifyUser(
            currentUser.id,
            NotificationTypes.injured,
            {
                reason: "mining accident",
                injury: injury.name,
                injuryTier: injury.tier,
            },
            true
        );

        logAction({
            action: "EXPLORE_MINING_INJURY",
            userId: currentUser.id,
            info: {
                injury: injury.name,
                injuryTier: injury.tier,
                location: location,
                miningType: miningType,
                difficulty: difficulty,
            },
        });

        return injury;
    }

    return null;
};

/**
 * Determine mining difficulty based on location and user level
 */
export const determineMiningDifficulty = (
    location: ExploreNodeLocation,
    userLevel: number
): "easy" | "medium" | "hard" => {
    // Base difficulty on location characteristics
    const locationDifficulty = {
        shibuya: "easy",
        shinjuku: "medium",
        bunkyo: "easy",
        chiyoda: "hard",
        minato: "medium",
    } as const;

    const baseDifficulty = locationDifficulty[location] || "easy";

    // Adjust based on user level
    if (userLevel >= 30) {
        return baseDifficulty === "easy" ? "medium" : baseDifficulty === "medium" ? "hard" : "hard";
    } else if (userLevel >= 15) {
        return baseDifficulty === "easy" ? "easy" : baseDifficulty === "medium" ? "medium" : "medium";
    }
    return "easy";
};

/**
 * Select random mining type based on location
 */
export const selectMiningType = (location: ExploreNodeLocation): string => {
    const locationMiningTypes = {
        shibuya: ["ore", "crystals"],
        shinjuku: ["ore", "rare_metals"],
        bunkyo: ["gems", "crystals"],
        chiyoda: ["rare_metals", "gems"],
        minato: ["ore", "gems"],
    } as const;

    const availableTypes = locationMiningTypes[location] || ["ore"];
    return availableTypes[Math.floor(Math.random() * availableTypes.length)];
};

/**
 * Initialize mining operation
 */
export const initializeMining = async (userId: number, location: ExploreNodeLocation) => {
    const user = await UserRepository.getUserById(userId);
    if (!user) {
        throw new Error("User not found");
    }

    const miningType = selectMiningType(location);
    const difficulty = determineMiningDifficulty(location, user.level);
    const energyCost =
        MINING_CONFIG.DEFAULT_ENERGY_COST * (difficulty === "easy" ? 1 : difficulty === "medium" ? 1.5 : 2);
    const miningValidUntil = Date.now() + MINING_CONFIG.MINING_TIMEOUT_MS;

    logAction({
        action: "EXPLORE_MINING_INITIATED",
        userId: userId,
        info: {
            location: location,
            miningType: miningType,
            difficulty: difficulty,
            energyCost: energyCost,
        },
    });

    return {
        miningType,
        difficulty,
        energyCost,
        miningValidUntil,
    };
};

/**
 * Process mining operation
 */
export const processMining = async (
    userId: number,
    nodeId: number,
    location: ExploreNodeLocation,
    miningType: string,
    difficulty: "easy" | "medium" | "hard"
) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return {
            success: false,
            message: "User not found",
        };
    }

    const goodOutcome: boolean = Math.random() > MINING_CONFIG.FAIL_CHANCE;
    const result: MiningResult = {
        success: goodOutcome,
        miningType,
    };

    if (goodOutcome) {
        // Success - get ore/gem reward
        const miningResult = await handleMiningSuccess(currentUser, location, miningType, difficulty);
        if (miningResult) {
            result.itemReward = miningResult.itemReward;
            result.itemQuantity = miningResult.itemQuantity;
            result.experienceGained = miningResult.experienceGained;
            result.bonusReward = miningResult.bonusReward;
            result.bonusQuantity = miningResult.bonusQuantity;

            if (result.bonusReward) {
                result.message = `You successfully mined ${result.itemQuantity} ${result.itemReward?.name} and found a bonus of ${result.bonusQuantity} ${result.bonusReward.name}!`;
            } else {
                result.message = `You successfully mined ${result.itemQuantity} ${result.itemReward?.name}!`;
            }
        } else {
            result.message = "You mined the area thoroughly but didn't find any valuable materials.";
        }
    } else {
        // Failure - get injury
        const failureResult = await handleMiningFailure(currentUser, location, miningType, difficulty);

        result.injury = failureResult ?? undefined;

        if (failureResult) {
            result.message = "A mining accident occurred! You've been injured in the process.";
        } else {
            result.message = "You mined the area thoroughly but didn't find any valuable materials.";
        }
    }

    return {
        success: true,
        message: "Mining operation completed",
        data: result,
    };
};

/**
 * Process mining operation when user clicks "Mine Ore" button
 */
export const processMiningOperation = async (
    userId: number,
    nodeId: number,
    location: ExploreNodeLocation,
    miningType: string,
    difficulty: "easy" | "medium" | "hard"
) => {
    return await processMining(userId, nodeId, location, miningType, difficulty);
};

/**
 * Main handler for mining encounters in explore nodes
 * This handles the initial interaction with a mining node
 */
export const handleMiningEncounter = async (userId: number, nodeId: number, location: ExploreNodeLocation) => {
    try {
        // Initialize the mining encounter
        const miningOperation = await initializeMining(userId, location);

        // Update the node metadata with the mining operation details and put it in "current" status
        const metadataUpdated = await ExploreRepository.updatePlayerNodeMetadata(nodeId, userId, {
            miningType: miningOperation.miningType,
            difficulty: miningOperation.difficulty,
            energyCost: miningOperation.energyCost,
            miningValidUntil: miningOperation.miningValidUntil,
        });

        if (!metadataUpdated) {
            return {
                success: false,
                message: "Failed to initialize mining operation",
            };
        }

        return {
            success: true,
            message: "Mining site prepared. Click 'Mine Ore' to begin extraction.",
            data: {
                action: "mining_ready",
                nodeId,
                miningType: miningOperation.miningType,
                difficulty: miningOperation.difficulty,
                energyCost: miningOperation.energyCost,
                estimatedReward:
                    miningOperation.difficulty === "easy"
                        ? "low"
                        : miningOperation.difficulty === "medium"
                          ? "medium"
                          : "high",
            },
        };
    } catch (error) {
        logAction({
            action: "EXPLORE_MINING_ERROR",
            userId: userId,
            info: {
                nodeId,
                location,
                error: error instanceof Error ? error.message : "Unknown error",
            },
        });

        return {
            success: false,
            message: "Failed to prepare mining site",
        };
    }
};
