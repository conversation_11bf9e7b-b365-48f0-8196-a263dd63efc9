import { QuestObjectiveTypes } from "../types/quest.js";
import { QuestProgressStatus } from "@prisma/client";
import { LogErrorStack, logger } from "../utils/log.js";
import { getToday } from "../utils/dateHelpers.js";
import * as QuestRepository from "../repositories/quest.repository.js";
import * as DailyQuestRepository from "../repositories/dailyquest.repository.js";
import * as QuestHelpers from "../features/quest/quest.helpers.js";
import { updateDailyQuest } from "../features/dailyquest/dailyquest.helpers.js";
import * as QuestController from "../features/quest/quest.controller.js";
export const handleQuestObjective = async (userId, criteria, amount = 1) => {
    try {
        await handleRegularQuestObjective(userId, criteria, amount);
        await handleDailyQuestObjective(userId, criteria, amount);
    }
    catch (error) {
        LogErrorStack({
            message: `Error handling quest objective ${criteria.objectiveType} for user ${userId}`,
            error,
        });
    }
};
export const handleRegularQuestObjective = async (userId, criteria, amount = 1) => {
    const objectiveProgress = await QuestRepository.findUserQuestObjectiveProgress(userId, criteria);
    if (!objectiveProgress) {
        return;
    }
    await QuestHelpers.updateQuestObjectiveCount(objectiveProgress, amount);
};
export const handleDailyQuestObjective = async (userId, criteria, amount = 1) => {
    const today = getToday();
    const questProgress = await DailyQuestRepository.findDailyQuestProgress(userId, criteria.objectiveType, today, typeof criteria.target === "object" ? null : criteria.target, typeof criteria.targetAction === "string" ? criteria.targetAction : null);
    if (questProgress) {
        await updateDailyQuest(questProgress, amount);
    }
};
export const handleDefeatNPC = async (userId, creatureId, location) => {
    if (creatureId !== null) {
        await handleQuestObjective(userId, {
            objectiveType: QuestObjectiveTypes.DEFEAT_NPC,
            target: creatureId,
            location,
        });
    }
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_NPC,
        target: null,
        location,
    });
};
export const handleDefeatNPCInTurns = async (userId, turns) => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_NPC_IN_TURNS,
        target: turns,
    });
};
export const handleDefeatNPCWithLowDamage = async (userId, percentDmgTaken) => {
    if (percentDmgTaken === null || percentDmgTaken === undefined) {
        return;
    }
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_NPC_WITH_LOW_DAMAGE,
        target: { gte: percentDmgTaken },
    });
};
export const handlePvPPostBattleChoice = async (userId, targetId, postBattleAction) => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.PVP_POST_BATTLE_CHOICE,
        targetAction: postBattleAction,
    });
};
export const handlePvPKill = async (userId, targetLevel) => {
    if (targetLevel === null || targetLevel === undefined) {
        return;
    }
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_PLAYER,
        target: { lte: targetLevel },
    });
};
export const handleBountyPlaced = async (userId, bountyAmount) => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.PLACE_BOUNTY,
        target: { lte: bountyAmount },
    });
};
export const handleCraftItem = async (userId, itemId, amount = 1) => {
    if (itemId !== null) {
        await handleQuestObjective(userId, {
            objectiveType: QuestObjectiveTypes.CRAFT_ITEM,
            target: itemId,
        }, amount);
    }
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.CRAFT_ITEM,
        target: null,
    }, amount);
};
export const handleSuggestionVote = async (userId, suggestionId, amount = 1) => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.VOTE_ON_SUGGESTION,
    }, amount);
};
export const handleCharacterEncounter = async (userId, encounterId, location, amount = 1) => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.CHARACTER_ENCOUNTERS,
        location,
    }, amount);
};
export const handleMissionComplete = async (userId) => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.COMPLETE_MISSIONS,
    });
};
export const handleShrineDonation = async (userId, amount) => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DONATE_TO_SHRINE,
    }, amount);
};
export const handleStatsTraining = async (userId, amount) => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.TRAIN_STATS,
    }, amount);
};
export const handleGambling = async (userId, gameType, amount) => {
    if (gameType === "slots" && amount) {
        await handleQuestObjective(userId, {
            objectiveType: QuestObjectiveTypes.GAMBLING_SLOTS,
        }, amount);
    }
};
export const handleFetchItem = async (userId, itemId, amount = 1) => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.ACQUIRE_ITEM,
        target: itemId,
    }, amount);
};
export const handleCompleteStoryEpisode = async (userId, location) => {
    await handleQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.COMPLETE_STORY_EPISODE,
        location,
    }, 1);
};
export const handleSpecificObjective = async (userId, objectiveId) => {
    try {
        const objectiveProgressList = await QuestRepository.findUserQuestObjectiveProgress(userId, {
            id: objectiveId,
        });
        if (!objectiveProgressList || objectiveProgressList.length === 0) {
            logger.warn(`No objective progress found for user ${userId} and objective ${objectiveId}`);
            return;
        }
        await QuestHelpers.updateQuestObjectiveCount(objectiveProgressList[0], 1);
        logger.info(`Completed specific objective ${objectiveId} for user ${userId}`);
    }
    catch (error) {
        LogErrorStack({
            message: `Error completing specific objective ${objectiveId} for user ${userId}`,
            error,
        });
    }
};
export const handleDefeatBoss = async (userId) => {
    await handleDailyQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_BOSS,
    });
};
export const handleWinBattle = async (userId) => {
    await handleDailyQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.WIN_BATTLE,
    });
};
export const handleBountyCollection = async (userId) => {
    await handleDailyQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.COLLECT_BOUNTY_REWARD,
    });
};
export const handleDefeatSpecificPlayer = async (userId, targetId) => {
    await handleDailyQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_SPECIFIC_PLAYER,
        target: targetId,
    });
};
export const handleDefeatPlayerByUsername = async (userId, targetUsername) => {
    await handleDailyQuestObjective(userId, {
        objectiveType: QuestObjectiveTypes.DEFEAT_PLAYER_XNAME,
        targetAction: targetUsername,
    });
};
export const handleStoryChoiceMade = (userId, choiceValue) => {
    const _placeholder = { userId, choiceValue };
};
export const hasUserCompletedBannerQuest = async () => {
    return true;
};
export const completeStoryQuestObjective = async (userId, objectiveId, episodeId) => {
    const objective = await QuestRepository.getQuestObjectiveById(objectiveId);
    if (objective && objective.objectiveType === QuestObjectiveTypes.COMPLETE_STORY_EPISODE) {
        await handleSpecificObjective(userId, objectiveId);
        logger.info(`Completed specific COMPLETE_STORY_EPISODE objective ${objectiveId} for episode ${episodeId} at location ${objective.location}`);
        if (objective.questId) {
            await autoCompleteStoryQuest(objective.questId, userId, episodeId);
        }
    }
};
export const autoCompleteStoryQuest = async (questId, userId, episodeId) => {
    const quest = await QuestRepository.getQuestById(questId);
    if (quest && quest.isStoryQuest) {
        const questProgress = await QuestRepository.getUserQuestProgress(userId, quest.id);
        if (questProgress && questProgress.questStatus === QuestProgressStatus.ready_to_complete) {
            const completionResult = await QuestController.CompleteQuest(userId, quest.id);
            if (completionResult.data) {
                logger.info(`Auto-completed story quest ${quest.id} (${quest.name}) after completing episode ${episodeId}`);
            }
            else {
                logger.warn(`Failed to auto-complete story quest ${quest.id} after episode ${episodeId}: ${completionResult.error}`);
            }
        }
    }
};
