import * as TalentHelper from "../../talents/talents.helpers.js";
import { ItemTypes } from "@prisma/client";
import { ATTACK_TYPE_MELEE, ATTACK_TYPE_RANGED } from "./battle.constants.js";
export const FIST_DAMAGE = 1;
export const DEFAULT_FIST_WEAPON = { damage: FIST_DAMAGE, itemType: "weapon" };
export const getEquippedItemDetails = (combatant, item) => {
    if (combatant.equipment && typeof combatant.equipment === "object" && item in combatant.equipment) {
        return combatant.equipment[item];
    }
    return null;
};
export const GetTargetAttackType = (target) => {
    let enemyAttackType = ATTACK_TYPE_MELEE;
    const weapon = getEquippedItemDetails(target, "weapon");
    const ranged = getEquippedItemDetails(target, "ranged");
    if (target.userType === "player" && ranged && target.ammo > 0) {
        if (target.attributes.dexterity > target.attributes.strength) {
            enemyAttackType = ATTACK_TYPE_RANGED;
            target.ammo -= 1;
        }
        else if (!weapon) {
            enemyAttackType = ATTACK_TYPE_RANGED;
            target.ammo -= 1;
        }
    }
    return enemyAttackType;
};
export const GetWeaponForCombatant = (combatant, isMeleeAttack) => {
    if (combatant.userType !== "player") {
        return {
            damage: combatant.weaponDamage || DEFAULT_FIST_WEAPON.damage,
            itemType: ItemTypes.weapon,
        };
    }
    const weapon = getEquippedItemDetails(combatant, "weapon");
    const ranged = getEquippedItemDetails(combatant, "ranged");
    if (isMeleeAttack && weapon) {
        return weapon;
    }
    if (!isMeleeAttack && ranged) {
        return ranged;
    }
    return DEFAULT_FIST_WEAPON;
};
export const GetOffhandWeaponForCombatant = (combatant, isMeleeAttack) => {
    if (combatant.userType !== "player")
        return { damage: 0 };
    const offhand = getEquippedItemDetails(combatant, "offhand");
    if (isMeleeAttack && offhand) {
        if (offhand.itemType === ItemTypes.shield) {
            return { damage: 0 };
        }
        return offhand;
    }
    return { damage: 0 };
};
export const GetAmmoForUser = async (userId, equipment) => {
    if (!equipment)
        return 0;
    const equippedRangedWeapon = equipment.ranged;
    if (!equippedRangedWeapon)
        return 0;
    const baseAmmo = equippedRangedWeapon.baseAmmo || 0;
    if (baseAmmo === 0) {
        return 0;
    }
    const quiverTalent = await TalentHelper.UserHasQuiverTalent(userId);
    if (quiverTalent) {
        return baseAmmo + (quiverTalent.modifier || 0);
    }
    return baseAmmo;
};
