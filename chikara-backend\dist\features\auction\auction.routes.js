import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as AuctionController from "./auction.controller.js";
import auctionSchema from "./auction.validation.js";
export const auctionRouter = {
    getList: isLoggedInAuth.handler(async () => {
        const response = await AuctionController.getAuctionList();
        return handleResponse(response);
    }),
    createListing: canMakeStateChangesAuth
        .input(auctionSchema.createAuctionListing)
        .handler(async ({ input, context }) => {
        const { itemId, quantity, buyoutPrice, auctionLength, bankFunds } = input;
        const response = await AuctionController.createAuctionListing(context.user.id, itemId, quantity, buyoutPrice, auctionLength, bankFunds);
        return handleResponse(response);
    }),
    buyoutListing: canMakeStateChangesAuth
        .input(auctionSchema.buyoutAuctionListing)
        .handler(async ({ input, context }) => {
        const { auctionItemId, quantity } = input;
        const response = await AuctionController.buyoutAuctionListing(context.user.id, auctionItemId, quantity);
        return handleResponse(response);
    }),
    cancelListing: canMakeStateChangesAuth
        .input(auctionSchema.cancelAuctionListing)
        .handler(async ({ input, context }) => {
        const response = await AuctionController.cancelAuctionListing(context.user.id, input.auctionItemId);
        return handleResponse(response);
    }),
};
export default auctionRouter;
