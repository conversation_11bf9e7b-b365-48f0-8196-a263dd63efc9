import { db } from "../lib/db.js";
export const decrementUserTalentPoints = async (userId, pointsToDecrement) => {
    return await db.user.update({
        where: { id: userId },
        data: { talentPoints: { decrement: pointsToDecrement } },
    });
};
export const incrementUserMaxActionPoints = async (userId, increment) => {
    return await db.user.update({
        where: { id: userId },
        data: { maxActionPoints: { increment } },
    });
};
export const incrementUserHealth = async (userId, healthIncrement) => {
    return await db.user.update({
        where: { id: userId },
        data: { health: { increment: healthIncrement } },
    });
};
export const findUserTalents = async (userId) => {
    return await db.user_talent.findMany({
        where: { userId: userId },
        select: { userId: true, talentId: true, level: true },
    });
};
export const findUserTalent = async (userId, talentId) => {
    if (typeof userId !== "number") {
        userId = Number.parseInt(userId);
    }
    return await db.user_talent.findFirst({
        where: {
            userId,
            talentId: talentId,
        },
    });
};
export const createUserTalent = async (userId, talentId) => {
    return await db.user_talent.create({
        data: {
            userId: userId,
            talentId: talentId,
            level: 1,
        },
    });
};
export const updateUserTalentLevel = async (userId, talentId, newLevel) => {
    return await db.user_talent.update({
        where: {
            talentId_userId: {
                userId: userId,
                talentId: talentId,
            },
        },
        data: {
            level: newLevel,
        },
    });
};
export const deleteAllUserTalents = async (userId) => {
    return await db.user_talent.deleteMany({
        where: {
            userId: userId,
        },
    });
};
export const updateUserTalentPoints = async (userId, talentPoints) => {
    return await db.user.update({
        where: { id: userId },
        data: { talentPoints },
    });
};
export const updateUserMaxActionPoints = async (userId, maxActionPoints) => {
    return await db.user.update({
        where: { id: userId },
        data: { maxActionPoints },
    });
};
export const resetUserEquippedAbilities = async (userId) => {
    return await db.user_equipped_abilities.upsert({
        where: { userId },
        update: {
            equippedAbility1Id: null,
            equippedAbility2Id: null,
            equippedAbility3Id: null,
            equippedAbility4Id: null,
        },
        create: {
            userId,
            equippedAbility1Id: null,
            equippedAbility2Id: null,
            equippedAbility3Id: null,
            equippedAbility4Id: null,
        },
    });
};
export const findUserEquippedAbilities = async (userId) => {
    return await db.user_equipped_abilities.findUnique({
        where: { userId },
        include: {
            talent_equippedAbility1: true,
            talent_equippedAbility2: true,
            talent_equippedAbility3: true,
            talent_equippedAbility4: true,
        },
    });
};
export const updateUserEquippedAbilities = async (userId, slot, talentId) => {
    if (slot < 1 || slot > 4) {
        throw new Error(`Invalid slot number: ${slot}. Must be between 1 and 4.`);
    }
    const equippedAbilityId = `equippedAbility${slot}Id`;
    return await db.user_equipped_abilities.upsert({
        where: { userId },
        update: { [equippedAbilityId]: talentId },
        create: {
            userId,
            equippedAbility1Id: slot === 1 ? talentId : null,
            equippedAbility2Id: slot === 2 ? talentId : null,
            equippedAbility3Id: slot === 3 ? talentId : null,
            equippedAbility4Id: slot === 4 ? talentId : null,
        },
    });
};
