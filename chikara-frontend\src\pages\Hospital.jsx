import Button from "@/components/Buttons/Button";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import DataTable from "@/components/Tables/DataTable";
import useHealUser from "@/features/hospital/api/useHealUser";
import HospitalInjuryPanel from "@/features/hospital/components/HospitalInjuryPanel";
import { timeRemaining } from "@/helpers/dateHelpers";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useIsTalentUnlocked from "@/hooks/api/useIsTalentUnlocked";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { cn } from "@/lib/utils";
import { orpc } from "@/lib/orpc";
import { useQuery } from "@tanstack/react-query";
import { useLayoutEffect, useState } from "react";
import { Link } from "react-router-dom";

function Hospital() {
    const { isLoading, error, data } = useQuery(
        orpc.infirmary.getHospitalList.queryOptions({
            select: (hospitalData) => hospitalData.sort((a, b) => (a.hospitalisedUntil > b.hospitalisedUntil ? 1 : -1)),
        })
    );
    const { data: injuredData, isLoading: isLoadingPatients } = useQuery(
        orpc.infirmary.getInjuredList.queryOptions({
            select: (injuredListData) => injuredListData.sort((a, b) => (b.endsAt > a.endsAt ? 1 : -1)),
        })
    );
    const reviveTalent = useIsTalentUnlocked("revive");
    const [currentTab, setCurrentTab] = useState("injured");

    const isInjuredTab = currentTab === "injured";

    // TODO: Clean up
    const dailyRevivesUsed = 0;
    const revivesRemaining = reviveTalent?.modifier - dailyRevivesUsed || 0;

    const { healUser } = useHealUser();

    const idComparator = (a, b, nodeA, nodeB) => {
        return nodeA.data.id - nodeB.data.id;
    };

    const { data: currentUser } = useFetchCurrentUser();
    const isMobile = useCheckMobileScreen();

    const getGangTextCol = (gang) => {
        if (!gang) {
            return "text-gray-400";
        }
        if (gang.id === 1) return "text-green-500";
        return "text-red-400";
    };

    const DisplayInjuries = (props) => {
        const injuries = props?.data?.user_status_effect;
        if (injuries && injuries?.length < 1)
            return (
                <div className="!flex items-center! justify-center! w-full! font-semibold">
                    <p className="m-auto! text-red-400 text-xs md:text-sm">Regaining Health</p>
                </div>
            );
        return (
            <div className="h-full! flex! text-center! justify-center! flex-col items-center gap-1.5 py-1.5 font-semibold">
                {injuries?.map((injury) => (
                    <div key={injury?.id}>
                        <li className="flex flex-row gap-1.5 text-gray-200 text-xs leading-none md:text-sm">
                            {injury?.stacks}x{" "}
                            <p className="text-red-500">
                                {injury?.customName ? injury?.customName : injury?.effect?.name}
                            </p>
                        </li>
                    </div>
                ))}
                {isInjuredTab && (
                    <p className="text-[0.6rem] text-blue-400 leading-none md:text-sm">
                        Fully healed in {timeRemaining(props?.data?.endsAt)}
                    </p>
                )}
            </div>
        );
    };

    const DisplayActions = (props) => {
        const effectId = props?.data?.user_status_effect?.[0]?.id;

        return (
            <div className="flex size-full">
                <Button className="m-auto!" disabled={revivesRemaining === 0} onClick={() => healUser.mutate(effectId)}>
                    Heal ({revivesRemaining})
                </Button>
            </div>
        );
    };

    const DisplayPlayerCell = (props) => {
        const { username, id, gang } = props?.data;

        return (
            <div
                className={cn(
                    props.data?.defeated && "grayscale",
                    props.data?.disabled && "opacity-25 grayscale",
                    "relative flex h-full items-center justify-normal px-0.5 py-0 font-lili md:w-full md:gap-2"
                )}
            >
                <div className="mt-1.5 flex min-w-13 flex-col items-center justify-center gap-1 text-center md:mt-0">
                    <DisplayAvatar
                        src={props?.data}
                        className="aspect-square! size-11 rounded-full border border-blue-800 md:size-13"
                    />
                    {id && (
                        <small className="block font-semibold text-gray-500 text-xs md:hidden dark:text-blue-400">
                            #{id}
                        </small>
                    )}
                </div>
                <Link to={!username ? null : `/profile/${id}`}>
                    <div className="ml-4">
                        <div
                            className={cn(
                                "font-bold text-sm text-stroke-sm md:text-base",
                                username ? "text-blue-600" : "text-gray-200"
                            )}
                        >
                            {!username ? "Anonymous" : username}{" "}
                        </div>
                        {id && (
                            <div className="hidden text-gray-500 text-stroke-sm text-xs md:block md:text-sm dark:text-gray-400">
                                ID <span className="text-indigo-400">#{id}</span>
                            </div>
                        )}

                        <div
                            className={cn(
                                getGangTextCol(gang),
                                "mt-1 block font-semibold text-xs md:hidden md:text-sm dark:text-stroke-sm"
                            )}
                        >
                            {gang === null ? "No Gang" : gang?.name}
                        </div>
                    </div>
                </Link>
            </div>
        );
    };

    useLayoutEffect(() => {
        setColDefs(cols);
    }, [currentTab, dailyRevivesUsed]);

    const cols = [
        {
            headerName: "Name",
            field: "id",
            comparator: idComparator,
            cellRenderer: DisplayPlayerCell,
            minWidth: isMobile ? 160 : 300,
            maxWidth: isMobile ? 160 : null,
            suppressFloatingFilterButton: true,
            filterParams: {
                filterOptions: ["contains"],
                defaultOption: "contains",
            },
        },
        {
            headerName: "Injuries",
            field: "hospitalisedReason",
            minWidth: isMobile ? 120 : null,
            maxWidth: isMobile ? 120 : null,
            cellRenderer: DisplayInjuries,
            cellClass: "items-center! justify-center! flex!",
            headerClass: "centerGridHeader",
            filter: false,
            autoHeight: true,
        },
        {
            headerName: isInjuredTab ? "Actions" : "Duration",
            field: isInjuredTab ? "endsAt" : "hospitalisedUntil",
            headerClass: "centerGridHeader",
            cellClass: "md:text-base text-sm font-semibold text-center flex! items-center! justify-center! truncate",
            cellRenderer: isInjuredTab ? DisplayActions : null,
            filter: false,
            floatingFilter: false,
            valueFormatter: isInjuredTab
                ? null
                : (params) => {
                      return timeRemaining(params.value);
                  },
            filterValueGetter: isInjuredTab
                ? null
                : (params) => {
                      return timeRemaining(params.value);
                  },
        },
    ];

    const [colDefs, setColDefs] = useState(cols);

    if (error) return "An error has occurred: " + error.message;

    const tabs = [
        { name: "Injured List", value: "injured", current: currentTab === "injured" },
        { name: "Patients List", value: "treatment", current: currentTab === "treatment" },
    ];

    return (
        <div className="mb-8 md:mx-auto md:mb-0 md:max-w-6xl">
            <HospitalInjuryPanel currentUser={currentUser} />
            <DataTable
                dataList={!isInjuredTab ? data : injuredData}
                colDefs={colDefs}
                isLoading={isLoading || isLoadingPatients}
                setCurrentTab={setCurrentTab}
                currentTab={currentTab}
                tabs={tabs}
            />
        </div>
    );
}

export default Hospital;
