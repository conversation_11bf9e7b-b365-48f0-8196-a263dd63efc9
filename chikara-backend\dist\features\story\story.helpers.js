export const validateEpisodeChoices = (episode, choices) => {
    if (!episode.choices) {
        return Object.keys(choices).length === 0;
    }
    for (const choiceId of Object.keys(choices)) {
        if (!episode.choices[choiceId]) {
            return false;
        }
    }
    return true;
};
export const checkSeasonUnlock = (season, userLevel) => {
    if (userLevel < season.requiredLevel) {
        return false;
    }
    if (new Date() < season.startDate) {
        return false;
    }
    return true;
};
export const checkChapterUnlock = (chapter, userLevel, userProgress) => {
    if (userLevel < chapter.requiredLevel) {
        return false;
    }
    if (new Date() < chapter.unlockDate) {
        return false;
    }
    if (chapter.requiredChapterIds && chapter.requiredChapterIds.length > 0) {
        const chapterProgress = userProgress?.chapterProgress || {};
        const hasAllChapters = chapter.requiredChapterIds.every((chapterId) => {
            const progress = chapterProgress[chapterId.toString()];
            return progress && progress.status === "completed";
        });
        if (!hasAllChapters) {
            return false;
        }
    }
    return true;
};
