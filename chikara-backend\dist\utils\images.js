import crypto from "node:crypto";
import * as fs from "node:fs";
import { saveToCDN } from "./cdnUpload.js";
import { LogErrorStack, logger } from "./log.js";
import multer from "multer";
import sharp from "sharp";
const SUPPORTED_FILE_TYPES = new Set(["jpg", "jpeg", "png", "webp"]);
const SUPPORTED_MIME_TYPES = new Set(["image/jpeg", "image/png", "image/webp"]);
const PUBLIC_DIR = process.env.ENABLE_CDN_UPLOADS === "true" ? "" : "public/";
const USER_UPLOADS_DIRECTORY = PUBLIC_DIR + "avatars/";
const UPLOADS_DIRECTORY = PUBLIC_DIR + "images/";
export const UploadType = {
    AVATAR: 1,
    ITEM: 2,
    SHOP: 3,
    JOB: 4,
    PROFILE_BANNER: 5,
    GANG_AVATAR: 6,
};
const upload = multer({});
function getMimeTypeFromFormat(format) {
    switch (format) {
        case "jpeg":
        case "jpg": {
            return "image/jpeg";
        }
        case "png": {
            return "image/png";
        }
        case "webp": {
            return "image/webp";
        }
        default: {
            return `image/${format}`;
        }
    }
}
export async function getFileType(imageBuffer) {
    try {
        if (!imageBuffer || !Buffer.isBuffer(imageBuffer)) {
            throw new Error("Invalid image buffer provided");
        }
        const metadata = await sharp(imageBuffer).metadata();
        if (!metadata || !metadata.format) {
            throw new Error("Could not detect image format");
        }
        const format = metadata.format.toLowerCase();
        const mimeType = getMimeTypeFromFormat(format);
        return {
            ext: format,
            mime: mimeType,
            width: metadata.width,
            height: metadata.height,
        };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error(`Error detecting file type: ${errorMessage}`);
        throw new Error(`File type detection failed: ${errorMessage}`);
    }
}
export function validateImageType(fileType, imageData) {
    if (!fileType || !fileType.ext) {
        logger.warn(`File type detection failed: ${JSON.stringify(fileType)}`);
        throw new Error("File type detection failed");
    }
    if (!SUPPORTED_MIME_TYPES.has(fileType.mime)) {
        logger.warn(`Unsupported MIME type: ${fileType.mime}`);
        throw new Error(`Unsupported file type: ${fileType.mime}`);
    }
    if (imageData.mimetype && imageData.mimetype !== fileType.mime) {
        logger.warn(`Misleading file info provided. Detected: ${fileType.mime}, Provided: ${imageData.mimetype}`);
        throw new Error("File type mismatch - possible tampering detected");
    }
    if (!SUPPORTED_FILE_TYPES.has(fileType.ext)) {
        logger.warn(`Unsupported file extension: ${fileType.ext}`);
        throw new Error(`Unsupported file type: ${fileType.ext}`);
    }
    if (fileType.width && fileType.height && (fileType.width > 5000 || fileType.height > 5000)) {
        logger.warn(`Image dimensions too large: ${fileType.width}x${fileType.height}`);
        throw new Error("Image dimensions too large (max 5000x5000)");
    }
    return true;
}
function getUploadFolder(uploadType) {
    switch (uploadType) {
        case UploadType.AVATAR:
        case UploadType.GANG_AVATAR: {
            return USER_UPLOADS_DIRECTORY;
        }
        case UploadType.PROFILE_BANNER:
        case UploadType.SHOP:
        case UploadType.JOB:
        case UploadType.ITEM: {
            return UPLOADS_DIRECTORY;
        }
        default: {
            return UPLOADS_DIRECTORY;
        }
    }
}
async function processImage(imageBuffer, uploadType) {
    try {
        let processor = sharp(imageBuffer);
        if (uploadType === UploadType.PROFILE_BANNER) {
            processor = processor.resize(1205, 288);
        }
        else if (uploadType !== UploadType.SHOP && uploadType !== UploadType.JOB) {
            processor = processor.resize(200, 200);
        }
        return await processor.webp({ quality: 80, effort: 6 }).toBuffer();
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error(`Error processing image: ${errorMessage}`);
        throw new Error(`Image processing failed: ${errorMessage}`);
    }
}
async function ensureDirectoryExists(directory) {
    if (process.env.ENABLE_CDN_UPLOADS === "true")
        return;
    try {
        await fs.promises.mkdir(directory, { recursive: true });
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error(`Error creating directory ${directory}: ${errorMessage}`);
        throw new Error(`Failed to create upload directory: ${errorMessage}`);
    }
}
async function deleteOldFile(filePath) {
    if (!filePath)
        return;
    if (process.env.ENABLE_CDN_UPLOADS === "true")
        return;
    try {
        const fullPath = filePath.startsWith(PUBLIC_DIR) || PUBLIC_DIR === "" ? filePath : PUBLIC_DIR + filePath;
        await fs.promises.unlink(fullPath);
        logger.info(`Successfully deleted file: ${fullPath}`);
    }
    catch (error) {
        if (error instanceof Error && "code" in error && error.code !== "ENOENT") {
            const errorMessage = error.message || String(error);
            logger.error(`Error deleting file ${filePath}: ${errorMessage}`);
            LogErrorStack({ error });
        }
    }
}
export function parseSingleImage(fieldName) {
    return (req, res, next) => {
        upload.single(fieldName)(req, res, (err) => {
            if (err) {
                logger.warn(`Multer error parsing single image: ${err.message}`);
                if (err instanceof multer.MulterError) {
                    res.status(400).send({ success: false, data: null, error: `Image upload error: ${err.message}` });
                    return;
                }
                return next(err);
            }
            next();
        });
    };
}
export function parseMultipleImages(fields) {
    return (req, res, next) => {
        upload.fields(fields)(req, res, (err) => {
            if (err) {
                logger.warn(`Multer error parsing multiple images: ${err.message}`);
                if (err instanceof multer.MulterError) {
                    res.status(400).send({ success: false, data: null, error: `Image upload error: ${err.message}` });
                    return;
                }
                return next(err);
            }
            next();
        });
    };
}
export async function saveAsWebp(imageData, uploadType, fileToReplace) {
    try {
        if (!imageData || !imageData.buffer) {
            logger.info("No image data provided for saving.");
            return false;
        }
        const imageBuffer = imageData.buffer;
        const fileType = await getFileType(imageBuffer);
        validateImageType(fileType, imageData);
        const folder = getUploadFolder(uploadType);
        await ensureDirectoryExists(folder);
        const filename = crypto.randomUUID() + ".webp";
        const relativeFilePath = folder.replace(PUBLIC_DIR, "") + filename;
        const fullFilePath = folder + filename;
        const processedBuffer = await processImage(imageBuffer, uploadType);
        if (process.env.ENABLE_CDN_UPLOADS === "true") {
            const cdnPath = await saveToCDN(processedBuffer, relativeFilePath, fileToReplace);
            if (!cdnPath) {
                throw new Error("Failed to upload image to CDN.");
            }
            return cdnPath;
        }
        await sharp(processedBuffer).toFile(fullFilePath);
        if (fileToReplace) {
            await deleteOldFile(fileToReplace);
        }
        return relativeFilePath;
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error(`Failed to save image as WebP: ${errorMessage}`);
        LogErrorStack({ error });
        if (error instanceof Error) {
            throw error;
        }
        else {
            throw new Error(errorMessage);
        }
    }
}
export { getMimeTypeFromFormat, processImage, ensureDirectoryExists, deleteOldFile, getUploadFolder };
