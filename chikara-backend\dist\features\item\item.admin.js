import * as ItemRepository from "../../repositories/item.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { LogErrorStack } from "../../utils/log.js";
export const itemList = async () => {
    try {
        const items = await ItemRepository.findAllItems();
        return { data: items };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Unexpected server error", statusCode: 500 };
    }
};
export const createItem = async (itemData, adminId) => {
    try {
        const data = {
            name: itemData.name,
            about: itemData.about,
            rarity: itemData.rarity,
            itemType: itemData.itemType,
        };
        if (itemData.damage !== undefined)
            data.damage = itemData.damage;
        if (itemData.armour !== undefined)
            data.armour = itemData.armour;
        if (itemData.image !== undefined)
            data.image = itemData.image;
        if (itemData.level !== undefined)
            data.level = itemData.level;
        if (itemData.cashValue !== undefined)
            data.cashValue = itemData.cashValue;
        if (itemData.health !== undefined)
            data.health = itemData.health;
        if (itemData.energy !== undefined)
            data.energy = itemData.energy;
        if (itemData.actionPoints !== undefined)
            data.actionPoints = itemData.actionPoints;
        if (itemData.baseAmmo !== undefined)
            data.baseAmmo = itemData.baseAmmo;
        if (itemData.itemEffects !== undefined)
            data.itemEffects = itemData.itemEffects;
        if (itemData.recipeUnlockId !== undefined) {
            data.crafting_recipe = {
                connect: { id: itemData.recipeUnlockId }
            };
        }
        const item = await ItemRepository.createItem(data);
        logAction({
            action: "ADMIN_CREATE_ITEM",
            userId: adminId,
            info: {
                itemId: item.id,
                itemName: item.name,
            },
        });
        return { data: item };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Error creating item: " + error, statusCode: 500 };
    }
};
export const editItem = async (itemData) => {
    const updateableParams = [
        "name",
        "about",
        "itemType",
        "image",
        "damage",
        "armour",
        "rarity",
        "level",
        "cashValue",
        "health",
        "energy",
        "actionPoints",
        "baseAmmo",
        "itemEffects",
        "recipeUnlockId",
    ];
    try {
        const itemId = Number(itemData.id);
        const itemToUpdate = await ItemRepository.findItemById(itemId);
        if (!itemToUpdate) {
            return { error: "Item not found", statusCode: 404 };
        }
        const updateValues = {};
        for (const param of updateableParams) {
            if (itemData[param] !== undefined) {
                updateValues[param] = itemData[param];
            }
        }
        await ItemRepository.updateItem(itemId, updateValues);
        return { data: itemToUpdate };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Something went wrong", statusCode: 400 };
    }
};
