import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as DailyQuestController from "./dailyquest.controller.js";
import dailyQuestSchema from "./dailyquest.validation.js";
import * as DailyQuestHelper from "./dailyquest.helpers.js";
import { levelGatesConfig } from "../../config/gameConfig.js";

const { DAILY_QUESTS_LEVEL_GATE } = levelGatesConfig.public;

export const dailyQuestRouter = {
    /**
     * Get daily quest list for the user
     * Includes background task to generate daily quests if needed
     */
    getDailyQuests: isLoggedInAuth.handler(async ({ context }) => {
        const { user } = context;

        // Handle background task: generate daily quests if user level is sufficient
        if (user && (user.level || 0) >= DAILY_QUESTS_LEVEL_GATE) {
            await DailyQuestHelper.GenerateDailyQuestsForUser(user);
        }

        const result = await DailyQuestController.GetDailyQuestList(user.id);
        return handleResponse(result);
    }),

    /**
     * Complete a daily quest
     */
    completeDailyQuest: canMakeStateChangesAuth
        .input(dailyQuestSchema.completeDailyQuest)
        .handler(async ({ input, context }) => {
            const result = await DailyQuestController.CompleteDailyQuest(context.user.id, input.id);
            return handleResponse(result);
        }),

    /**
     * Claim daily completion reward
     */
    claimDailyCompletionReward: canMakeStateChangesAuth.handler(async ({ context }) => {
        const result = await DailyQuestController.ClaimDailyCompletionReward(context.user.id);
        return handleResponse(result);
    }),
};
