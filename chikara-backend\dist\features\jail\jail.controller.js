import { redisClient } from "../../config/redisClient.js";
import * as NotificationService from "../../core/notification.service.js";
import * as JailRepository from "../../repositories/jail.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { expiryQueue } from "../../queues/expiryValues/queue.js";
import { NotificationTypes } from "../../types/notification.js";
import { LogErrorStack } from "../../utils/log.js";
import * as UserRepository from "../../repositories/user.repository.js";
export const jailList = async () => {
    const crims = await JailRepository.findJailedUsers();
    return { data: crims };
};
export const BailUser = async (user) => {
    const redisKey = `jail-${user.id}-${user.jailedUntil}`;
    await JailRepository.updateUserJailStatus(user.id);
    const keys = await redisClient.keys(`bull:user-expiry-queue:${redisKey}`);
    for (const key of keys) {
        const jobId = key.split(":").pop();
        if (jobId) {
            await expiryQueue.remove(jobId);
        }
    }
};
export const jailBail = async (userId, targetId) => {
    try {
        const currentUser = await UserRepository.getUserById(userId);
        const target = await UserRepository.getUserById(targetId);
        if (target === null) {
            return { error: "User does not exist", statusCode: 400 };
        }
        if (!currentUser) {
            return { error: "User not found", statusCode: 400 };
        }
        if (target.id === currentUser.id) {
            return { error: "Can't bail yourself from jail!", statusCode: 400 };
        }
        if (!target.jailedUntil) {
            return { error: "User is not in jail", statusCode: 400 };
        }
        const bailfee = target.level * 300;
        if (currentUser.cash < bailfee) {
            return { error: "You don't have enough money to bail", statusCode: 400 };
        }
        await UserRepository.decrementUserCash(currentUser.id, bailfee);
        await BailUser(target);
        logAction({
            action: "JAIL_BAILED",
            userId: currentUser.id,
            info: {
                targetId: target.id,
                targetUsername: target.username,
                bailFee: bailfee,
            },
        });
        NotificationService.NotifyUser(target.id, NotificationTypes.bailed_out, { bailer: currentUser.id });
        return { data: { success: true } };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to bail user:", error });
        return { error: "Failed to bail user", statusCode: 400 };
    }
};
