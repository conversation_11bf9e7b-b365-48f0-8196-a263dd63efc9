import { adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as DropChanceController from "./dropchance.controller.js";
import dropChanceSchema from "./dropchance.validation.js";

export const dropChanceAdminRouter = {
    /**
     * Create a new drop chance (admin only)
     */
    create: adminAuth.input(dropChanceSchema.create).handler(async ({ input }) => {
        const response = await DropChanceController.createDropChance(input);
        return handleResponse(response);
    }),

    /**
     * Edit an existing drop chance (admin only)
     */
    edit: adminAuth.input(dropChanceSchema.edit).handler(async ({ input }) => {
        const response = await DropChanceController.editDropChance(input);
        return handleResponse(response);
    }),

    /**
     * Delete a drop chance (admin only)
     */
    delete: adminAuth.input(dropChanceSchema.delete).handler(async ({ input }) => {
        const response = await DropChanceController.deleteDropChance(input.id);
        return handleResponse(response);
    }),
};

export default dropChanceAdminRouter;
