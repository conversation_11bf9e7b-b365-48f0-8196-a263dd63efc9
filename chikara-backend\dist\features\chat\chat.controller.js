import { chatConfig } from "../../config/gameConfig.js";
import { SendNotification } from "../../config/socket.js";
import * as ChatHelpers from "./chat.helpers.js";
import * as chatRepository from "../../repositories/chat.repository.js";
import { NotificationTypes } from "../../types/notification.js";
import { getRejectedMessageError } from "../../utils/contentFilter.js";
import { LogErrorStack, logger } from "../../utils/log.js";
const { MAX_CHAT_HISTORY_LENGTH } = chatConfig.hidden;
const { DISCORD_CHAT_WEBHOOK_ENABLED } = chatConfig.hidden;
let roomNames = {};
export async function loadRoomNames() {
    const rooms = await chatRepository.findAllChatRooms();
    roomNames = rooms.reduce((acc, room) => {
        acc[room.id] = room.name;
        return acc;
    }, {});
}
export function startRoomNamesRefreshInterval() {
    loadRoomNames().catch((error) => {
        logger.error("Failed to load room names: " + error);
    });
    setInterval(() => {
        loadRoomNames().catch((error) => {
            logger.error("Failed to refresh room names: " + error);
        });
    }, 600_000);
}
export const getRoomName = (roomId) => {
    return roomNames[roomId];
};
export const handleChatMessage = async (io, msg, currentUser) => {
    if (!roomNames[msg.room.id]) {
        logger.info("Attempt to send message to non-existent room");
        return null;
    }
    try {
        if (!ChatHelpers.UserCanSendMessage(currentUser, msg)) {
            if (!currentUser.chatBannedUntil) {
                SendNotification(currentUser.id, {
                    type: NotificationTypes.temporary_notification,
                    details: getRejectedMessageError(),
                });
            }
            return null;
        }
        const messageData = {
            message: msg.message,
            userId: currentUser.id,
            chatRoomId: msg.room.id,
            ...(msg.parentMessageId && { parentMessageId: msg.parentMessageId }),
        };
        const parentMessage = msg.parentMessageId
            ? await chatRepository.findMessageWithUser(msg.parentMessageId)
            : null;
        const newMessage = await chatRepository.createChatMessageWithUser(messageData);
        if (!newMessage) {
            LogErrorStack({
                message: "Failed to create chat message",
                error: new Error("Failed to create chat message"),
            });
            return null;
        }
        const messageToEmit = {
            ...newMessage,
            user: {
                id: currentUser.id,
                username: currentUser.username,
                avatar: currentUser.avatar,
                userType: currentUser.userType,
                level: currentUser.level,
            },
            ...(msg.parentMessageId && parentMessage ? { parentMessage } : {}),
        };
        io?.to(roomNames[msg.room.id]).emit("chat message", messageToEmit);
        if (DISCORD_CHAT_WEBHOOK_ENABLED && process.env.DISCORD_WEBHOOK_URL) {
            ChatHelpers.sendToDiscordChat(msg.message, currentUser);
        }
        return newMessage;
    }
    catch (error) {
        LogErrorStack({ message: "Failed to process chat message:", error });
        return null;
    }
};
export const getChatHistory = async (roomId, limit = 10) => {
    try {
        const requestedLimit = limit && !Number.isNaN(limit) && Number.parseInt(String(limit)) > 0 ? Number.parseInt(String(limit)) : 10;
        const messages = await chatRepository.findChatMessages(roomId, Math.min(requestedLimit, MAX_CHAT_HISTORY_LENGTH));
        const transformedMessages = messages.map((message) => {
            const { chat_message, ...rest } = message;
            return {
                ...rest,
                parentMessage: chat_message,
            };
        });
        return { data: transformedMessages };
    }
    catch (error) {
        LogErrorStack({ message: `Failed to fetch chat history`, error });
        return { error: "Failed to fetch chat history", statusCode: 400 };
    }
};
export const getChatRooms = async () => {
    try {
        const rooms = await chatRepository.findAllChatRooms();
        return { data: rooms };
    }
    catch (error) {
        LogErrorStack({ message: `Failed to fetch chat rooms`, error });
        return { error: "Failed to fetch chat rooms", statusCode: 400 };
    }
};
export const createAnnouncement = async (announcementType, message, roomId = 1, currentUser) => {
    try {
        if (currentUser.userType !== "admin" && currentUser.userType !== "prefect") {
            return { error: "Unauthorized access", statusCode: 403 };
        }
        if (!roomNames[roomId]) {
            return { error: "Invalid chat room", statusCode: 400 };
        }
        await ChatHelpers.SendAnnouncementMessage(announcementType, message, roomId);
        return {
            data: {
                success: true,
                message: "Announcement sent successfully",
            },
        };
    }
    catch (error) {
        LogErrorStack({ message: `Failed to send announcement`, error });
        return { error: "Failed to send announcement", statusCode: 500 };
    }
};
