import { db } from "../lib/db.js";
import { getNow } from "../utils/dateHelpers.js";
import { LogErrorStack } from "../utils/log.js";
export function updateLastActivity(req) {
    if (req.user && req.method === "POST") {
        db.user
            .update({
            where: { id: req.user.id },
            data: { last_activity: getNow() },
        })
            .catch((error) => {
            LogErrorStack({ message: "Error updating last activity:", error });
        });
    }
}
