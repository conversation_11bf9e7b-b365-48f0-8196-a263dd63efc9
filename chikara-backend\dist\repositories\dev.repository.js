import { db } from "../lib/db.js";
export const findAllUsers = async () => {
    return await db.user.findMany();
};
export const findPetsList = async () => {
    return await db.pet.findMany();
};
export const findUserPets = async (userId) => {
    return await db.user_pet.findMany({
        where: { userId },
        include: {
            pet: true,
        },
    });
};
export const findRandomUser = async (excludeUserId) => {
    const users = await db.user.findMany({
        where: {
            AND: [{ userType: { not: "admin" } }, { id: { not: excludeUserId } }, { level: { gt: 5 } }],
        },
    });
    return users[Math.floor(Math.random() * users.length)] || null;
};
export const incrementUserStats = async (userId, amount) => {
    const userUpdate = await db.user.update({
        where: { id: userId },
        data: {
            currentHealth: { increment: amount },
            health: { increment: amount },
        },
    });
    const combatStats = ["strength", "defence", "intelligence", "dexterity", "endurance", "vitality"];
    for (const skillType of combatStats) {
        await db.user_skill.upsert({
            where: {
                userId_skillType: {
                    userId,
                    skillType,
                },
            },
            update: {
                level: { increment: amount },
                updatedAt: new Date(),
            },
            create: {
                userId,
                skillType,
                level: 1 + amount,
                experience: 0,
                talentPoints: 0,
            },
        });
    }
    return userUpdate;
};
export const decrementUserStats = async (userId, amount) => {
    const userUpdate = await db.user.update({
        where: { id: userId },
        data: {
            currentHealth: { decrement: amount },
            health: { decrement: amount },
        },
    });
    const combatStats = ["strength", "defence", "intelligence", "dexterity", "endurance", "vitality"];
    for (const skillType of combatStats) {
        const currentSkill = await db.user_skill.findUnique({
            where: {
                userId_skillType: {
                    userId,
                    skillType,
                },
            },
        });
        const currentLevel = currentSkill?.level || 1;
        const newLevel = Math.max(1, currentLevel - amount);
        await db.user_skill.upsert({
            where: {
                userId_skillType: {
                    userId,
                    skillType,
                },
            },
            update: {
                level: newLevel,
                updatedAt: new Date(),
            },
            create: {
                userId,
                skillType,
                level: 1,
                experience: 0,
                talentPoints: 0,
            },
        });
    }
    return userUpdate;
};
export const createUserItem = async (userId, itemId, count) => {
    return await db.user_item.create({
        data: {
            userId,
            itemId,
            count,
        },
    });
};
export const deleteQuestProgress = async (userId) => {
    return await db.quest_progress.deleteMany({
        where: { userId },
    });
};
export const findNonAdminUsers = async () => {
    return await db.user.findMany({
        where: {
            userType: {
                not: "admin",
            },
        },
    });
};
export const findUserQuestItem = async (userId, itemId) => {
    return await db.user_item.findFirst({
        where: {
            userId,
            itemId,
            item: {
                itemType: "quest",
            },
        },
        select: {
            id: true,
            count: true,
            itemId: true,
            item: {
                select: {
                    itemType: true,
                },
            },
        },
    });
};
export const findAllQuests = async () => {
    return await db.quest.findMany();
};
export const createQuestProgress = async (userId, questId, status) => {
    return await db.quest_progress.create({
        data: {
            userId,
            questId,
            questStatus: status,
        },
    });
};
export const createTraderRep = async (userId, shopId, level) => {
    return await db.trader_rep.create({
        data: {
            userId,
            shopId,
            reputationLevel: level,
        },
    });
};
export const findRecentChatMessages = async (limit) => {
    return await db.chat_message.findMany({
        orderBy: {
            createdAt: "desc",
        },
        take: Math.min(limit),
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    createdAt: true,
                    userType: true,
                    level: true,
                },
            },
        },
    });
};
export const removeEffects = async (userId) => {
    return await db.user_status_effect.deleteMany({
        where: { userId },
    });
};
export const findUserEggs = async (userId) => {
    return await db.user_pet.findMany({
        where: { userId, evolution: { path: "$.current", equals: "egg" } },
    });
};
export const updatePetProgress = async (userId, petId, evolution) => {
    return await db.user_pet.update({
        where: { id: petId },
        data: { evolution },
    });
};
export const setAllPetsHappiness = async (userId) => {
    return await db.user_pet.updateMany({
        where: { userId },
        data: { happiness: 100 },
    });
};
export const addXpToAllPets = async (userId, xpAmount) => {
    const userPets = await db.user_pet.findMany({
        where: { userId },
    });
    const updatePromises = userPets.map((pet) => {
        return db.user_pet.update({
            where: { id: pet.id },
            data: { xp: { increment: xpAmount } },
        });
    });
    return await Promise.all(updatePromises);
};
export const deleteExploreNodes = async (userId) => {
    return await db.explore_player_node.deleteMany({
        where: { userId },
    });
};
