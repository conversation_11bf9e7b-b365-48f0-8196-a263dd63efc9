import missions from "../../../../data/missions.js";
import * as MissionHelper from "../../../../features/mission/mission.helpers.js";
import { db } from "../../../../lib/db.js";
import { getToday, getTomorrow } from "../../../../utils/dateHelpers.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";
function selectRandomMissions(missionList, count) {
    const listCopy = [...missionList];
    const shuffled = listCopy.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
}
async function generateMissionsForDate(date) {
    const missionSets = missions;
    const dailyMissions = {};
    for (const tier of Object.keys(missionSets)) {
        dailyMissions[tier] = selectRandomMissions(missionSets[tier], 3);
    }
    const missionsToCreate = [];
    for (const tier of Object.keys(dailyMissions)) {
        const tierLevel = Number.parseInt(tier.replace("tier", ""));
        const tierData = MissionHelper.GenerateMissionsForTier(tierLevel, date, dailyMissions[tier]);
        const processedTierData = tierData.map((mission) => {
            const { rewardType, ...missionWithoutRewardType } = mission;
            return {
                ...missionWithoutRewardType,
                missionDate: mission.missionDate,
            };
        });
        missionsToCreate.push(...processedTierData);
    }
    try {
        await db.daily_mission.createMany({
            data: missionsToCreate,
        });
        logger.info(`Stored new daily missions for ${date}`);
    }
    catch (error) {
        const err = error;
        logger.error(`Error storing daily missions for ${date}: ${err.message}`, err);
        throw error;
    }
}
async function processDailyMissions() {
    const tomorrow = getTomorrow();
    try {
        const count = await db.daily_mission.count({
            where: {
                missionDate: tomorrow,
            },
        });
        if (count === 0) {
            logger.profile("processDailyMissions");
            await generateMissionsForDate(tomorrow);
            logger.profile("processDailyMissions");
        }
        else {
            logger.info("Daily missions for tomorrow are already set.");
        }
    }
    catch (error) {
        LogErrorStack({ error: "Failed to generate tomorrow's daily missions: " + error });
    }
}
async function checkAndProcessMissions() {
    const today = getToday();
    try {
        const count = await db.daily_mission.count({
            where: {
                missionDate: today,
            },
        });
        if (count === 0) {
            logger.info("No missions for today. Generating now.");
            await generateMissionsForDate(today);
        }
        else {
            logger.info("Missions for today are already set.");
        }
    }
    catch (error) {
        LogErrorStack({ error: "Failed to check/process today's missions: " + error });
    }
}
export default async function processDailyMissionsTask() {
}
