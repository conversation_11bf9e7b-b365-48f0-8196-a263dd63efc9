import * as core from "../../core/inventory.service.js";
import { getRedisItem, redisClient } from "../../config/redisClient.js";
import { db } from "../../lib/db.js";
import { LogErrorStack, logger } from "../../utils/log.js";
import crypto from "node:crypto";
import * as UserRepository from "../../repositories/user.repository.js";
const MINING_DIFFICULTIES = {
    easy: {
        name: "Shallow Mine",
        energyCost: 2,
        maxSwings: 8,
        speed: 1,
        perfectZoneSize: 12,
        goodZoneSize: 35,
        oreMultiplier: 1,
        rareChance: 0.1,
        requiredLevel: 1,
    },
    medium: {
        name: "Deep Mine",
        energyCost: 4,
        maxSwings: 10,
        speed: 1.3,
        perfectZoneSize: 10,
        goodZoneSize: 28,
        oreMultiplier: 1.5,
        rareChance: 0.2,
        requiredLevel: 15,
    },
    hard: {
        name: "Crystal Cavern",
        energyCost: 6,
        maxSwings: 12,
        speed: 1.6,
        perfectZoneSize: 8,
        goodZoneSize: 24,
        oreMultiplier: 2,
        rareChance: 0.35,
        requiredLevel: 30,
    },
    expert: {
        name: "Mythril Depths",
        energyCost: 8,
        maxSwings: 15,
        speed: 2,
        perfectZoneSize: 5,
        goodZoneSize: 15,
        oreMultiplier: 3,
        rareChance: 0.5,
        requiredLevel: 45,
    },
};
const ORE_ITEMS = {
    copper: { itemId: 200, rarity: "common", value: 10 },
    iron: { itemId: 201, rarity: "common", value: 25 },
    silver: { itemId: 202, rarity: "uncommon", value: 50 },
    gold: { itemId: 203, rarity: "rare", value: 100 },
    platinum: { itemId: 204, rarity: "epic", value: 250 },
    diamond: { itemId: 205, rarity: "epic", value: 500 },
    mythril: { itemId: 206, rarity: "legendary", value: 1000 },
};
const REDIS_TTL = 3600;
export const startMining = async (userId, difficulty = "easy") => {
    try {
        const user = await UserRepository.findUserWithMiningSkills(userId);
        if (!user) {
            return { error: "User not found", statusCode: 404 };
        }
        const difficultyConfig = MINING_DIFFICULTIES[difficulty];
        const miningSkill = user.user_skills.find((s) => s.skillType === "mining");
        const miningLevel = miningSkill?.level || 1;
        if (miningLevel < difficultyConfig.requiredLevel) {
            return {
                error: `Mining level ${difficultyConfig.requiredLevel} required for ${difficultyConfig.name}`,
                statusCode: 400,
            };
        }
        const existingSessionKey = `mining_session:${userId}`;
        const existingSession = await getRedisItem(existingSessionKey);
        if (existingSession) {
            return {
                error: "You already have an active mining session",
                statusCode: 400,
            };
        }
        const sessionId = crypto.randomUUID();
        const session = {
            sessionId,
            userId,
            difficulty,
            swingsRemaining: difficultyConfig.maxSwings,
            oreCollected: [],
            experienceGained: 0,
            streak: 0,
            maxStreak: 0,
            startTime: Date.now(),
            energyCost: difficultyConfig.energyCost,
        };
        await redisClient.setEx(existingSessionKey, REDIS_TTL, JSON.stringify(session));
        logger.info(`Mining session started for user ${userId}, difficulty: ${difficulty}`);
        return {
            data: {
                sessionId,
                difficulty: difficultyConfig.name,
                swingsRemaining: difficultyConfig.maxSwings,
                perfectZoneSize: difficultyConfig.perfectZoneSize,
                goodZoneSize: difficultyConfig.goodZoneSize,
                speed: difficultyConfig.speed,
            },
        };
    }
    catch (error) {
        LogErrorStack({ message: "Error starting mining", error });
        return { error: "Failed to start mining", statusCode: 500 };
    }
};
export const processSwing = async (userId, hitPosition, targetPosition) => {
    try {
        const sessionKey = `mining_session:${userId}`;
        const sessionData = await getRedisItem(sessionKey);
        if (!sessionData) {
            return { error: "No active mining session", statusCode: 400 };
        }
        const session = sessionData;
        const difficultyConfig = MINING_DIFFICULTIES[session.difficulty];
        if (session.swingsRemaining <= 0) {
            return { error: "No swings remaining", statusCode: 400 };
        }
        const distance = Math.abs(hitPosition - targetPosition);
        const perfectZone = difficultyConfig.perfectZoneSize / 2;
        const goodZone = difficultyConfig.goodZoneSize / 2;
        let hitQuality = "miss";
        let expGained = 0;
        if (distance <= perfectZone) {
            hitQuality = "perfect";
            expGained = 25;
            session.streak += 1;
        }
        else if (distance <= goodZone) {
            hitQuality = "good";
            expGained = 15;
            session.streak += 1;
        }
        else {
            hitQuality = "miss";
            expGained = 5;
            session.streak = 0;
        }
        const streakBonus = Math.floor(session.streak / 3) * 5;
        expGained += streakBonus;
        session.experienceGained += expGained;
        session.maxStreak = Math.max(session.maxStreak, session.streak);
        session.swingsRemaining -= 1;
        const oreReward = generateOreReward(hitQuality, session.streak, difficultyConfig);
        if (oreReward) {
            session.oreCollected.push({
                itemId: oreReward.itemId,
                quantity: oreReward.quantity,
                hitQuality,
            });
        }
        await redisClient.setEx(sessionKey, REDIS_TTL, JSON.stringify(session));
        const nextTargetPosition = Math.random() * 80 + 10;
        const result = {
            hitQuality,
            expGained,
            streak: session.streak,
            maxStreak: session.maxStreak,
            swingsRemaining: session.swingsRemaining,
            nextTargetPosition,
            oreReward,
            sessionComplete: session.swingsRemaining === 0,
        };
        if (session.swingsRemaining === 0) {
            await finalizeSession(userId, session);
        }
        return { data: result };
    }
    catch (error) {
        LogErrorStack({ message: "Error processing swing", error });
        return { error: "Failed to process swing", statusCode: 500 };
    }
};
export const getMiningSession = async (userId) => {
    try {
        const sessionKey = `mining_session:${userId}`;
        const sessionData = await getRedisItem(sessionKey);
        if (!sessionData) {
            return { error: "No active mining session", statusCode: 404 };
        }
        const session = sessionData;
        const difficultyConfig = MINING_DIFFICULTIES[session.difficulty];
        return {
            data: {
                sessionId: session.sessionId,
                difficulty: difficultyConfig.name,
                swingsRemaining: session.swingsRemaining,
                oreCollected: session.oreCollected,
                experienceGained: session.experienceGained,
                streak: session.streak,
                maxStreak: session.maxStreak,
                perfectZoneSize: difficultyConfig.perfectZoneSize,
                goodZoneSize: difficultyConfig.goodZoneSize,
                speed: difficultyConfig.speed,
            },
        };
    }
    catch (error) {
        LogErrorStack({ message: "Error getting mining session", error });
        return { error: "Failed to get mining session", statusCode: 500 };
    }
};
export const cancelMining = async (userId) => {
    try {
        const sessionKey = `mining_session:${userId}`;
        const sessionData = await getRedisItem(sessionKey);
        if (!sessionData) {
            return { error: "No active mining session", statusCode: 404 };
        }
        const session = sessionData;
        const timeElapsed = Date.now() - session.startTime;
        if (timeElapsed < 30000) {
            const user = await UserRepository.getUserById(userId);
            if (user) {
                await db.user.update({
                    where: { id: userId },
                    data: { energy: user.energy + session.energyCost },
                });
            }
        }
        await redisClient.del(sessionKey);
        logger.info(`Mining session cancelled for user ${userId}`);
        return { data: { message: "Mining session cancelled" } };
    }
    catch (error) {
        LogErrorStack({ message: "Error cancelling mining", error });
        return { error: "Failed to cancel mining", statusCode: 500 };
    }
};
async function finalizeSession(userId, session) {
    try {
        const miningSkill = await db.user_skill.findFirst({
            where: { userId, skillType: "mining" },
        });
        if (miningSkill) {
            await db.user_skill.update({
                where: { id: miningSkill.id },
                data: { experience: miningSkill.experience + session.experienceGained },
            });
        }
        else {
            await db.user_skill.create({
                data: {
                    userId,
                    skillType: "mining",
                    level: 1,
                    experience: session.experienceGained,
                },
            });
        }
        for (const ore of session.oreCollected) {
            await core.AddItemToUser({
                userId,
                itemId: ore.itemId,
                amount: ore.quantity,
                isTradeable: true,
            });
        }
        const sessionKey = `mining_session:${userId}`;
        await redisClient.del(sessionKey);
        logger.info(`Mining session finalized for user ${userId}, exp: ${session.experienceGained}, ores: ${session.oreCollected.length}`);
    }
    catch (error) {
        LogErrorStack({ message: "Error finalizing session", error });
    }
}
function generateOreReward(hitQuality, streak, difficultyConfig) {
    const random = Math.random();
    let rareBonus = 0;
    let baseMultiplier = 1;
    if (hitQuality === "perfect") {
        rareBonus = 0.15;
        baseMultiplier = 1.5;
    }
    else if (hitQuality === "good") {
        rareBonus = 0.08;
        baseMultiplier = 1.2;
    }
    else {
        rareBonus = 0;
        baseMultiplier = 0.6;
    }
    const streakBonus = Math.min(streak * 0.02, 0.2);
    const totalRareChance = (difficultyConfig.rareChance + rareBonus + streakBonus) * baseMultiplier;
    let selectedOre;
    let quantity = 1;
    if (hitQuality === "miss") {
        selectedOre = Math.random() < 0.7 ? ORE_ITEMS.copper : ORE_ITEMS.iron;
        quantity = 1;
    }
    else {
        if (random < totalRareChance * 0.1) {
            selectedOre = ORE_ITEMS.mythril;
        }
        else if (random < totalRareChance * 0.3) {
            selectedOre = Math.random() < 0.5 ? ORE_ITEMS.diamond : ORE_ITEMS.platinum;
        }
        else if (random < totalRareChance * 0.6) {
            selectedOre = ORE_ITEMS.gold;
        }
        else if (random < totalRareChance) {
            selectedOre = ORE_ITEMS.silver;
        }
        else {
            selectedOre = Math.random() < 0.6 ? ORE_ITEMS.copper : ORE_ITEMS.iron;
        }
        if (hitQuality === "perfect")
            quantity += 1;
        if (hitQuality === "good")
            quantity += Math.random() < 0.5 ? 1 : 0;
        if (streak >= 5)
            quantity += 1;
        if (streak >= 10)
            quantity += 1;
    }
    return {
        itemId: selectedOre.itemId,
        quantity,
        name: Object.keys(ORE_ITEMS).find((key) => ORE_ITEMS[key].itemId === selectedOre.itemId),
        value: selectedOre.value,
    };
}
