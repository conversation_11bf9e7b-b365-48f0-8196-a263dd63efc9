import * as InventoryService from "../../core/inventory.service.js";
import * as NotificationService from "../../core/notification.service.js";
import * as StatusEffectService from "../../core/statuseffect.service.js";
import * as UserService from "../../core/user.service.js";
import getScavengeLocation from "../../data/scavengeLocations.js";
import { APOLLO_DIALOGUE, BAD_DIALOGUE_LINES, CHARACTER_NAMES, ENCOUNTER_LOCATIONS_IMAGES, ENCOUNTER_TYPES, GOOD_DIALOGUE_LINES, MAX_DOUBLE_EDGED_NODES, MAX_NODES, MIN_NODES, DOUBLE_EDGE_CHANCE, BATTLE_WEIGHT, BUFF_WEIGHT, CHARACTER_WEIGHT, SCAVENGE_NODE_WEIGHT, DEFAULT_ENCOUNTER_JAIL_DURATION_MS, } from "./roguelike.constants.js";
import { findApplicableQuestsForDrops, findDropChanceByItemId, findPotentialDropsForRoguelike, findScavengeDrops, } from "../../repositories/roguelike.repository.js";
import * as ShrineHelper from "../shrine/shrine.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import { NotificationTypes } from "../../types/notification.js";
import { DropChanceTypes } from "@prisma/client";
import { emitItemDropped } from "../../core/events/index.js";
function randomIntFromInterval(min, max) {
    return Math.floor(Math.random() * (max - min + 1) + min);
}
function GetWeightedRandomNodeType(hasScavengeNodes) {
    const items = [
        {
            item: ENCOUNTER_TYPES.BATTLE,
            weight: BATTLE_WEIGHT,
        },
        {
            item: ENCOUNTER_TYPES.BUFF,
            weight: BUFF_WEIGHT,
        },
        {
            item: ENCOUNTER_TYPES.CHARACTER,
            weight: CHARACTER_WEIGHT,
        },
    ];
    if (hasScavengeNodes) {
        items.push({
            item: ENCOUNTER_TYPES.SCAVENGE,
            weight: SCAVENGE_NODE_WEIGHT,
        });
    }
    let i;
    const weights = [];
    for (i = 0; i < items.length; i++) {
        weights[i] = items[i].weight + (weights[i - 1] || 0);
    }
    const random = Math.random() * weights.at(-1);
    for (i = 0; i < weights.length; i++) {
        if (weights[i] > random) {
            break;
        }
    }
    return items[i].item;
}
export const GenerateMap = ({ hasScavengeNodes, buffs, location, }) => {
    const nodesToGenerate = Math.random() * (MAX_NODES - MIN_NODES) + MIN_NODES;
    const nodes = [];
    nodes.push({ encounterType: ENCOUNTER_TYPES.BASE });
    for (let i = 1; i < nodesToGenerate - 1; ++i) {
        nodes.push({ encounterType: GetWeightedRandomNodeType(hasScavengeNodes), edges: [] });
    }
    nodes.push({ encounterType: ENCOUNTER_TYPES.BOSS });
    const freeNodes = [];
    for (let i = 1; i < nodes.length - 1; ++i) {
        freeNodes.push(i);
    }
    const nodeQueue = [];
    function SelectEdge() {
        const selected = freeNodes[Math.floor(Math.random() * freeNodes.length)];
        nodeQueue.push(selected);
        freeNodes.splice(freeNodes.indexOf(selected), 1);
        return selected;
    }
    nodes[0].edges = [SelectEdge(), SelectEdge(), SelectEdge()];
    let doubleEdgedNodes = 0;
    while (freeNodes.length > 0) {
        const currentNodeIndex = nodeQueue.shift();
        const currentNode = nodes[currentNodeIndex];
        if (!currentNode) {
            throw new Error(`GenerateMap: Expected node at index ${currentNodeIndex} but found undefined.`);
        }
        if (!currentNode.edges) {
            currentNode.edges = [];
        }
        const numberOfEdges = doubleEdgedNodes >= MAX_DOUBLE_EDGED_NODES || Math.random() > DOUBLE_EDGE_CHANCE ? 1 : 2;
        if (numberOfEdges === 2) {
            ++doubleEdgedNodes;
        }
        for (let i = 0; i < numberOfEdges; ++i) {
            if (freeNodes.length > 0) {
                currentNode.edges.push(SelectEdge());
            }
        }
    }
    while (nodeQueue.length > 0) {
        const currentNodeIndex = nodeQueue.shift();
        const currentNode = nodes[currentNodeIndex];
        if (!currentNode) {
            throw new Error(`GenerateMap: Expected node at index ${currentNodeIndex} but found undefined.`);
        }
        if (!currentNode.edges) {
            currentNode.edges = [];
        }
        currentNode.edges.push(nodes.length - 1);
    }
    const { strBuff, defBuff, dexBuff } = buffs;
    return {
        playerLocation: 0,
        currentNodeComplete: true,
        mapComplete: false,
        strBuff,
        defBuff,
        dexBuff,
        nodes: nodes,
        location,
    };
};
export const GetCharacterDialogue = (goodOutcome, mapLevel) => {
    const selectedCharacter = CHARACTER_NAMES[Math.floor(Math.random() * CHARACTER_NAMES.length)];
    const selectedLocation = ENCOUNTER_LOCATIONS_IMAGES[Math.floor(Math.random() * ENCOUNTER_LOCATIONS_IMAGES.length)];
    const selectedLine = goodOutcome
        ? GOOD_DIALOGUE_LINES[Math.floor(Math.random() * GOOD_DIALOGUE_LINES.length)]
        : BAD_DIALOGUE_LINES[Math.floor(Math.random() * BAD_DIALOGUE_LINES.length)];
    const cashReward = randomIntFromInterval(20 + mapLevel * 10, 20 + mapLevel * 30);
    if (goodOutcome && Math.random() <= 0.05) {
        const apolloDialogue = APOLLO_DIALOGUE[Math.floor(Math.random() * APOLLO_DIALOGUE.length)];
        return {
            character: "Apollo",
            location: selectedLocation,
            line: apolloDialogue,
            isItemDrop: false,
            rewards: 0,
            mugged: false,
        };
    }
    if (goodOutcome && Math.random() <= 0.4) {
        return {
            character: selectedCharacter,
            location: selectedLocation,
            line: selectedLine,
            isItemDrop: true,
            rewards: cashReward,
            mugged: !goodOutcome,
        };
    }
    return {
        character: selectedCharacter,
        location: selectedLocation,
        line: selectedLine,
        isItemDrop: false,
        rewards: cashReward,
        mugged: !goodOutcome,
    };
};
export const GetDropId = async (user) => {
    const map = user.roguelikeMap;
    if (!map) {
        return 0;
    }
    const dropChanceType = map.mapComplete ? DropChanceTypes.boss : DropChanceTypes.roguelike;
    const potentialDrops = await findPotentialDropsForRoguelike(user.roguelikeLevel, map.location);
    const applicableQuests = await findApplicableQuestsForDrops(user.id, map.location);
    for (const applicableQuest of applicableQuests) {
        if (!applicableQuest.quest?.quest_objective || applicableQuest.quest.quest_objective.length === 0) {
            continue;
        }
        const questObjective = applicableQuest.quest.quest_objective[0];
        if (questObjective.target === null || questObjective.quantity === null) {
            continue;
        }
        const hasAllQuestItems = await InventoryService.UserHasNumberOfItem(user.id, questObjective.target, questObjective.quantity);
        if (hasAllQuestItems) {
            break;
        }
        let drop = await findDropChanceByItemId(questObjective.target);
        if (!drop || !drop.dropRate) {
            drop = {
                itemId: questObjective.target,
                dropRate: 1.1,
            };
        }
        if (drop) {
            if (dropChanceType === DropChanceTypes.boss) {
                potentialDrops.push(drop);
            }
            else {
                potentialDrops.unshift(drop);
            }
        }
    }
    const shrineBuffActive = await ShrineHelper.dailyBuffIsActive("rareDrops");
    for (const drop of potentialDrops) {
        if (shrineBuffActive && drop.dropRate < 0.08) {
            drop.dropRate *= shrineBuffActive;
        }
        let dropRate = drop.dropRate / 4.5;
        if (dropRate < 0.05) {
            dropRate = dropRate * 0.5;
        }
        if (Math.random() <= dropRate) {
            return drop.itemId;
        }
    }
    return 0;
};
export const GetScavengeItemDrop = async (user, map, choice) => {
    const potentialDrops = await findScavengeDrops(user.roguelikeLevel, map.location, choice);
    if (!potentialDrops || potentialDrops.length === 0) {
        return null;
    }
    const randomIndex = Math.floor(Math.random() * potentialDrops.length);
    const selectedItem = potentialDrops[randomIndex];
    if (selectedItem.itemId === null || !selectedItem.item) {
        return null;
    }
    await InventoryService.AddItemToUser({
        userId: user.id,
        itemId: selectedItem.itemId,
        amount: selectedItem.quantity,
        isTradeable: true,
    });
    await emitItemDropped({
        userId: user.id,
        itemId: selectedItem.itemId,
        quantity: selectedItem.quantity,
        source: "scavenge",
    });
    logAction({
        action: "SCAVENGE_ITEM",
        userId: user.id,
        info: {
            itemId: selectedItem.item.id,
            itemName: selectedItem.item.name,
            quantity: selectedItem.quantity,
        },
    });
    return {
        itemReward: selectedItem.item,
        itemQuantity: selectedItem.quantity,
    };
};
export const GetScavengeBadOutcome = async (currentUser, map, choice) => {
    const jailChance = 0.2;
    const outcome = {};
    if (Math.random() <= jailChance) {
        const jailShrineBuffActive = (await ShrineHelper.dailyBuffIsActive("jail")) || 1;
        const jailDuration = DEFAULT_ENCOUNTER_JAIL_DURATION_MS * jailShrineBuffActive;
        await UserService.JailUser(currentUser.id, jailDuration, "Caught scavenging", {
            notificationType: "jail",
        });
        outcome.jailed = true;
        outcome.jailDuration = jailDuration;
        logAction({
            action: "SCAVENGE_JAILED",
            userId: currentUser.id,
            info: {
                jailDuration: jailDuration,
            },
        });
        await NotificationService.NotifyUser(currentUser.id, NotificationTypes.jail, {
            reason: "scavenging",
            duration: jailDuration,
        }, true);
    }
    else {
        const scavengeData = getScavengeLocation(map.currentNodeChoices || []);
        if (!scavengeData.choices[choice]) {
            throw new Error(`Invalid scavenge choice: ${choice}`);
        }
        const injuryType = scavengeData.choices[choice].injury;
        const injury = await StatusEffectService.GetRandomInjury("Minor", injuryType);
        if (!injury) {
            throw new Error(`No minor injury found for type: ${injuryType}`);
        }
        await StatusEffectService.ApplyStatusEffectToUser(currentUser, injury);
        outcome.injury = injury;
        await NotificationService.NotifyUser(currentUser.id, NotificationTypes.injured, {
            reason: "scavenging",
            injury: injury.name,
            injuryTier: injury.tier,
        }, true);
        logAction({
            action: "SCAVENGE_INJURY",
            userId: currentUser.id,
            info: {
                injury: injury.name,
                injuryTier: injury.tier,
            },
        });
    }
    return outcome;
};
