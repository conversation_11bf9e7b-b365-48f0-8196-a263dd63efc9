import { db } from "../../../../lib/db.js";
import { generateDonationGoalForDate } from "./processShrineGoal.js";
import { getToday } from "../../../../utils/dateHelpers.js";
import { logger } from "../../../../utils/log.js";
async function checkDonationGoal() {
    const today = getToday();
    const count = await db.shrine_goal.count({
        where: {
            goalDate: today,
        },
    });
    if (count === 0) {
        logger.info("No donation goal for today. Generating now.");
        await generateDonationGoalForDate(today);
    }
    else {
        logger.info("Donation goal for today is already set.");
    }
}
export default checkDonationGoal;
