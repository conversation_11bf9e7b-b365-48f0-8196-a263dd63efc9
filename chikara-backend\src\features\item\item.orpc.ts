import * as DropChanceService from "../dropchance/dropchance.controller.js";
import * as ItemControllerAdmin from "./item.admin.js";
import * as ItemController from "./item.controller.js";
import itemSchema from "./item.validation.js";
import * as UniqueItemController from "./uniqueitem.service.js";
import { isLoggedInAuth, canMakeStateChangesAuth, adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";

export const itemRouter = {
    // ===============================================
    // User Routes
    // ===============================================

    /**
     * Get upgrade items for crafting
     */
    getUpgradeItems: isLoggedInAuth.handler(async () => {
        const response = await ItemController.getUpgradeItems();
        return handleResponse(response);
    }),

    /**
     * Upgrade an item using upgrade cores
     */
    upgradeItem: canMakeStateChangesAuth.input(itemSchema.upgradeItem).handler(async ({ input, context }) => {
        const response = await ItemController.upgradeItem(context.user.id, input.upgradeCores, input.itemId.toString());
        return handleResponse(response);
    }),

    // ===============================================
    // Unique Item Routes
    // ===============================================

    /**
     * Use Death Note unique item
     */
    useDeathNote: canMakeStateChangesAuth.input(itemSchema.deathNote).handler(async ({ input, context }) => {
        const response = await UniqueItemController.useDeathNote(
            context.user.id,
            input.userId,
            input.injuryName,
            input.injuryType
        );
        return handleResponse(response);
    }),

    /**
     * Use Life Note unique item
     */
    useLifeNote: canMakeStateChangesAuth.input(itemSchema.lifeNote).handler(async ({ input, context }) => {
        const response = await UniqueItemController.useLifeNote(context.user.id, input.userId);
        return handleResponse(response);
    }),

    /**
     * Use Megaphone unique item
     */
    useMegaphone: canMakeStateChangesAuth.input(itemSchema.megaphone).handler(async ({ input, context }) => {
        const response = await UniqueItemController.useMegaPhone(context.user.id, input.message);
        return handleResponse(response);
    }),

    /**
     * Use Kompromat unique item
     */
    useKompromat: canMakeStateChangesAuth.input(itemSchema.kompromat).handler(async ({ input, context }) => {
        const response = await UniqueItemController.useKompromat(context.user.id, input.userId, input.reason);
        return handleResponse(response);
    }),

    /**
     * Use Daily Chest unique item
     */
    useDailyChest: canMakeStateChangesAuth.handler(async ({ context }) => {
        const response = await UniqueItemController.useDailyChest(context.user.id);
        return handleResponse(response);
    }),

    /**
     * Use Raw Materials Crate unique item
     */
    useMaterialsCrate: canMakeStateChangesAuth.handler(async ({ context }) => {
        const response = await UniqueItemController.useRawMaterialsCrate(context.user.id);
        return handleResponse(response);
    }),

    /**
     * Use Tools Crate unique item
     */
    useToolsCrate: canMakeStateChangesAuth.handler(async ({ context }) => {
        const response = await UniqueItemController.useToolsCrate(context.user.id);
        return handleResponse(response);
    }),

    /**
     * Get daily chest items (read-only)
     */
    getDailyChestItems: isLoggedInAuth.handler(async () => {
        const response = await UniqueItemController.getDailyChestItems();
        return handleResponse(response);
    }),
};

// ===============================================
// Admin Routes
// ===============================================

export const itemAdminRouter = {
    /**
     * Get all items (admin only)
     */
    list: adminAuth.handler(async () => {
        const response = await ItemControllerAdmin.itemList();
        return handleResponse(response);
    }),

    /**
     * Create new item (admin only)
     */
    create: adminAuth.input(itemSchema.create).handler(async ({ input, context }) => {
        const response = await ItemControllerAdmin.createItem(input, context.user.id);
        return handleResponse(response);
    }),

    /**
     * Update existing item (admin only)
     */
    update: adminAuth.input(itemSchema.update).handler(async ({ input }) => {
        const response = await ItemControllerAdmin.editItem(input);
        return handleResponse(response);
    }),

    /**
     * Get drop tables (admin only)
     */
    getDropTables: adminAuth.handler(async () => {
        const response = await DropChanceService.getDropTables();
        return handleResponse(response);
    }),
};
