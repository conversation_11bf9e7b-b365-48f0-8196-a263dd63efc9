import * as NotificationService from "../../core/notification.service.js";
import * as <PERSON>t<PERSON><PERSON>per from "../chat/chat.helpers.js";
import * as SuggestionRepository from "../../repositories/suggestions.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { NotificationTypes } from "../../types/notification.js";
import { LogErrorStack } from "../../utils/log.js";
import { SuggestionStates, SuggestionVoteTypes } from "@prisma/client";
import { emitSuggestionVoted } from "../../core/events/game-event.service.js";
export const getSuggestions = async () => {
    const suggestions = await SuggestionRepository.findAllSuggestions();
    return { data: suggestions };
};
export const getVoteHistory = async (userId) => {
    const voteHistory = await SuggestionRepository.findVoteHistoryByUserId(userId);
    return { data: voteHistory };
};
export const getComments = async (suggestionId) => {
    try {
        const comments = await SuggestionRepository.findCommentsBySuggestionId(suggestionId);
        return { data: comments };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to fetch suggestion comments:", error });
        return { error: "Failed to fetch comments", statusCode: 500 };
    }
};
export const postSuggestion = async (userId, title, content) => {
    try {
        if (!title) {
            return { error: "Missing title!", statusCode: 400 };
        }
        if (!content) {
            return { error: "Missing content!", statusCode: 400 };
        }
        const suggestionData = {
            userId,
            title,
            content,
            state: SuggestionStates.New,
        };
        const suggestion = await SuggestionRepository.createSuggestion(suggestionData);
        await ChatHelper.SendAnnouncementMessage("newSuggestion", JSON.stringify({ id: suggestion.id, title }));
        return { data: suggestion };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to create suggestion:", error });
        return { error: "Failed to create suggestion", statusCode: 400 };
    }
};
export const CommentOnSuggestion = async (userId, suggestionId, message) => {
    const SUGGESTION_COMMENT_MAX_LENGTH = 800;
    try {
        if (message.length > SUGGESTION_COMMENT_MAX_LENGTH) {
            return { error: "Comment too long!", statusCode: 400 };
        }
        const suggestion = await SuggestionRepository.findSuggestionById(suggestionId);
        if (!suggestion) {
            return { error: "Suggestion not found!", statusCode: 400 };
        }
        const commentData = {
            suggestionId,
            userId,
            message,
        };
        await SuggestionRepository.createSuggestionComment(commentData);
        await SuggestionRepository.incrementSuggestionComments(suggestionId);
        logAction({
            action: "SUGGESTION_COMMENT",
            userId,
            info: {
                suggestionId,
            },
        });
        const otherComments = await SuggestionRepository.findUserCommentsBySuggestionId(suggestionId, [
            userId,
            suggestion.userId ?? 0,
        ]);
        const uniqueUserIds = new Set(otherComments.map((comment) => comment.userId));
        for (const commenterId of uniqueUserIds) {
            if (!commenterId) {
                continue;
            }
            NotificationService.NotifyUser(commenterId, NotificationTypes.profile_comment, {
                senderId: userId,
                type: "suggestion_reply",
                suggestionId: suggestion.id,
                suggestionTitle: suggestion.title,
            });
        }
        if (suggestion.userId && suggestion.userId !== userId) {
            NotificationService.NotifyUser(suggestion.userId, NotificationTypes.profile_comment, {
                senderId: userId,
                type: "suggestion",
                suggestionId: suggestion.id,
            });
        }
        return { data: "" };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to send profile comment:", error });
        return { error: "Failed to create profile comment", statusCode: 400 };
    }
};
export const changeSuggestionState = async (userId, suggestionId, newState) => {
    try {
        if (!suggestionId) {
            return { error: "Missing suggestion id!", statusCode: 400 };
        }
        if (!newState) {
            return { error: "Missing state field!", statusCode: 400 };
        }
        if (!Object.values(SuggestionStates).includes(newState)) {
            return { error: "Invalid state value!", statusCode: 400 };
        }
        const updatedSuggestion = await SuggestionRepository.changeSuggestionState(suggestionId, newState);
        if (!updatedSuggestion) {
            return { error: "Suggestion not found!", statusCode: 400 };
        }
        logAction({
            action: "SUGGESTION_STATE_CHANGE",
            userId,
            info: {
                suggestionId,
                state: newState,
            },
        });
        return { data: "" };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to change suggestion state:", error });
        return { error: "Failed to change suggestion state", statusCode: 400 };
    }
};
export const suggestionVote = async (userId, suggestionId, vote) => {
    try {
        if (!suggestionId) {
            return { error: "Missing suggestion id!", statusCode: 400 };
        }
        if (!vote) {
            return { error: "Missing vote type!", statusCode: 400 };
        }
        if (!Object.values(SuggestionVoteTypes).includes(vote)) {
            return { error: "Invalid vote type!", statusCode: 400 };
        }
        const suggestion = await SuggestionRepository.findSuggestionById(suggestionId);
        if (!suggestion) {
            return { error: "Suggestion not found!", statusCode: 400 };
        }
        const previousVote = await SuggestionRepository.findVoteByUserAndSuggestion(userId, suggestionId);
        if (previousVote) {
            if (previousVote.voteType === vote) {
                return { error: "Already voted!", statusCode: 400 };
            }
            previousVote.voteType = vote;
            await SuggestionRepository.updateVote(previousVote);
            await SuggestionRepository.updateSuggestionVoteCounts(suggestionId, {
                type: "change",
                to: vote,
            });
            logAction({
                action: "SUGGESTION_VOTE_CHANGE",
                userId,
                info: {
                    suggestionId,
                    vote,
                },
            });
        }
        else {
            const voteData = {
                userId,
                suggestionId,
                voteType: vote,
            };
            await SuggestionRepository.createVote(voteData);
            await SuggestionRepository.updateSuggestionVoteCounts(suggestionId, {
                type: "add",
                vote,
            });
            emitSuggestionVoted({ userId, suggestionId });
            logAction({
                action: "SUGGESTION_VOTE",
                userId,
                info: {
                    suggestionId,
                    vote,
                },
            });
        }
        return { data: suggestion };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to add vote:", error });
        return { error: "Failed to add vote", statusCode: 400 };
    }
};
