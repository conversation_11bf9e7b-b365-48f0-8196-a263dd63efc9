import { isLoggedInAuth, canMakeStateChangesAuth, adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as StoryController from "./story.controller.js";
import * as StoryRepository from "../../repositories/story.repository.js";
import * as StoryAdmin from "./story.admin.js";
import storySchema from "./story.validation.js";
export const storyRouter = {
    getSeasons: isLoggedInAuth.input(storySchema.getSeasons).handler(async ({ input, context }) => {
        const { includeChapters = false, includeEpisodes = false } = input;
        const seasons = await StoryController.getSeasons(context.user.id, context.user.level, includeChapters, includeEpisodes);
        return handleResponse({ data: seasons });
    }),
    completeEpisode: canMakeStateChangesAuth.input(storySchema.completeEpisode).handler(async ({ input, context }) => {
        const result = await StoryController.completeEpisode(context.user.id, input);
        return handleResponse(result);
    }),
    makeChoice: canMakeStateChangesAuth.input(storySchema.makeChoice).handler(async ({ input, context }) => {
        return handleResponse({ data: "V2 Feature - not yet implemented" });
    }),
    admin: {
        getAllContent: adminAuth.handler(async () => {
            const content = await StoryAdmin.getAllStoryContentForAdmin();
            return handleResponse({ data: content });
        }),
        createSeason: adminAuth.input(storySchema.createSeason).handler(async ({ input }) => {
            const season = await StoryRepository.createSeason(input);
            return handleResponse({ data: season });
        }),
        updateSeason: adminAuth.input(storySchema.updateSeason).handler(async ({ input }) => {
            const { id, ...updateData } = input;
            const season = await StoryRepository.updateSeason(id, updateData);
            return handleResponse({ data: season });
        }),
        createChapter: adminAuth.input(storySchema.createChapter).handler(async ({ input }) => {
            const chapterData = {
                ...input,
                story_season: {
                    connect: { id: input.seasonId },
                },
            };
            const chapter = await StoryRepository.createChapter(chapterData);
            return handleResponse({ data: chapter });
        }),
        updateChapter: adminAuth.input(storySchema.updateChapter).handler(async ({ input }) => {
            const { id, ...updateData } = input;
            const chapter = await StoryRepository.updateChapter(id, updateData);
            return handleResponse({ data: chapter });
        }),
        createEpisode: adminAuth.input(storySchema.createEpisode).handler(async ({ input }) => {
            const episodeData = {
                ...input,
                quest_objective: {
                    connect: { id: input.objectiveId },
                },
            };
            const episode = await StoryRepository.createEpisode(episodeData);
            return handleResponse({ data: episode });
        }),
        updateEpisode: adminAuth.input(storySchema.updateEpisode).handler(async ({ input }) => {
            const { id, ...updateData } = input;
            const episode = await StoryRepository.updateEpisode(id, updateData);
            return handleResponse({ data: episode });
        }),
        getContentStats: adminAuth.handler(async () => {
            const stats = await StoryAdmin.getStoryContentStats();
            return handleResponse({ data: stats });
        }),
    },
};
export default storyRouter;
