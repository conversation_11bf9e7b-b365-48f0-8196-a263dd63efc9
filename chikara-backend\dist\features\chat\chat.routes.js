import { createAnnouncement, getChatHistory, getChatRooms } from "./chat.controller.js";
import { chatSchema } from "./chatmessage.validation.js";
import { adminAuth, isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
export const chatRouter = {
    getRooms: isLoggedInAuth.handler(async () => {
        const response = await getChatRooms();
        return handleResponse(response);
    }),
    getHistory: isLoggedInAuth.input(chatSchema.getChatHistory).handler(async ({ input }) => {
        const { roomId, limit } = input;
        const response = await getChatHistory(Number(roomId), limit);
        return handleResponse(response);
    }),
    createAnnouncement: adminAuth.input(chatSchema.createAnnouncement).handler(async ({ input, context }) => {
        const { announcementType, message, roomId = 1 } = input;
        const response = await createAnnouncement(announcementType, message, roomId, context.user);
        return handleResponse(response);
    }),
};
