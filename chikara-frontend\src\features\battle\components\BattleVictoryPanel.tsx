import lifeEssenceImg from "@/assets/icons/UI/lifeEssence.png";
import respectImg from "@/assets/icons/UI/respect.png";
import Button from "@/components/Buttons/Button";
// import { DisplayItem } from "@/components/DisplayItem";
import { APIROUTES } from "@/helpers/apiRoutes";
import { cn } from "@/lib/utils";
import { useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useNormalStore } from "../../../app/store/stores";
import { usePostBattleAction } from "../api/usePostBattleAction";
import { BattleType, BattlePlayer, PostBattleAction } from "../types/battle";

const VictoryOptionBtn = ({
    text,
    onClick,
    description,
}: {
    text: string;
    onClick: () => void;
    description: string;
}) => {
    return (
        <button
            className="darkBlueButtonBG mr-2 mb-2 flex h-16 w-full items-center justify-center rounded-md px-4 py-2 font-medium text-gray-700 text-xl shadow-xs dark:text-gray-200 dark:text-stroke-sm"
            onClick={() => onClick()}
        >
            <div>
                <p className="">{text}</p>
                <p className="text-gray-400 text-xs">{description}</p>
            </div>
        </button>
    );
};

interface VictoryPanelProps {
    enemy: BattlePlayer["username"];
    flee: boolean;
    isNPC: boolean;
    battleType: BattleType | undefined;
}

export default function VictoryPanel({ enemy, flee, isNPC, battleType }: VictoryPanelProps) {
    const [actionSelected, setActionSelected] = useState("");
    const [mugAmount, setMugAmount] = useState<number | null>(null);
    const [playerXPReward, setPlayerXpReward] = useState<number | null>(null);
    const [respectReward, setRespectReward] = useState<number | null>(null);
    const [targetRespectChange, setTargetRespectChange] = useState<number | null>(null);
    const [playerEssenceReward, setPlayerEssenceReward] = useState<number | null>(null);
    const navigate = useNavigate();
    const queryClient = useQueryClient();
    const { justJailed } = useNormalStore();
    const postBattleActionMutation = usePostBattleAction();

    useEffect(() => {
        if (flee && battleType === "pve") {
            queryClient.invalidateQueries({ queryKey: [APIROUTES.USER.CURRENTUSERINFO] });
            queryClient.setQueryData(APIROUTES.ROGUELIKE.CURRENTMAP, null);
        }
    }, [flee, queryClient, battleType]);

    const handlePostBattleAction = async (action: PostBattleAction) => {
        const result = await postBattleActionMutation.mutateAsync({ action });
        if (result) {
            setActionSelected(action);
            if (action === "mug") {
                setMugAmount(result.mugAmount);
            }
            setPlayerXpReward(result.xpReward);
            if (result.essenceReward && result.essenceReward > 0) {
                setPlayerEssenceReward(result.essenceReward);
            }
            if (result.respectReward !== null) {
                setRespectReward(result.respectReward);
            }
            if (result.targetRespectChange !== null) {
                setTargetRespectChange(result.targetRespectChange);
            }

            queryClient.invalidateQueries({ queryKey: [APIROUTES.USER.CURRENTUSERINFO] });
        }
    };

    const selectedActionText = () => {
        if (actionSelected === "mug") {
            return `You mugged ${enemy} and stole `;
        }
        if (actionSelected === "cripple") {
            return `You beat ${enemy} to a pulp and left them in the street!`;
        }
        if (actionSelected === "leave") {
            return `You left ${enemy} alone to lick their wounds.`;
        }
    };

    const returnButtonText = () => {
        let buttonText = "Return";
        if (battleType === "pve-rooftop") {
            buttonText += "to Rooftop";
        } else if (battleType === "pve") {
            buttonText += "to Streets";
        }
        return buttonText;
    };

    const returnButtonPath = () => {
        if (battleType === "pve-rooftop") {
            return "/rooftop";
        } else if (battleType === "pve") {
            return "/streets";
        } else if (battleType === "pve-explore") {
            return "/explore";
        }
        return "/home";
    };

    const fleeButton = async () => {
        const returnPath = returnButtonPath();

        if (battleType === "pve-rooftop") {
            await queryClient.invalidateQueries({ queryKey: APIROUTES.ROOFTOP.NPCLIST });
        }
        return navigate(returnPath);
    };

    if (flee) {
        return (
            <div className="flex flex-col">
                <div className="flex flex-col md:my-8">
                    <p className="mx-auto text-lg dark:text-gray-200">You got away from the battle!</p>
                    <div className="flex w-full items-center justify-center">
                        <Button
                            variant="primary"
                            className="min-w-60! mt-6 3xl:w-1/4 md:mb-2"
                            onClick={() => fleeButton()}
                        >
                            {returnButtonText()}
                        </Button>
                    </div>
                </div>
            </div>
        );
    }

    if (isNPC) {
        return (
            <div className="flex flex-col">
                <div className="flex flex-col md:my-8">
                    <p className="mx-auto mb-2 text-xl dark:text-blue-500">You won!</p>
                    {/* <p className="mx-auto mt-2 dark:text-gray-200">
            You gained <span className="text-indigo-500">{enemy?.xpReward} EXP</span> for
            winning.
          </p>
          {enemy?.droppedItem !== null && (
            <p className="mx-auto text-gray-200 flex gap-1 mt-1">
              The enemy dropped:{" "}
              <span className="text-custom-yellow">{enemy?.droppedItem?.name}</span>
              <DisplayItem
                className="mr-1 ml-1 inline-block"
                height="h-6! w-6"
                item={enemy?.droppedItem}
              />
            </p>
          )}
          {enemy?.crateReward !== null && (
            <p className="mx-auto text-gray-200 flex gap-1 mt-1">
              {enemy?.droppedItem !== null ? "+" : "The enemy dropped:"}
              <span className="text-custom-yellow">{enemy?.crateReward?.name}</span>
              <DisplayItem
                className="mr-1 ml-1 inline-block"
                height="h-6! w-6"
                item={enemy?.crateReward}
              />
            </p>
          )} */}

                    <div className="flex w-full">
                        <button
                            className="mx-auto mt-6 mb-2 flex h-12 w-1/2 items-center justify-center rounded-md border border-gray-700 bg-blue-700 px-4 py-2 font-medium text-gray-200 text-sm shadow-xs hover:bg-blue-800"
                            onClick={() => navigate(returnButtonPath())}
                        >
                            {returnButtonText()}
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="flex flex-col dark:text-slate-200">
            {actionSelected ? (
                <div className="flex flex-col md:my-8">
                    <div className="mx-4 flex flex-col rounded-lg border-2 border-indigo-600 bg-slate-900 p-2 text-stroke-sm md:mx-auto md:w-fit md:px-4 lg:px-16">
                        <p className="mx-auto text-center">
                            {selectedActionText()}
                            {actionSelected === "mug" && (
                                <span>
                                    <span className="text-green-500">¥{mugAmount}</span>
                                </span>
                            )}
                        </p>
                        <p className="mx-auto mt-2">
                            You gained <span className="text-blue-500">{playerXPReward} EXP</span>.
                        </p>
                        {playerEssenceReward && playerEssenceReward > 0 && (
                            <p className="mx-auto mt-2">
                                You gained{" "}
                                <span className="text-indigo-500">
                                    {playerEssenceReward}{" "}
                                    <img
                                        src={lifeEssenceImg}
                                        alt="Life Essence"
                                        className="mx-0.5 mb-0.5 inline h-4 w-auto"
                                    />{" "}
                                    Life Essence.
                                </span>
                            </p>
                        )}
                    </div>
                    {respectReward !== null || targetRespectChange !== null ? (
                        <div className="mx-4 mt-4 flex flex-col rounded-lg border-2 border-indigo-600 bg-slate-900 p-2 text-stroke-sm md:mx-auto md:w-fit md:px-4 lg:px-16">
                            {respectReward !== null && (
                                <p className="mx-auto">
                                    Your Gang:{" "}
                                    <span className={cn(respectReward > 0 ? "text-green-500" : "text-red-500", "ml-2")}>
                                        {respectReward > 0 ? "+" : ""}
                                        {respectReward}{" "}
                                        <img
                                            src={respectImg}
                                            alt="Respect"
                                            className="mx-0.5 mb-0.5 inline h-4 w-auto"
                                        />{" "}
                                        Respect.
                                    </span>
                                </p>
                            )}
                            {targetRespectChange !== null && (
                                <p className="mx-auto mt-2">
                                    Enemy Gang:{" "}
                                    <span className="ml-2 text-red-500">
                                        {targetRespectChange}{" "}
                                        <img
                                            src={respectImg}
                                            alt="Respect"
                                            className="mx-0.5 mb-0.5 inline h-4 w-auto"
                                        />{" "}
                                        Respect.
                                    </span>
                                </p>
                            )}
                        </div>
                    ) : null}
                    <div className="flex w-full">
                        {justJailed ? (
                            <button
                                className="mx-auto mt-6 mb-2 flex h-12 w-1/2 items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 text-sm shadow-xs hover:bg-gray-50 dark:border-gray-500/50 dark:bg-blue-800 dark:text-slate-200 dark:hover:bg-blue-900"
                                onClick={() => navigate("/jail")}
                            >
                                To Jail.
                            </button>
                        ) : (
                            <button
                                className="mx-auto mt-6 mb-2 flex h-12 w-1/2 items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 text-sm text-stroke-sm shadow-xs hover:bg-gray-50 dark:border-blue-800 dark:bg-blue-800 dark:text-slate-200 dark:hover:bg-blue-700"
                                onClick={() => navigate("/home")}
                            >
                                Return Home.
                            </button>
                        )}
                    </div>
                </div>
            ) : (
                <>
                    <p className="mx-auto my-1">Victory!</p>
                    <p className="mx-auto my-2">What do you want to do with this Student?</p>
                    <div className="flex flex-col px-4">
                        <VictoryOptionBtn
                            text="Mug"
                            description="Steal ¥ from enemy"
                            onClick={() => handlePostBattleAction("mug")}
                        />

                        <VictoryOptionBtn
                            text="Cripple"
                            description="More Injury Inflicted"
                            onClick={() => handlePostBattleAction("cripple")}
                        />
                        <VictoryOptionBtn
                            text="Leave"
                            description="Bonus EXP"
                            onClick={() => handlePostBattleAction("leave")}
                        />
                    </div>
                </>
            )}
        </div>
    );
}
