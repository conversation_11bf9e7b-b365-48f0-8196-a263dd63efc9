import { db } from "../lib/db.js";
export const findAllJobs = async () => {
    return await db.job.findMany();
};
export const findJobById = async (jobId) => {
    return await db.job.findUnique({
        where: { id: jobId },
    });
};
export const updateUserJob = async (user, jobId, jobLevel) => {
    return await db.user.update({
        where: { id: user.id },
        data: {
            jobId,
            jobLevel,
        },
    });
};
export const updateUserJobLevel = async (user, jobLevel) => {
    return await db.user.update({
        where: { id: user.id },
        data: {
            jobLevel,
        },
    });
};
export const updateUserJobPayoutTime = async (user, payoutHour, blockNextPayout) => {
    return await db.user.update({
        where: { id: user.id },
        data: {
            jobPayoutHour: payoutHour,
            blockNextJobPayout: blockNextPayout,
        },
    });
};
