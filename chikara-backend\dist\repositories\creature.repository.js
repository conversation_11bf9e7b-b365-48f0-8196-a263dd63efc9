import { db } from "../lib/db.js";
import { QuestObjectiveTypes } from "../types/quest.js";
import { QuestProgressStatus } from "@prisma/client";
export const findAllCreatures = async () => {
    return await db.creature.findMany();
};
export const createNewCreature = async (creatureData) => {
    if (!creatureData.name) {
        throw new Error("Missing creature name");
    }
    return await db.creature.create({
        data: creatureData,
    });
};
export const findCreatureById = async (id) => {
    return await db.creature.findUnique({
        where: { id },
    });
};
export const updateCreature = async (id, updateData) => {
    const creature = await findCreatureById(id);
    if (!creature) {
        throw new Error("Creature not found");
    }
    const updateableParams = [
        "name",
        "image",
        "minFloor",
        "maxFloor",
        "boss",
        "health",
        "strength",
        "defence",
        "weaponDamage",
        "location",
        "statType",
        "currentHealth",
    ];
    const updateValues = {};
    for (const param of updateableParams) {
        if (updateData[param] !== undefined) {
            updateValues[param] = updateData[param];
        }
    }
    return await db.creature.update({
        where: { id },
        data: updateValues,
    });
};
export const deleteCreature = async (id) => {
    return await db.creature.delete({
        where: { id },
    });
};
export const findQuestEnemy = async (userId, location, floor, isBoss) => {
    const questProgress = await db.quest_progress.findFirst({
        where: {
            userId,
            questStatus: QuestProgressStatus.in_progress,
        },
        include: {
            quest: {
                include: {
                    quest_objective: {
                        where: {
                            objectiveType: QuestObjectiveTypes.DEFEAT_NPC,
                            location,
                        },
                    },
                },
            },
        },
    });
    if (!questProgress || !questProgress.quest || questProgress.quest.quest_objective.length === 0) {
        return null;
    }
    const objective = questProgress.quest.quest_objective[0];
    if (!objective.target) {
        return null;
    }
    return await db.creature.findFirst({
        where: {
            id: objective.target,
            minFloor: {
                lte: floor,
            },
            maxFloor: {
                gte: floor,
            },
            boss: isBoss,
        },
    });
};
export const findRandomEnemy = async (floor, location, isBoss) => {
    const creatures = await db.creature.findMany({
        where: {
            AND: {
                boss: isBoss,
                minFloor: {
                    lte: floor,
                },
                maxFloor: {
                    gte: floor,
                },
                location: {
                    in: [location, "any"],
                },
            },
        },
    });
    if (creatures.length === 0) {
        return null;
    }
    const randomIndex = Math.floor(Math.random() * creatures.length);
    return creatures[randomIndex];
};
