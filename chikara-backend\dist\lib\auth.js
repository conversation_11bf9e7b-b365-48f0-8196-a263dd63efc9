import { redisClient } from "../config/redisClient.js";
import { logAction } from "./actionLogger.js";
import { db } from "./db.js";
import { PasswordResetEmail } from "../utils/email.js";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { createAuthMiddleware } from "better-auth/api";
import { to<PERSON>ode<PERSON><PERSON><PERSON> } from "better-auth/node";
import { admin, openAPI } from "better-auth/plugins";
const prisma = db;
let trustedOrigins = ["*"];
if (process.env.NODE_ENV === "staging") {
    trustedOrigins = ["https://test.battleacademy.io"];
}
if (process.env.NODE_ENV === "production") {
    trustedOrigins = ["https://app.battleacademy.io"];
}
const appBaseUrl = process.env.APP_BASE_URL || "https://api.battleacademy.io";
export const auth = betterAuth({
    appName: "Chikara Academy",
    baseURL: appBaseUrl,
    basePath: "/auth",
    plugins: [
        openAPI(),
        admin({
            defaultRole: "student",
            schema: {
                user: {
                    fields: {
                        role: "userType",
                    },
                },
            },
        }),
    ],
    socialProviders: {
        discord: {
            clientId: process.env.DISCORD_CLIENT_ID,
            clientSecret: process.env.DISCORD_CLIENT_SECRET,
        },
    },
    emailAndPassword: {
        enabled: true,
        autoSignIn: true,
        sendResetPassword: async ({ user, url, token }) => {
            await PasswordResetEmail({
                email: user.email,
                resetUrl: url,
                resetToken: token,
            });
        },
    },
    user: {
        fields: {
            name: "username",
            image: "avatar",
        },
        changeEmail: {
            enabled: true,
        },
    },
    database: prismaAdapter(prisma, {
        provider: "mysql",
    }),
    session: {
        expiresIn: 60 * 60 * 24 * 365,
        updateAge: 60 * 60 * 24,
    },
    secondaryStorage: {
        get: async (key) => (await redisClient.get(`chikara-backend-session:${key}`)),
        set: async (key, value, ttl) => {
            await (ttl
                ? redisClient.set(`chikara-backend-session:${key}`, value, { EX: ttl })
                : redisClient.set(`chikara-backend-session:${key}`, value));
        },
        delete: async (key) => {
            await redisClient.del(`chikara-backend-session:${key}`);
            return null;
        },
    },
    databaseHooks: {
        account: {
            create: {
                before: async (account) => {
                    return {
                        data: {
                            ...account,
                            accountId: String(account.accountId),
                        },
                    };
                },
                after: async (account) => {
                    if (account.providerId === "discord" || account.providerId === "google") {
                        await prisma.user.update({
                            where: {
                                id: Number.parseInt(account.userId),
                            },
                            data: {
                                usernameSet: false,
                            },
                        });
                    }
                },
            },
        },
    },
    hooks: {
        after: createAuthMiddleware(async (ctx) => {
            if (ctx.path.startsWith("/sign-in")) {
                const newSession = ctx.context.newSession;
                if (newSession) {
                    await logAction({
                        action: "LOGIN",
                        userId: newSession.session.userId,
                        session: newSession.session,
                    });
                }
            }
        }),
    },
    advanced: {
        database: { useNumberId: true, generateId: false },
        defaultCookieAttributes: {
            sameSite: "none",
            secure: true,
        },
        crossSubDomainCookies: {
            enabled: process.env.NODE_ENV !== "development",
            domain: "battleacademy.io",
        },
    },
    trustedOrigins: trustedOrigins,
    crossSubDomain: true,
});
export const setupAuthRoutes = (app) => {
    app.all("/auth/*name", toNodeHandler(auth));
};
