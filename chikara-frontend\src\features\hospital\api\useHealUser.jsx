import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useHealUser = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        orpc.infirmary.revivePlayer.mutationOptions({
            onSuccess: () => {
                toast.success(`You healed the user!`);
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.INFIRMARY.INJUREDLIST,
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );

    return {
        healUser: mutation,
    };
};

export default useHealUser;
