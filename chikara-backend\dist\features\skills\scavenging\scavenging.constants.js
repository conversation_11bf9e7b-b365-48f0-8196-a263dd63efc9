export const POTENTIAL_RESOURCES = [
    { itemId: 1, width: 1, height: 2, weight: 25 },
    { itemId: 2, width: 1, height: 2, weight: 25 },
    { itemId: 3, width: 3, height: 1, weight: 25 },
    { itemId: 4, width: 2, height: 1, weight: 15 },
    { itemId: 5, width: 1, height: 1, weight: 8 },
    { itemId: 6, width: 2, height: 2, weight: 5 },
    { itemId: 42, width: 3, height: 2, weight: 5 },
    { itemId: 88, width: 2, height: 3, weight: 5 },
    { itemId: 25, width: 1, height: 2, weight: 5 },
    { itemId: 37, width: 1, height: 3, weight: 5 },
];
export const DIFFICULTY_TIERS = {
    1: { gridSize: 4, minResources: 1, maxResources: 2 },
    2: { gridSize: 4, minResources: 2, maxResources: 3 },
    3: { gridSize: 5, minResources: 2, maxResources: 4 },
    4: { gridSize: 5, minResources: 3, maxResources: 5 },
    5: { gridSize: 6, minResources: 3, maxResources: 6 },
    6: { gridSize: 6, minResources: 4, maxResources: 7 },
    7: { gridSize: 7, minResources: 4, maxResources: 8 },
    8: { gridSize: 7, minResources: 5, maxResources: 9 },
    9: { gridSize: 8, minResources: 6, maxResources: 10 },
    10: { gridSize: 9, minResources: 7, maxResources: 12 },
    11: { gridSize: 10, minResources: 8, maxResources: 14 },
    12: { gridSize: 10, minResources: 10, maxResources: 16 },
};
export const DEFAULT_ENERGY = 15;
export const MAX_DIFFICULTY_TIER = 12;
export const MIN_DIFFICULTY_TIER = 1;
export const MAX_GRID_SIZE = 10;
export const MIN_GRID_SIZE = 4;
