import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as PropertyController from "./property.controller.js";
import propertySchema from "./property.validation.js";
export const propertyRouter = {
    getHousingList: isLoggedInAuth.handler(async () => {
        const data = await PropertyController.getHousingList();
        return handleResponse({ data });
    }),
    getUserProperties: isLoggedInAuth.handler(async ({ context }) => {
        const result = await PropertyController.getUserProperties(context.user.id);
        return handleResponse(result);
    }),
    purchaseProperty: canMakeStateChangesAuth
        .input(propertySchema.purchaseProperty)
        .handler(async ({ input, context }) => {
        const result = await PropertyController.purchaseProperty(input.propertyId, context.user.id);
        return handleResponse(result);
    }),
    sellProperty: canMakeStateChangesAuth.input(propertySchema.sellProperty).handler(async ({ input, context }) => {
        const result = await PropertyController.sellProperty(input.propertyId, context.user.id);
        return handleResponse(result);
    }),
    setPrimaryProperty: canMakeStateChangesAuth
        .input(propertySchema.setPrimaryProperty)
        .handler(async ({ input, context }) => {
        const result = await PropertyController.setPrimaryProperty(input.propertyId, context.user.id);
        return handleResponse(result);
    }),
};
