import { db } from "../lib/db.js";
import { Prisma } from "@prisma/client";
export const getMissionsByDate = async (date) => {
    return await db.daily_mission
        .findMany({
        where: { missionDate: date },
        include: {
            item: true,
        },
    })
        .then((missions) => missions.map((mission) => ({
        ...mission,
        itemReward: mission.item,
        item: undefined,
    })));
};
export const getMissionById = async (id) => {
    return await db.daily_mission.findUnique({
        where: { id },
        include: {
            item: true,
        },
    });
};
export const getMissionByIdAndDate = async (id, date) => {
    return await db.daily_mission.findFirst({
        where: {
            id,
            missionDate: date,
        },
        include: {
            item: true,
        },
    });
};
export const updateUser = async (user) => {
    const { id, roguelikeMap, defeatedNpcs, ...userData } = user;
    return await db.user.update({
        where: { id },
        data: {
            ...userData,
            roguelikeMap: roguelikeMap ?? Prisma.DbNull,
            defeatedNpcs: defeatedNpcs ?? Prisma.DbNull,
        },
    });
};
export const updateUserMissionStart = async (user, missionId, missionEndTime) => {
    return await db.user.update({
        where: { id: user.id },
        data: {
            currentMission: missionId,
            missionEnds: BigInt(missionEndTime),
        },
    });
};
export const updateUserMissionCancel = async (user) => {
    return await db.user.update({
        where: { id: user.id },
        data: {
            currentMission: null,
            missionEnds: null,
        },
    });
};
