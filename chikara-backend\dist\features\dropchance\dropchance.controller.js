import * as dropChanceRepository from "../../repositories/dropchance.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";
export const getDropTables = async () => {
    const drops = await dropChanceRepository.findAllDrops();
    return { data: drops };
};
export const createDropChance = async function (body) {
    const itemId = body.itemId;
    const dropRate = body.dropRate;
    const dropChanceType = body.dropChanceType;
    const location = body.location;
    const minLevel = body.minLevel;
    const maxLevel = body.maxLevel;
    const scavengeType = body.scavengeType;
    const quantity = body.quantity;
    if (!itemId || !(await ItemRepository.findItemById(itemId))) {
        return { error: "Item does not exist" };
    }
    if (dropRate < 0 || dropRate > 1) {
        return { error: "Invalid drop rate" };
    }
    if (!dropChanceRepository.getAllDropChanceTypes().includes(dropChanceType)) {
        return { error: "Invalid drop chance type" };
    }
    const data = {
        itemId: itemId,
        dropRate: dropRate,
        dropChanceType: dropChanceType,
        location: location || "any",
        quantity: quantity || 1,
        minLevel,
        maxLevel,
        scavengeType,
    };
    const dropChance = await dropChanceRepository.createDropChance(data);
    return { data: dropChance };
};
export const editDropChance = async function (body) {
    const id = body.id;
    const itemId = body.itemId;
    const dropRate = body.dropRate;
    const dropChanceType = body.dropChanceType;
    const location = body.location;
    const minLevel = body.minLevel;
    const maxLevel = body.maxLevel;
    const scavengeType = body.scavengeType;
    const quantity = body.quantity;
    if (dropRate < 0 || dropRate > 1) {
        return { error: "Invalid drop rate" };
    }
    if (!dropChanceRepository.getAllDropChanceTypes().includes(dropChanceType)) {
        return { error: "Invalid drop chance type" };
    }
    if (!itemId || !(await ItemRepository.findItemById(itemId))) {
        return { error: "Item does not exist" };
    }
    const dropChance = await dropChanceRepository.findDropChanceById(id);
    if (!dropChance) {
        return { error: "Drop chance does not exist" };
    }
    const updateData = {
        itemId,
        dropRate,
        dropChanceType,
    };
    if (location) {
        updateData.location = location;
    }
    if (minLevel) {
        updateData.minLevel = minLevel;
    }
    if (maxLevel) {
        updateData.maxLevel = maxLevel;
    }
    if (scavengeType != "null") {
        updateData.scavengeType = scavengeType;
    }
    if (quantity) {
        updateData.quantity = quantity;
    }
    const updatedDropChance = await dropChanceRepository.updateDropChance(dropChance, updateData);
    return { data: updatedDropChance };
};
export const deleteDropChance = async (id) => {
    const count = await dropChanceRepository.deleteDropChanceById(id);
    return { data: "DropChances deleted: " + count };
};
