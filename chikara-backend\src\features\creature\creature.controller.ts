import * as CreatureRepository from "../../repositories/creature.repository.js";
import { CreatureModel } from "../../lib/db.js";

export const creatureList = async () => {
    const creatures = await CreatureRepository.findAllCreatures();
    return { data: creatures };
};

export const createCreature = async (creatureData: CreatureModel) => {
    const creature = await CreatureRepository.createNewCreature(creatureData);
    return { data: creature };
};

export const editCreature = async (id: number, updateData: Partial<CreatureModel>) => {
    const updatedCreature = await CreatureRepository.updateCreature(id, updateData);
    return { data: updatedCreature };
};

export const deleteCreature = async (id: number) => {
    await CreatureRepository.deleteCreature(id);
    return { data: `Creature deleted` };
};
