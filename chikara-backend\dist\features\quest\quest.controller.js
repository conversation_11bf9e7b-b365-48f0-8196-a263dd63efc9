import * as FocusService from "../../core/focus.service.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as QuestHelper from "./quest.helpers.js";
import * as QuestRepository from "../../repositories/quest.repository.js";
import * as UserRepository from "../../repositories/user.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { QuestObjectiveTypes } from "../../types/quest.js";
import { LogErrorStack } from "../../utils/log.js";
import { QuestProgressStatus } from "@prisma/client";
export async function GetUserQuestProgress(userId, activeOnly) {
    try {
        const quests = await QuestRepository.findQuestProgressWithQuest(userId, activeOnly);
        return { data: quests };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to get quest progress list:", error });
        return { error: "Failed to get quest progress list", statusCode: 400 };
    }
}
export async function GetCombinedQuestList(user) {
    try {
        const fullQuestDetails = await QuestRepository.getFullQuestDetails(user.id, user.level);
        return { data: fullQuestDetails };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to get combined quest list:", error });
        return { error: "Failed to get combined quest list", statusCode: 400 };
    }
}
export async function GetActiveQuestList(user) {
    try {
        const activeQuests = await QuestRepository.getActiveQuestsForUser(user.id, user.level);
        return { data: activeQuests };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to get active quest list:", error });
        return { error: "Failed to get active quest list", statusCode: 400 };
    }
}
export async function GetCompletedQuestList(user) {
    try {
        const completedQuests = await QuestRepository.getCompletedQuestsForUser(user.id);
        return { data: completedQuests };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to get completed quest list:", error });
        return { error: "Failed to get completed quest list", statusCode: 400 };
    }
}
export async function GetStoryQuestList(user) {
    try {
        const storyQuests = await QuestRepository.getFullQuestDetails(user.id, user.level, true);
        return { data: storyQuests };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to get story quest list:", error });
        return { error: "Failed to get story quest list", statusCode: 400 };
    }
}
export async function GetAvailableQuests(user) {
    try {
        const availableQuests = await QuestRepository.getAvailableQuestsForUser(user.id, user.level);
        return { data: availableQuests };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to get available quests:", error });
        return { error: "Failed to get available quests", statusCode: 400 };
    }
}
export async function StartQuest(userId, questId) {
    try {
        const quest = await QuestRepository.getQuestById(questId);
        if (!quest) {
            return { error: "Invalid quest", statusCode: 400 };
        }
        const currentUser = await UserRepository.getUserById(userId);
        if (!currentUser) {
            return { error: "User not found", statusCode: 404 };
        }
        const canStartResult = await QuestHelper.UserCanStartQuest(currentUser, quest);
        if ("error" in canStartResult) {
            return { error: canStartResult.error, statusCode: 400 };
        }
        const progress = await QuestRepository.createQuestProgress(currentUser.id, quest.id, QuestProgressStatus.in_progress);
        const objectiveProgress = [];
        for (const objective of quest.quest_objective) {
            const count = 0;
            const status = QuestProgressStatus.in_progress;
            objectiveProgress.push(await QuestRepository.createQuestObjectiveProgress(currentUser.id, objective.id, count, status));
        }
        logAction({
            action: "QUEST_STARTED",
            userId: userId,
            info: {
                questId: quest.id,
                questName: quest.name,
            },
        });
        return { data: { ...progress, objectiveProgress } };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to start quest:", error });
        return { error: "Failed to start quest", statusCode: 400 };
    }
}
export async function CompleteQuest(userId, questId) {
    try {
        const quest = await QuestRepository.getQuestById(questId);
        if (!quest) {
            return { error: "Invalid quest", statusCode: 400 };
        }
        if (quest.disabled) {
            return { error: "Quest disabled", statusCode: 400 };
        }
        const currentUser = await UserRepository.getUserById(userId);
        if (!currentUser) {
            return { error: "User not found", statusCode: 404 };
        }
        const requirementsResult = await QuestHelper.checkQuestRequirements(currentUser, quest);
        if ("error" in requirementsResult) {
            return { error: requirementsResult.error, statusCode: 400 };
        }
        const questProgress = await QuestRepository.getUserQuestProgress(currentUser.id, quest.id);
        if (!questProgress) {
            return { error: "Quest progress not found", statusCode: 404 };
        }
        if (questProgress.questStatus !== QuestProgressStatus.in_progress &&
            questProgress.questStatus !== QuestProgressStatus.ready_to_complete) {
            return { error: "Quest not in progress", statusCode: 400 };
        }
        if (questProgress.questStatus === QuestProgressStatus.in_progress) {
            const objectiveProgress = await QuestRepository.findRequiredUserQuestObjectiveProgressForQuest(currentUser.id, quest.id);
            const incompleteObjectives = objectiveProgress.filter((progress) => progress.status !== QuestProgressStatus.complete);
            if (incompleteObjectives.length > 0) {
                return { error: "Incomplete objectives", statusCode: 400 };
            }
        }
        await QuestRepository.updateQuestProgress(questProgress.id, {
            questStatus: QuestProgressStatus.complete,
        });
        await QuestHelper.ApplyQuestCompletion(quest, currentUser);
        await FocusService.addQuestCompleteFocus(currentUser.id);
        logAction({
            action: "QUEST_COMPLETED",
            userId: userId,
            info: {
                questId: quest.id,
                questName: quest.name,
            },
        });
        if (quest.isStoryQuest) {
            await AutoStartNextStoryQuest(userId, quest.id);
        }
        return { data: questProgress };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to complete quest:", error });
        return { error: "Failed to complete quest", statusCode: 400 };
    }
}
export async function HandInItem(userId, objectiveId, itemId) {
    try {
        const objective = await QuestRepository.getQuestObjectiveById(objectiveId);
        if (!objective) {
            return { error: "Objective not found", statusCode: 404 };
        }
        if (objective.objectiveType !== QuestObjectiveTypes.DELIVER_ITEM) {
            return { error: "Not an item hand-in objective", statusCode: 400 };
        }
        if (objective.itemId !== itemId) {
            return { error: "Wrong item for this objective", statusCode: 400 };
        }
        if (!objective.quantity || objective.quantity <= 0) {
            return { error: "Invalid quantity required", statusCode: 400 };
        }
        let objectiveProgress = await QuestRepository.findQuestObjectiveProgressForUser(userId, objectiveId);
        if (!objectiveProgress) {
            objectiveProgress = await QuestRepository.createQuestObjectiveProgress(userId, objectiveId, 0, QuestProgressStatus.in_progress);
        }
        if (objectiveProgress.status !== QuestProgressStatus.in_progress) {
            return { error: "Objective is not in progress", statusCode: 400 };
        }
        const hasEnoughItems = await InventoryService.UserHasNumberOfItem(userId, itemId, objective.quantity);
        if (!hasEnoughItems) {
            return {
                error: `Not enough items. Required: ${objective.quantity}`,
                statusCode: 400,
            };
        }
        await InventoryService.SubtractItemFromUser({
            userId,
            itemId,
            amount: objective.quantity,
        });
        const updated = await QuestHelper.CompleteItemHandInObjective(objectiveProgress.id, objective.quantity);
        if (!updated) {
            return { error: "Failed to update objective", statusCode: 500 };
        }
        logAction({
            action: "QUEST_ITEM_HAND_IN",
            userId,
            info: {
                objectiveId: objective.id,
                itemId,
                quantity: objective.quantity,
            },
        });
        return {
            data: {
                message: "Items handed in successfully",
                objectiveCompleted: true,
            },
        };
    }
    catch (error) {
        LogErrorStack({ message: "Failed to hand in item:", error });
        return { error: "Failed to hand in item", statusCode: 400 };
    }
}
export async function AutoStartNextStoryQuest(userId, completedQuestId) {
    try {
        const completedQuest = await QuestRepository.getQuestById(completedQuestId);
        if (!completedQuest ||
            !completedQuest.isStoryQuest ||
            !completedQuest.chapterId ||
            !completedQuest.orderInChapter) {
            return;
        }
        const nextQuest = await QuestRepository.getNextStoryQuestInChapter(completedQuest.chapterId, completedQuest.orderInChapter);
        if (!nextQuest) {
            return;
        }
        const user = await UserRepository.getUserById(userId);
        if (!user) {
            return;
        }
        const canStartResult = await QuestHelper.UserCanStartQuest(user, nextQuest);
        if ("error" in canStartResult) {
            return;
        }
        const startResult = await StartQuest(userId, nextQuest.id);
        if (startResult?.error) {
            LogErrorStack({
                message: `AutoStartNextStoryQuest: Failed to start quest '${nextQuest.name}' for user ${userId}`,
                error: startResult.error,
            });
            return;
        }
        logAction({
            action: "STORY_QUEST_AUTO_STARTED",
            userId: userId,
            info: {
                questId: nextQuest.id,
                questName: nextQuest.name,
                chapterId: completedQuest.chapterId,
                previousQuestId: completedQuestId,
            },
        });
    }
    catch (error) {
        LogErrorStack({
            message: `Failed to auto-start next story quest for user ${userId} after completing quest ${completedQuestId}`,
            error,
        });
    }
}
