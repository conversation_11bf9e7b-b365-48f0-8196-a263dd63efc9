import AchievementHelpers from "../../core/achievement.service.js";
import { getMissionById, getMissionByIdAndDate, getMissionsByDate, updateUserMissionCancel, updateUserMissionStart, } from "../../repositories/mission.repository.js";
import { getUserById } from "../../repositories/user.repository.js";
import * as ShrineHelpers from "../shrine/shrine.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import { getToday } from "../../utils/dateHelpers.js";
import { LogErrorStack } from "../../utils/log.js";
export const missionList = async () => {
    const today = getToday();
    const todaysMissions = await getMissionsByDate(today);
    const shrineBuffActive = await ShrineHelpers.dailyBuffIsActive("mission");
    if (shrineBuffActive) {
        for (const mission of todaysMissions) {
            mission.duration = Math.round(mission.duration * shrineBuffActive);
        }
    }
    return { data: todaysMissions };
};
export const currentMission = async (missionId) => {
    if (!missionId) {
        return { error: "Not on a mission!", statusCode: 400 };
    }
    const mission = await getMissionById(missionId);
    return { data: mission };
};
const isEligibleForMission = async (currentUser, mission) => {
    if (currentUser.level < mission.levelReq) {
        return false;
    }
    const totalMissionHours = await AchievementHelpers.GetUserAchievement(currentUser.id, "totalMissionHours");
    if (mission.hoursReq > 0 && (totalMissionHours || 0) < mission.hoursReq) {
        return false;
    }
    return true;
};
export const startMission = async (userId, missionId) => {
    try {
        if (!missionId) {
            return { error: "Mission ID is required", statusCode: 400 };
        }
        const today = getToday();
        const selectedMission = await getMissionByIdAndDate(missionId, today);
        if (!selectedMission) {
            return { error: "Mission not found", statusCode: 404 };
        }
        const currentUser = await getUserById(userId);
        if (!currentUser) {
            return { error: "User not found", statusCode: 404 };
        }
        if (currentUser.currentMission || currentUser.missionEnds) {
            return { error: "Already on a mission!", statusCode: 400 };
        }
        if (currentUser.hospitalisedUntil || currentUser.jailedUntil) {
            return { error: "Can't start a mission while incapacitated", statusCode: 400 };
        }
        if (!(await isEligibleForMission(currentUser, selectedMission))) {
            return { error: "Not Eligible", statusCode: 400 };
        }
        const shrineBuffActive = (await ShrineHelpers.dailyBuffIsActive("mission")) || 1;
        const missionEndTime = Date.now() + selectedMission.duration * shrineBuffActive;
        await updateUserMissionStart(currentUser, missionId, missionEndTime);
        logAction({
            action: "MISSION_STARTED",
            userId: userId,
            info: {
                missionId: selectedMission.id,
                missionName: selectedMission.missionName,
            },
        });
        return { data: "Mission started successfully" };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to start mission due to an internal error", statusCode: 500 };
    }
};
export const cancelMission = async (userId) => {
    try {
        const currentUser = await getUserById(userId);
        if (!currentUser) {
            return { error: "User not found", statusCode: 404 };
        }
        if (currentUser?.id === 14) {
            return { error: "Banned from mission cancelling", statusCode: 400 };
        }
        if (!currentUser.currentMission || !currentUser.missionEnds) {
            return { error: "Not on a mission!", statusCode: 400 };
        }
        const missionId = currentUser.currentMission;
        await updateUserMissionCancel(currentUser);
        logAction({
            action: "MISSION_CANCELLED",
            userId: userId,
            info: {
                missionId: missionId,
            },
        });
        return { data: "Mission cancelled successfully" };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to cancel mission due to an internal error", statusCode: 500 };
    }
};
