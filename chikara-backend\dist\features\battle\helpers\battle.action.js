import { GetDamage } from "./battle.damage.js";
import * as TalentHelper from "../../talents/talents.helpers.js";
import * as UniqueItemHelpers from "../../item/uniqueitem.helpers.js";
import { GetAmmoForUser, getEquippedItemDetails } from "./battle.equipment.js";
import { GetShieldBashDamage, GetSprayDamage, GetGiantKillingSlingshotDamage, GetHeadbuttDamage, ApplySelfHarmDamage, STATUS_ABILITIES, } from "../logic/battle.abilities.js";
import { getHealingImprovement, getImpactResistance } from "./battle.scaling.js";
import { battleConfig } from "../../../config/gameConfig.js";
import { HEADBUTT_ABILITY_NAME, HEAL_ABILITY_NAME, MAX_HP_HEAL_ABILITY_NAME, SELF_HARM_ABILITY_NAME, SHIELD_BASH_ABILITY_NAME, RELOAD_ABILITY_NAME, SPRAY_ABILITY_NAME, GIANT_KILLING_SLINGSHOT_ABILITY_NAME, ATTACK_TYPE_MELEE, ATTACK_TYPE_RANGED, } from "./battle.constants.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import { handleError } from "../../../utils/log.js";
const { FLEE_CHANCE } = battleConfig.hidden;
const processBasicAttack = async (user, target) => {
    return await GetDamage(user, target, ATTACK_TYPE_MELEE);
};
const processRangedAttack = async (user, target) => {
    if ((user.ammo ?? 0) <= 0) {
        return handleError("Not enough ammo", 400);
    }
    if (!getEquippedItemDetails(user, "ranged")) {
        return handleError("No ranged weapon equipped", 400);
    }
    user.ammo--;
    return await GetDamage(user, target, ATTACK_TYPE_RANGED);
};
const processAbilityAction = async (user, target, action) => {
    if (user.userType === "player") {
        const ability = user.abilities?.find((ab) => ab.name === action);
        if (!ability) {
            return handleError("Ability not equipped or available.", 400);
        }
        if (user.statusEffects["ability_lock"] || user.statusEffects["ability_lock_debuff"]) {
            return handleError("Ability locked!", 400);
        }
        let abilityEfficiencyActive = false;
        const abilityEfficiencyTalent = user.userType === "player"
            ? await TalentHelper.UserHasAbilityEfficiencyTalent(Number.parseInt(user.id))
            : null;
        if (abilityEfficiencyTalent) {
            const statusEffects = user.statusEffects;
            if (!statusEffects["lastCast"]) {
                statusEffects["lastCast"] = { name: "" };
            }
            if (!statusEffects["lastCastAmount"]) {
                statusEffects["lastCastAmount"] = { value: 0 };
            }
            if (statusEffects["lastCast"].name === action) {
                if (statusEffects["lastCastAmount"].value === 1) {
                    statusEffects["lastCastAmount"].value = 2;
                }
                else {
                    abilityEfficiencyActive = true;
                    delete statusEffects["lastCast"];
                    delete statusEffects["lastCastAmount"];
                }
            }
            else {
                statusEffects["lastCast"].name = action;
                statusEffects["lastCastAmount"].value = 1;
            }
            user.statusEffects = statusEffects;
        }
        const staminaCost = ability.staminaCost * (abilityEfficiencyActive ? (abilityEfficiencyTalent?.modifier ?? 1) : 1);
        if (staminaCost && staminaCost > user.currentStamina) {
            return handleError("Not enough stamina", 400);
        }
        user.currentStamina -= staminaCost;
    }
    else {
        const ability = user.abilities?.find((ab) => ab.name === action);
        if (!ability) {
            return handleError("Invalid ability", 400);
        }
        const baseCost = ability.staminaCost;
        if (baseCost && baseCost > user.currentStamina) {
            return handleError("Not enough stamina", 400);
        }
        user.currentStamina -= baseCost;
    }
    const abilityDmgDebuff = user.statusEffects["ability_damage_debuff"] || 0;
    const healthyCasterTalentActive = user.userType === "player" ? await TalentHelper.HealthyCasterTalentActiveForUser(user) : false;
    switch (action) {
        case HEADBUTT_ABILITY_NAME: {
            return GetHeadbuttDamage(target, healthyCasterTalentActive, abilityDmgDebuff, user.abilities);
        }
        case HEAL_ABILITY_NAME: {
            const healAbility = user.abilities?.find((ab) => ab.name === HEAL_ABILITY_NAME);
            if (!healAbility || !healAbility.currentModifier)
                return 0;
            applyPercentageHeal(user, healAbility.currentModifier);
            return 0;
        }
        case MAX_HP_HEAL_ABILITY_NAME: {
            const maxHealAbility = user.abilities?.find((ab) => ab.name === MAX_HP_HEAL_ABILITY_NAME);
            if (!maxHealAbility || !maxHealAbility.currentModifier)
                return 0;
            applyPercentageHeal(user, maxHealAbility.currentModifier);
            return 0;
        }
        case SHIELD_BASH_ABILITY_NAME: {
            return GetShieldBashDamage(user, healthyCasterTalentActive, abilityDmgDebuff, user.abilities);
        }
        case SELF_HARM_ABILITY_NAME: {
            ApplySelfHarmDamage(user, user.abilities);
            break;
        }
        case RELOAD_ABILITY_NAME: {
            user.ammo = await GetAmmoForUser(user.id, user.equipment);
            return 0;
        }
        case SPRAY_ABILITY_NAME: {
            const ammoToUse = user.ammo ?? 0;
            if (ammoToUse <= 0) {
                return handleError("Not enough ammo", 400);
            }
            if (!getEquippedItemDetails(user, "ranged")) {
                return handleError("No ranged weapon equipped", 400);
            }
            user.ammo = 0;
            return GetSprayDamage(user, target, ammoToUse, abilityDmgDebuff, user.abilities);
        }
        case GIANT_KILLING_SLINGSHOT_ABILITY_NAME: {
            return GetGiantKillingSlingshotDamage(target, healthyCasterTalentActive, abilityDmgDebuff, user.abilities);
        }
    }
    for (const ability of STATUS_ABILITIES) {
        if (ability.name === action) {
            const targetOfAbility = ability.target === "self" ? user : target;
            const statusEffects = targetOfAbility.statusEffects;
            let finalTurns = ability.turns;
            if (ability.target !== "self" && targetOfAbility.userType === "player") {
                const impactResistance = getImpactResistance(targetOfAbility.attributes.intelligence);
                finalTurns = Math.max(1, Math.round(ability.turns * (1 - impactResistance)));
            }
            statusEffects[ability.name] = { turns: finalTurns };
            targetOfAbility.statusEffects = statusEffects;
            return 0;
        }
    }
    return handleError("Invalid action", 400);
};
const calculateFleeChance = async (userId) => {
    let fleeChance = FLEE_CHANCE;
    const currentUser = await UserRepository.getUserById(Number.parseInt(userId));
    if (currentUser) {
        if (await UniqueItemHelpers.IsRunItemEquipped(currentUser)) {
            fleeChance += 0.2;
        }
        const cowardTalent = await TalentHelper.UserHasCowardTalent(Number.parseInt(userId));
        if (cowardTalent && typeof cowardTalent !== "boolean" && cowardTalent.modifier) {
            fleeChance += cowardTalent.modifier;
        }
    }
    return fleeChance;
};
const processFlee = async (user) => {
    if (user.userType !== "player") {
        return {
            damage: 0,
            metadata: {
                fleeAttempt: {
                    success: false,
                },
            },
        };
    }
    const fleeChance = await calculateFleeChance(user.id);
    const fleeSuccess = Math.random() < fleeChance;
    return {
        damage: 0,
        metadata: {
            fleeAttempt: {
                success: fleeSuccess,
            },
        },
    };
};
const applyPercentageHeal = (user, percentage) => {
    const healAmount = Math.floor(user.maxHealth * percentage);
    const healingImprovement = user.userType === "player" ? getHealingImprovement(user.attributes.vitality) : 1;
    const improvedHeal = Math.round(healAmount * healingImprovement);
    user.currentHealth = Math.min(user.maxHealth, user.currentHealth + improvedHeal);
};
const actionHandlers = {
    attack: (u, t) => processBasicAttack(u, t),
    ranged: (u, t) => processRangedAttack(u, t),
    flee: (u) => processFlee(u),
};
export const ProcessAction = async (user, target, action) => {
    const handler = actionHandlers[action];
    if (handler) {
        return await handler(user, target);
    }
    return await processAbilityAction(user, target, action);
};
