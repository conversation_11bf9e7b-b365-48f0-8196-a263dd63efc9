import { UTCDate } from "@date-fns/utc";
import { startOfDay } from "date-fns";
export { isSameDay } from "date-fns";
export const getNow = () => {
    return new UTCDate();
};
export const getEpochTime = () => {
    return Date.now();
};
export const getToday = () => {
    return startOfDay(new UTCDate());
};
export const getYesterday = () => {
    const today = getToday();
    today.setDate(today.getDate() - 1);
    return today;
};
export const getTomorrow = () => {
    const today = getToday();
    today.setDate(today.getDate() + 1);
    return today;
};
export const getSevenDaysAgo = () => {
    const today = getToday();
    today.setDate(today.getDate() - 7);
    return today;
};
export const get24HoursAgo = () => {
    const today = new UTCDate();
    today.setHours(today.getHours() - 24);
    return today;
};
export const hasExpired = (date) => {
    const now = getEpochTime();
    let compareTime;
    if (date instanceof Date) {
        compareTime = date.getTime();
        if (Number.isNaN(compareTime)) {
            return false;
        }
    }
    else {
        compareTime = Number(date);
    }
    return compareTime <= now;
};
