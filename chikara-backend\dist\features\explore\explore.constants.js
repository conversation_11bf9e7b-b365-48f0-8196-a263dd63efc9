export const LOCATION_NODE_COUNTS = {
    shibuya: 6,
    shinjuku: 7,
    bunkyo: 8,
    chiyoda: 9,
    minato: 10,
};
export const PLAYER_NODE_CONFIG = {
    DEFAULT_EXPIRATION_HOURS: 6,
    COMPLETION_EXPIRATION_MINUTES: 30,
};
export const DISABLED_NODE_TYPES = {
    SHOP: false,
    HOUSING: true,
    BATTLE: false,
    CHARACTER_ENCOUNTER: false,
    ACTION: true,
    CONDITION: true,
    CHOICE: true,
    STORY: false,
    MINING_NODE: false,
    SCAVENGE_NODE: false,
    FORAGING_NODE: false,
};
export const BASE_NODE_CONFIG = {
    BATTLE: {
        titles: [
            "Wild Encounter",
            "Hostile Territory",
            "Dangerous Path",
            "Combat Zone",
            "Predator's Den",
            "Battlefield Remnants",
            "Aggressive Wildlife",
            "Bandit Hideout",
            "Monster Lair",
            "Contested Ground",
            "Violent Alley",
            "Threat Zone",
        ],
        descriptions: [
            "A dangerous area where hostile creatures roam.",
            "You sense danger in this location.",
            "This area looks like trouble...",
            "Something lurking in the shadows.",
            "The air feels thick with tension and danger.",
            "Scratch marks and signs of recent conflict are evident.",
            "You hear growling and aggressive sounds nearby.",
            "This place reeks of violence and bloodshed.",
            "The ground is stained with evidence of past battles.",
            "Your instincts scream danger as you approach.",
            "Broken weapons and torn fabric litter the area.",
            "An ominous presence watches from the darkness.",
        ],
    },
    CHARACTER_ENCOUNTER: {
        titles: [
            "Wandering Traveler",
            "Mysterious Figure",
            "Local Resident",
            "Fellow Explorer",
            "Seasoned Veteran",
            "Curious Onlooker",
            "Helpful Stranger",
            "Information Broker",
            "Wise Elder",
            "Lost Soul",
            "Merchant Trader",
            "Skilled Artisan",
        ],
        descriptions: [
            "Someone is here who might have something to say.",
            "A person approaches you with interest.",
            "You encounter someone in this area.",
            "A friendly face in an unexpected place.",
            "An experienced individual notices your presence.",
            "Someone watches you with keen interest.",
            "A person waves and seems eager to talk.",
            "You spot someone who might have valuable information.",
            "An elderly person sits peacefully, full of stories.",
            "Someone appears confused and might need assistance.",
            "A trader has set up a small stall here.",
            "A craftsperson works diligently on their trade.",
        ],
    },
    MINING_NODE: {
        titles: [
            "Rich Ore Deposit",
            "Mining Site",
            "Mineral Vein",
            "Excavation Point",
            "Crystal Formation",
            "Quarry Remains",
            "Geological Wonder",
            "Precious Metal Cache",
            "Stone Outcrop",
            "Rare Earth Deposit",
            "Ancient Stratum",
            "Gemstone Cluster",
        ],
        descriptions: [
            "You spot valuable ore glinting in the rock.",
            "This area looks perfect for mining.",
            "A promising deposit of minerals.",
            "The ground here contains valuable resources.",
            "Crystalline formations catch the light beautifully.",
            "Old mining equipment suggests this was once productive.",
            "The rock face shows fascinating geological layers.",
            "Metallic veins run through the exposed stone.",
            "Solid rock formations reveal hidden treasures.",
            "Unusual minerals create colorful patterns in the stone.",
            "Ancient rock layers tell stories of deep time.",
            "Sparkling gems peek out from weathered cracks.",
        ],
    },
    SCAVENGE_NODE: {
        titles: [
            "Abandoned Cache",
            "Scavenge Site",
            "Hidden Stash",
            "Forgotten Supplies",
            "Deserted Camp",
            "Debris Field",
            "Lost Cargo",
            "Ruined Structure",
            "Discarded Goods",
            "Emergency Stockpile",
            "Wrecked Vehicle",
            "Overlooked Treasure",
        ],
        descriptions: [
            "Someone left useful items behind here.",
            "You might find something valuable in the debris.",
            "This area looks like it was once inhabited.",
            "Useful materials might be scattered around.",
            "An abandoned campsite with remnants of supplies.",
            "Scattered debris might contain something worthwhile.",
            "Dropped cargo containers could hold treasures.",
            "The ruins hide secrets and forgotten items.",
            "Discarded items might still have value.",
            "Emergency supplies were hastily abandoned here.",
            "A wrecked transport vehicle spills its contents.",
            "Most people would overlook the value here.",
        ],
    },
    FORAGING_NODE: {
        titles: [
            "Herb Garden",
            "Wild Growth",
            "Natural Bounty",
            "Foraging Spot",
            "Medicinal Grove",
            "Berry Thicket",
            "Mushroom Circle",
            "Flowering Meadow",
            "Fruit Grove",
            "Spice Garden",
            "Bamboo Stand",
            "Coastal Kelp",
        ],
        descriptions: [
            "Wild plants and herbs grow abundantly here.",
            "Nature provides its bounty in this area.",
            "You notice edible plants and useful materials.",
            "This area is rich with natural resources.",
            "Healing herbs grow in wild profusion here.",
            "Sweet berries hang heavy on the branches.",
            "Exotic mushrooms form mysterious fairy rings.",
            "Colorful flowers bloom with aromatic fragrances.",
            "Fruit trees offer their seasonal harvest.",
            "Aromatic spices and herbs perfume the air.",
            "Tall bamboo creates a natural sanctuary.",
            "Marine plants offer unique coastal resources.",
        ],
    },
};
export const LOCATION_NODE_WEIGHTS = {
    shibuya: {
        BATTLE: 40,
        CHARACTER_ENCOUNTER: 30,
        ACTION: 5,
        MINING_NODE: 5,
        SCAVENGE_NODE: 15,
        FORAGING_NODE: 5,
    },
    shinjuku: {
        BATTLE: 35,
        CHARACTER_ENCOUNTER: 25,
        ACTION: 10,
        MINING_NODE: 8,
        SCAVENGE_NODE: 20,
        FORAGING_NODE: 2,
    },
    bunkyo: {
        BATTLE: 20,
        CHARACTER_ENCOUNTER: 35,
        ACTION: 15,
        MINING_NODE: 5,
        SCAVENGE_NODE: 10,
        FORAGING_NODE: 15,
    },
    chiyoda: {
        BATTLE: 25,
        CHARACTER_ENCOUNTER: 20,
        ACTION: 20,
        MINING_NODE: 20,
        SCAVENGE_NODE: 10,
        FORAGING_NODE: 5,
    },
    minato: {
        BATTLE: 30,
        CHARACTER_ENCOUNTER: 15,
        ACTION: 10,
        MINING_NODE: 25,
        SCAVENGE_NODE: 15,
        FORAGING_NODE: 5,
    },
};
export const GRID_CONFIG = {
    SIZE: 5,
    MIN_COORD: 0,
    MAX_COORD: 4,
    TOTAL_POSITIONS: 25,
};
export const STATIC_NODE_RESERVED_POSITIONS = {
    shibuya: [
        { x: 0, y: 0 },
        { x: 4, y: 0 },
        { x: 2, y: 2 },
        { x: 0, y: 4 },
        { x: 4, y: 4 },
    ],
    shinjuku: [
        { x: 1, y: 0 },
        { x: 3, y: 0 },
        { x: 2, y: 2 },
        { x: 1, y: 4 },
        { x: 3, y: 4 },
    ],
    bunkyo: [
        { x: 0, y: 1 },
        { x: 4, y: 1 },
        { x: 2, y: 2 },
        { x: 0, y: 3 },
        { x: 4, y: 3 },
    ],
    chiyoda: [
        { x: 2, y: 0 },
        { x: 0, y: 2 },
        { x: 4, y: 2 },
        { x: 2, y: 4 },
        { x: 2, y: 1 },
    ],
    minato: [
        { x: 1, y: 1 },
        { x: 3, y: 1 },
        { x: 1, y: 3 },
        { x: 3, y: 3 },
        { x: 2, y: 2 },
    ],
};
export const MAP_CHANGE_COSTS = {
    shibuya: 100,
    shinjuku: 150,
    bunkyo: 200,
    chiyoda: 250,
    minato: 300,
};
export const TRAVEL_METHODS = {
    WALK: "walk",
    BUS: "bus",
};
export const TRAVEL_TIMES = {
    walk: {
        shibuya: 15,
        shinjuku: 20,
        bunkyo: 25,
        chiyoda: 30,
        minato: 35,
    },
    bus: {
        shibuya: 4,
        shinjuku: 5,
        bunkyo: 7,
        chiyoda: 8,
        minato: 9,
    },
};
export const TRAVEL_COSTS = {
    walk: {
        shibuya: 0,
        shinjuku: 0,
        bunkyo: 0,
        chiyoda: 0,
        minato: 0,
    },
    bus: MAP_CHANGE_COSTS,
};
