import { logAction } from "../../../../lib/actionLogger.js";
import { db } from "../../../../lib/db.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";
import { getAllUserStatLevels } from "../../../../features/user/user.helpers.js";
const getTotalStatPoints = async (userId) => {
    const userStats = await getAllUserStatLevels(userId);
    return userStats.strength + userStats.dexterity + userStats.defence + userStats.intelligence + userStats.endurance;
};
async function collectPlayerStats() {
    logger.profile("collectPlayerStats");
    try {
        const users = await db.user.findMany({
            select: {
                id: true,
                level: true,
                roguelikeHighscore: true,
                cash: true,
                bank_balance: true,
                user_item: {
                    select: {
                        id: true,
                        count: true,
                        item: {
                            select: {
                                cashValue: true,
                            },
                        },
                    },
                },
                user_achievements: {
                    select: {
                        roguelikeNodesCompleted: true,
                    },
                },
            },
        });
        for (const user of users) {
            const cashBalance = user.cash || 0;
            const bankBalance = user.bank_balance || 0;
            const totalBalance = cashBalance + bankBalance;
            const totalItemValues = user.user_item.reduce((total, item) => total + (item?.item?.cashValue || 0) * item.count, 0);
            const netWorth = totalBalance + totalItemValues;
            const totalStatPoints = await getTotalStatPoints(user.id);
            const currentLevel = user.level || 1;
            const currentRoguelikeHighscore = user.roguelikeHighscore;
            const yesterdaysGameStats = await db.game_stats.findFirst({
                where: {
                    playerId: user.id,
                    stats_type: "player_stats",
                },
                orderBy: {
                    createdAt: "desc",
                },
                take: 1,
            });
            const achievements = user.user_achievements || {};
            let statsGained24h = null;
            let nodesCompleted24h = null;
            let netWorthGained24h = null;
            let levelsGained24h = null;
            let roguelikeHighscoreGained24h = null;
            if (yesterdaysGameStats) {
                const yesterdaysStats = yesterdaysGameStats.info;
                statsGained24h = totalStatPoints - (yesterdaysStats?.totalStatPoints || 0);
                nodesCompleted24h =
                    (achievements.roguelikeNodesCompleted || 0) - (yesterdaysStats?.roguelikeNodesCompleted || 0);
                netWorthGained24h = netWorth - (yesterdaysStats?.netWorth || 0);
                levelsGained24h = currentLevel - (yesterdaysStats?.currentLevel || 0);
                roguelikeHighscoreGained24h = currentRoguelikeHighscore - (yesterdaysStats?.roguelikeHighscore || 0);
                if (yesterdaysStats?.netWorth > 0) {
                    const netWorthPercentageIncrease = ((netWorth - yesterdaysStats.netWorth) / yesterdaysStats.netWorth) * 100;
                    if (netWorthPercentageIncrease > 1000) {
                        logAction({
                            action: "DAILY_NET_WORTH_ANOMALY",
                            userId: user.id,
                            info: {
                                netWorthPercentageIncrease,
                            },
                        });
                    }
                }
                if (statsGained24h > 60) {
                    logAction({
                        action: "DAILY_STAT_ANOMALY",
                        userId: user.id,
                        info: {
                            statsGained24h,
                        },
                    });
                }
            }
            const stats = {
                cashBalance,
                bankBalance,
                totalItemValues,
                netWorth,
                currentLevel,
                totalStatPoints,
                statsGained24h,
                nodesCompleted24h,
                netWorthGained24h,
                levelsGained24h,
                roguelikeHighscoreGained24h,
            };
            await db.$executeRaw `
            INSERT INTO game_stats (playerId, stats_type, info, updatedAt, createdAt)
            VALUES (${user.id}, 'player_stats', ${JSON.stringify(stats)}, NOW(), NOW())
        `;
        }
        logAction({
            action: "DAILY_STATS_COLLECTED",
            userId: 1,
        });
    }
    catch (error) {
        LogErrorStack({ message: "Failed to process daily player stats:", error });
    }
    logger.profile("collectPlayerStats");
}
export default collectPlayerStats;
