function getScavengeLocation(scavengeChoices) {
    const locations = {
        trash_medical: {
            location: "Abandoned Clinic",
            description: "While scavenging, you come across an abandoned clinic in a back alley. Peeking inside, you see overflowing garbage bins and medical supplies scattered around. The clinic's exterior is covered in graffiti, and its interior contains old medical equipment and documents.",
            choices: {
                trash: {
                    action: "Scavenge through outdoor trash bins",
                    success: "Digging through the clinic's dumpster, you uncover some usable items beneath the refuse:",
                    failureInjury: "While lifting a heavy bag of medical waste, you feel a sharp pain in your side. You've bruised your ribs.",
                    injury: "fracture_injury",
                    failureJail: "A passing patrol spots you trespassing on private property. You're escorted to the local precinct for a 'chat'.",
                },
                medical: {
                    action: "Search for medical supplies inside",
                    success: "Amidst the cluttered shelves and drawers, you find some valuable medical supplies:",
                    failureInjury: "You trip over scattered debris, twisting your ankle badly as you fall.",
                    injury: "trauma_injury",
                    failureJail: "Alarms you didn't notice start blaring. Before you can escape, security arrives and detains you.",
                },
            },
        },
        trash_upgrade: {
            location: "Discarded Electronics Store",
            description: "While scavenging, you find an old electronics store that has been out of business for years. Piles of outdated gadgets and their packaging litter the floor. Among the trash, you notice components and tools that can be repurposed for upgrades.",
            choices: {
                trash: {
                    action: "Sort through piles of outdated gadgets",
                    success: "Sifting through the electronic junk, you salvage some useful items:",
                    failureInjury: "A jagged piece of metal slices your hand as you dig through the pile. The cut is deep and bleeding profusely.",
                    injury: "bleeding_injury",
                    failureJail: "The store owner catches you in the act. He calls the police, and you're taken in for attempted burglary.",
                },
                upgrade: {
                    action: "Look for repurposable components",
                    success: "You carefully extract some promising components from the old devices:",
                    failureInjury: "After hours of meticulous searching, you're overcome with minor fatigue, your vision blurring from exhaustion.",
                    injury: "fatigue_injury",
                    failureJail: "Your scavenging triggers a silent alarm. The police arrive and arrest you for trespassing and theft.",
                },
            },
        },
        trash_herb: {
            location: "Neglected Urban Garden",
            description: "While scavenging, you come across a neglected urban community garden. Trash bags and litter are strewn about, but amidst the neglect, wild herbs and plants still grow.",
            choices: {
                trash: {
                    action: "Check litter and trash bags",
                    success: "Rummaging through the garden's debris, you find some discarded but useful items:",
                    failureInjury: "You slip on wet leaves and bang your head against a planter. A mild headache sets in, possibly indicating a minor concussion.",
                    injury: "concussion_injury",
                    failureJail: "A community volunteer spots you and calls the police, reporting you for vandalism.",
                },
                herb: {
                    action: "Search among overgrown plants",
                    success: "Carefully pushing aside the overgrowth, you discover some thriving herbs:",
                    failureInjury: "You stumble into a thorny bush, leaving your arms covered in cuts and scrapes.",
                    injury: "bleeding_injury",
                    failureJail: "A local gardening club member mistakes you for a plant thief and calls the authorities.",
                },
            },
        },
        trash_tech: {
            location: "Abandoned Internet Café",
            description: "While scavenging, you find a once-bustling internet café now left to decay. Broken chairs, food wrappers, and old computers are scattered throughout. Among the trash, you spot some usable tech components.",
            choices: {
                trash: {
                    action: "Dig through debris and wrappers",
                    success: "Sorting through layers of café detritus, you unearth some valuable items:",
                    failureInjury: "A precariously balanced pile of old monitors topples onto you, leaving you with painful skin bruises all over.",
                    injury: "contusion_injury",
                    failureJail: "The café owner arrives to check on the property and catches you in the act. Off to jail you go!",
                },
                tech: {
                    action: "Inspect old computers and equipment",
                    success: "After a thorough examination of the outdated machines, you extract some useful components:",
                    failureInjury: "You accidentally touch an exposed wire, receiving a shock that leaves you with minor fatigue and muscle weakness.",
                    injury: "fatigue_injury",
                    failureJail: "Your tampering sets off an old security system. The police arrive and arrest you for breaking and entering.",
                },
            },
        },
        trash_ore: {
            location: "Derelict Construction Site",
            description: "While scavenging, you arrive at an unfinished construction site, abandoned and filled with debris. Trash piles mix with construction materials, including metal ores and other valuable resources.",
            choices: {
                trash: {
                    action: "Rummage through debris and trash",
                    success: "Picking through the construction site's refuse, you recover some usable items:",
                    failureInjury: "A stack of metal pipes shifts unexpectedly, falling on your leg and causing a large skin bruise.",
                    injury: "contusion_injury",
                    failureJail: "A security guard on his rounds spots you trespassing. He calls the police, and you're arrested.",
                },
                ore: {
                    action: "Search construction materials",
                    success: "Examining the abandoned construction materials, you find some valuable resources:",
                    failureInjury: "While lifting a heavy piece of ore, you strain your back. The trauma to your muscles is immediate and painful.",
                    injury: "trauma_injury",
                    failureJail: "Your activities attract the attention of a passing patrol. They detain you for theft of construction materials.",
                },
            },
        },
        medical_upgrade: {
            location: "Ransacked Pharmacy",
            description: "While scavenging, you enter a pharmacy that has been looted. Shelves are overturned, and supplies are scattered. Among the chaos, you find medical supplies mixed with items that can be used to enhance equipment.",
            choices: {
                medical: {
                    action: "Look for remaining medical supplies",
                    success: "Searching through the ransacked shelves, you manage to find some untouched medical supplies:",
                    failureInjury: "You slip on spilled liquid medicines, twisting your ankle badly as you fall.",
                    injury: "trauma_injury",
                    failureJail: "The pharmacy's silent alarm is still active. Police arrive and arrest you for looting.",
                },
                upgrade: {
                    action: "Find items to enhance gear",
                    success: "Among the scattered pharmacy goods, you discover some items perfect for gear enhancement:",
                    failureInjury: "A shelving unit collapses as you search, striking your chest and leaving you with bruised ribs.",
                    injury: "fracture_injury",
                    failureJail: "A vigilant neighbor spots you and reports a break-in. The police arrive swiftly to apprehend you.",
                },
            },
        },
        medical_herb: {
            location: "Traditional Herbalist Shop",
            description: "While scavenging, you discover a small herbalist shop in an old district, partially damaged but still containing various herbs and medical supplies. The air is filled with the scent of dried herbs.",
            choices: {
                medical: {
                    action: "Search for medical supplies",
                    success: "In the back of the shop, you uncover a cache of well-preserved medical supplies:",
                    failureInjury: "You accidentally inhale powdered herbs, triggering a coughing fit that leaves you with minor fatigue.",
                    injury: "fatigue_injury",
                    failureJail: "The shop owner returns unexpectedly. Mistaking you for a burglar, he calls the police.",
                },
                herb: {
                    action: "Gather stored herbs",
                    success: "Carefully sorting through the aromatic herbs, you collect some potent specimens:",
                    failureInjury: "You mistake a toxic herb for a harmless one, and handling it causes cuts and scrapes on your hands.",
                    injury: "bleeding_injury",
                    failureJail: "A local herbalist recognizes you're not the shop owner. She alerts the authorities, leading to your arrest.",
                },
            },
        },
        medical_tech: {
            location: "High-Tech Medical Facility",
            description: "While scavenging, you come across a state-of-the-art medical research facility that was hastily evacuated. Advanced medical technology and supplies are left behind, ripe for scavenging.",
            choices: {
                medical: {
                    action: "Collect advanced medical supplies",
                    success: "From the facility's stores, you gather some cutting-edge medical supplies:",
                    failureInjury: "You bump your head on an open cabinet door, leaving you with a mild headache that could be a minor concussion.",
                    injury: "concussion_injury",
                    failureJail: "The facility's AI security system detects your presence and locks down the building until authorities arrive.",
                },
                tech: {
                    action: "Scavenge high-tech devices",
                    success: "You manage to salvage some advanced technological devices from the facility:",
                    failureInjury: "An experimental device misfires, zapping you and causing minor fatigue and disorientation.",
                    injury: "fatigue_injury",
                    failureJail: "Your tampering with the equipment triggers a biohazard alarm. A HAZMAT team arrives, followed by the police.",
                },
            },
        },
        medical_ore: {
            location: "Research Laboratory",
            description: "While scavenging, you find a laboratory that focuses on medical and material sciences. It contains both medical supplies and samples of various ores used in their experiments.",
            choices: {
                medical: {
                    action: "Gather medical research supplies",
                    success: "From the lab's inventory, you collect some specialized medical research supplies:",
                    failureInjury: "You accidentally knock over a heavy medical device, causing trauma to your foot as it lands on it.",
                    injury: "trauma_injury",
                    failureJail: "A late-working scientist catches you in the act and immediately calls security.",
                },
                ore: {
                    action: "Collect ore samples",
                    success: "You carefully extract some rare ore samples from the laboratory's storage:",
                    failureInjury: "While handling a sample, you cut your hand on a sharp edge. The cut is deep and bleeding.",
                    injury: "bleeding_injury",
                    failureJail: "Your attempts to access a secured ore storage trigger a silent alarm. Security arrives promptly.",
                },
            },
        },
        upgrade_herb: {
            location: "Rooftop Garden Workshop",
            description: "While scavenging, you reach a rooftop garden that doubles as a workshop. Amidst the plants and herbs growing in containers, there are tools and materials for crafting and upgrades.",
            choices: {
                upgrade: {
                    action: "Search workshop for tools",
                    success: "Rifling through the workshop, you find some useful tools and materials:",
                    failureInjury: "You lose your footing on the wet rooftop and fall, twisting your ankle badly.",
                    injury: "trauma_injury",
                    failureJail: "The building's security spots you on the roof. They detain you until the police arrive.",
                },
                herb: {
                    action: "Pick herbs from containers",
                    success: "From the rooftop containers, you harvest a selection of healthy herbs:",
                    failureInjury: "The sweltering heat on the rooftop leaves you with minor fatigue and dizziness.",
                    injury: "fatigue_injury",
                    failureJail: "A resident spots you from their window and reports you for trespassing and theft.",
                },
            },
        },
        upgrade_tech: {
            location: "Innovator's Garage",
            description: "While scavenging, you enter a tinkerer's garage filled with half-finished projects and cutting-edge technology. Upgrade components are scattered among tech gadgets and tools.",
            choices: {
                upgrade: {
                    action: "Find upgrade components",
                    success: "Sifting through the innovator's projects, you salvage some valuable upgrade components:",
                    failureInjury: "An unstable prototype explodes in your hands, leaving you with cuts and scrapes all over your arms.",
                    injury: "bleeding_injury",
                    failureJail: "The garage's owner returns and catches you red-handed. He immediately calls the police.",
                },
                tech: {
                    action: "Scavenge high-tech gadgets",
                    success: "From the array of gadgets, you manage to extract some advanced technological items:",
                    failureInjury: "A malfunctioning robotic arm swings wildly, striking your chest and leaving you with bruised ribs.",
                    injury: "fracture_injury",
                    failureJail: "Your tinkering activates a high-pitched alarm. Neighbors call the police, who quickly arrive.",
                },
            },
        },
        upgrade_ore: {
            location: "Mechanic's Yard",
            description: "While scavenging, you explore an open yard used by a mechanic for salvaging and repurposing old machinery. Metal ores and various upgrade parts are plentiful here.",
            choices: {
                upgrade: {
                    action: "Look for upgrade parts",
                    success: "Searching through the mechanic's collection, you find some useful upgrade parts:",
                    failureInjury: "A heavy machine part falls, striking your leg and causing a large skin bruise.",
                    injury: "contusion_injury",
                    failureJail: "The mechanic's guard dogs corner you in the yard. Their barking alerts the police.",
                },
                ore: {
                    action: "Collect metal ores",
                    success: "From the yard's resources, you gather a variety of valuable metal ores:",
                    failureInjury: "The strain of carrying heavy ores leaves you with minor fatigue and muscle aches.",
                    injury: "fatigue_injury",
                    failureJail: "A night watchman spots you loading ores into your bag. He detains you until the police arrive.",
                },
            },
        },
        herb_tech: {
            location: "Experimental Bio-Lab",
            description: "While scavenging, you come across a bio-laboratory that blends nature and technology. Herb samples are grown alongside high-tech devices designed to study and enhance their properties.",
            choices: {
                herb: {
                    action: "Gather herb samples",
                    success: "From the bio-lab's greenhouse, you collect some rare and potent herb samples:",
                    failureInjury: "You bump your head on a low-hanging hydroponic system, causing a mild headache that might be a minor concussion.",
                    injury: "concussion_injury",
                    failureJail: "The lab's containment protocols activate, trapping you inside until the authorities arrive.",
                },
                tech: {
                    action: "Scavenge research devices",
                    success: "You manage to salvage some advanced bio-tech research devices:",
                    failureInjury: "A device designed to analyze plant growth accidentally pricks your skin, leaving you with minor cuts and scrapes.",
                    injury: "bleeding_injury",
                    failureJail: "Your tampering with the lab equipment triggers a biohazard alarm. A response team arrives and detains you.",
                },
            },
        },
        herb_ore: {
            location: "Botanical Research Center",
            description: "While scavenging, you find a facility dedicated to studying the natural properties of plants and minerals. Various herbs and mineral ores are stored here for research purposes.",
            choices: {
                herb: {
                    action: "Collect research herbs",
                    success: "From the center's vast collection, you gather some unique research-grade herbs:",
                    failureInjury: "You slip on a wet floor, twisting your ankle painfully as you fall.",
                    injury: "trauma_injury",
                    failureJail: "A late-night researcher catches you in the restricted area and calls security.",
                },
                ore: {
                    action: "Gather mineral ores",
                    success: "Searching through the facility's stores, you collect some rare mineral ore samples:",
                    failureInjury: "While reaching for a high shelf, you strain your back. The muscle trauma is immediate and painful.",
                    injury: "trauma_injury",
                    failureJail: "Your presence triggers a silent alarm. Police arrive to find you surrounded by valuable research materials.",
                },
            },
        },
        tech_ore: {
            location: "Abandoned Factory",
            description: "While scavenging, you come across an old factory that manufactured tech devices and components. The place is filled with discarded tech and raw ores used in production, waiting to be scavenged.",
            choices: {
                tech: {
                    action: "Search for tech components",
                    success: "Digging through the factory's leftovers, you salvage some valuable tech components:",
                    failureInjury: "You accidentally activate an old industrial laser cutter, which grazes your arm. That's going to leave a scar.",
                    injury: "bleeding_injury",
                    failureJail: "Your rummaging triggers an old security system. Local police respond to the silent alarm and find you amidst the valuable tech.",
                },
                ore: {
                    action: "Scavenge raw ores",
                    success: "From the factory's raw materials, you gather a selection of useful ores:",
                    failureInjury: "A unstable pile of ore samples collapses as you approach, partially burying you and leaving you with multiple contusions.",
                    injury: "contusion_injury",
                    failureJail: "A security guard on a routine check spots you hauling ore from the premises. He detains you until the authorities arrive.",
                },
            },
        },
        default: {
            location: "Unknown Location",
            description: "No description available.",
            choices: {},
        },
    };
    const sortedKey = [...scavengeChoices].sort().join("_");
    const reversedKey = sortedKey.split("_").reverse().join("_");
    return (locations[sortedKey] ||
        locations[reversedKey] ||
        locations.default);
}
export default getScavengeLocation;
