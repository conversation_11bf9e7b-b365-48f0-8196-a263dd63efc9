import { getRequestContext } from "./requestContext.js";
import { getXataClient } from "./xata.js";
import { LogErrorStack } from "../utils/log.js";
const xata = getXataClient();
export const logAction = async (params) => {
    try {
        const context = getRequestContext();
        const userId = params.userId ?? context?.userId;
        const ipAddress = params.session?.ipAddress ?? context?.ipAddress ?? null;
        const userAgent = params.session?.userAgent ?? context?.userAgent ?? null;
        const correlationId = context?.correlationId ?? null;
        if (!userId) {
            LogErrorStack({
                message: "logAction called without userId in params or request context",
                error: new Error("Missing userId"),
            });
            return;
        }
        const userIdNum = typeof userId === "string" ? Number(userId) : userId;
        if (!Number.isFinite(userIdNum)) {
            LogErrorStack({ error: new Error(`Invalid userId: ${userId}`) });
            return;
        }
        const metadata = {
            ipAddress,
            userAgent,
            correlationId,
        };
        await xata.db.audit_logs.create({
            userId: userIdNum,
            action: params.action,
            info: params.info || null,
            logType: params.logType || "player",
            metadata,
        });
    }
    catch (error) {
        LogErrorStack({ message: "Failed to log audit entry", error });
    }
};
export const logPlayerAction = (action, info, userId) => {
    return logAction({
        action,
        info,
        logType: "player",
        userId,
    });
};
export const logAdminAction = (action, info, userId) => {
    return logAction({
        action,
        info,
        logType: "admin",
        userId,
    });
};
export const getLatestUserActions = async (userId, options = {}) => {
    try {
        const { startDate, endDate, action, limit = 100, logType } = options;
        const userIdNum = typeof userId === "string" ? Number.parseInt(userId) : userId;
        let query = xata.db.audit_logs.filter({ userId: userIdNum });
        if (startDate && endDate) {
            query = query.filter({
                "xata.createdAt": { $ge: startDate, $le: endDate },
            });
        }
        if (action) {
            query = query.filter({ action });
        }
        if (logType) {
            query = query.filter({ logType });
        }
        return await query.sort("xata.createdAt", "desc").getPaginated({ pagination: { size: limit } });
    }
    catch (error) {
        LogErrorStack({ message: "Failed to query audit logs:", error });
        throw error;
    }
};
export const getLatestPlayerLogs = async (limit = 100) => {
    try {
        return await xata.db.audit_logs
            .filter({ logType: "player" })
            .sort("xata.createdAt", "desc")
            .getPaginated({ pagination: { size: limit } });
    }
    catch (error) {
        LogErrorStack({ message: "Failed to get latest player logs:", error });
        throw error;
    }
};
