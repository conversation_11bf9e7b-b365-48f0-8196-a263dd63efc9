import * as JailController from "./jail.controller.js";
import { jailBailSchema } from "./jail.validation.js";
import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
export const jailRouter = {
    jailList: isLoggedInAuth.handler(async () => {
        const response = await JailController.jailList();
        return handleResponse(response);
    }),
    bail: canMakeStateChangesAuth.input(jailBailSchema).handler(async ({ input, context }) => {
        const response = await JailController.jailBail(context.user.id, input.targetId);
        return handleResponse(response);
    }),
};
