import ms from "ms";
function createCache(defaultDuration = "2h") {
    return {
        data: null,
        lastFetch: 0,
        isValid: function () {
            const cacheDuration = ms(defaultDuration);
            return Date.now() - this.lastFetch < cacheDuration;
        },
        set: function (data) {
            this.data = data;
            this.lastFetch = Date.now();
            return this;
        },
        get: function () {
            return {
                data: this.data,
                lastFetch: this.lastFetch,
            };
        },
    };
}
export { createCache };
