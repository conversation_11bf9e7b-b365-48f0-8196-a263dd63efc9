import { getRedisItem, redisClient } from "../../config/redisClient.js";
import * as EquipmentService from "../../core/equipment.service.js";
import * as NotificationService from "../../core/notification.service.js";
import * as StatusEffectService from "../../core/statuseffect.service.js";
import * as UserService from "../../core/user.service.js";
import { BATTLE_STATE } from "./helpers/battle.constants.js";
import gameConfig from "../../config/gameConfig.js";
import * as BattleEquipment from "./helpers/battle.equipment.js";
import { GetMaxStamina } from "./helpers/battle.helpers.js";
import { getInitiativeModifier } from "./helpers/battle.scaling.js";
import * as TalentHelper from "../talents/talents.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import { LogErrorStack, logger } from "../../utils/log.js";
import { getAllUserStatLevels } from "../user/user.helpers.js";
import * as UserRepository from "../../repositories/user.repository.js";
import { Prisma } from "@prisma/client";
const { BASE_STAMINA } = gameConfig;
const REDIS_KEY_PREFIX = {
    BATTLE: "battle",
    PLAYER_ACTIVE_BATTLE: "player",
};
const NPC_ID_PREFIX = "npc_";
const formatPlayerId = (id, userType) => {
    if (userType === "player") {
        return id.toString();
    }
    return `${NPC_ID_PREFIX}${id}`;
};
const getBattleKey = (battleId) => `${REDIS_KEY_PREFIX.BATTLE}:${battleId}`;
const getPlayerActiveBattleKey = (playerId) => `${REDIS_KEY_PREFIX.PLAYER_ACTIVE_BATTLE}:${playerId}:activeBattle`;
export const generateBattleId = (currentUserId, targetId, battleType) => {
    return `battle_${battleType}_${Date.now()}_${currentUserId}_${targetId}`;
};
export const getActiveBattleForUser = async (userId) => {
    const battleId = await redisClient.get(getPlayerActiveBattleKey(userId));
    if (!battleId || typeof battleId !== "string")
        return null;
    return (await getRedisItem(getBattleKey(battleId)));
};
export const listBattles = async () => {
    try {
        const battleKeys = await redisClient.keys(`${REDIS_KEY_PREFIX.BATTLE}:*`);
        if (battleKeys.length === 0) {
            return { data: [] };
        }
        const battleStates = await Promise.all(battleKeys.map(async (key) => {
            const battleData = await redisClient.get(key);
            if (!battleData || typeof battleData !== "string")
                return null;
            return JSON.parse(battleData);
        }));
        const activeBattles = battleStates
            .filter((battle) => battle !== null && battle.state === BATTLE_STATE.IN_PROGRESS && Date.now() <= battle.validUntil)
            .map((battle) => ({
            id: battle.id,
            battleType: battle.battleType,
            startTime: battle.startTime,
            validUntil: battle.validUntil,
            currentRound: battle.currentRound,
            aggressorId: battle.aggressorId,
            players: Object.values(battle.players).map((player) => ({
                id: player.id,
                username: player.username,
                currentHealth: player.currentHealth,
                maxHealth: player.maxHealth,
                userType: player.userType,
            })),
        }));
        return { data: activeBattles };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: "Failed to list battles" };
    }
};
export const saveBattleState = async (battleState, battleId, currentUser, target) => {
    const isNPCBattle = battleState.battleType !== "pvp";
    const multi = redisClient
        .multi()
        .set(getBattleKey(battleId), JSON.stringify(battleState))
        .set(getPlayerActiveBattleKey(currentUser.id.toString()), battleId);
    if (!isNPCBattle && target) {
        multi.set(getPlayerActiveBattleKey(target.id.toString()), battleId);
    }
    await multi.exec();
};
const createPlayerState = async (user, userType, initialPlayerDebuffs) => {
    const isNPC = userType === "npc" || userType === "rooftop_npc";
    const battleStatusEffects = isNPC
        ? user.battleStatusEffects
        : await StatusEffectService.GetBattleStatusEffects(user);
    const playerId = formatPlayerId(user.id, userType);
    let buffs;
    if (user.roguelikeMap) {
        try {
            const roguelikeMap = user.roguelikeMap;
            buffs = {
                roguelikeStrBuff: roguelikeMap?.strBuff || 1,
                roguelikeDefBuff: roguelikeMap?.defBuff || 1,
                roguelikeDexBuff: roguelikeMap?.dexBuff || 1,
            };
        }
        catch (error) {
            LogErrorStack({ message: "Failed to parse roguelikeMap", error });
            buffs = {
                roguelikeStrBuff: 1,
                roguelikeDefBuff: 1,
                roguelikeDexBuff: 1,
            };
        }
    }
    let equipment = null;
    if (!isNPC) {
        equipment = await EquipmentService.GetEquippedItems(user.id);
    }
    const statusEffects = {
        ...battleStatusEffects,
        ...initialPlayerDebuffs,
    };
    let playerStats = {
        strength: isNPC ? user.strength : 1,
        defence: isNPC ? user.defence : 1,
        dexterity: isNPC ? 0 : 1,
        intelligence: isNPC ? 0 : 1,
        endurance: isNPC ? 0 : 1,
        vitality: isNPC ? 0 : 1,
    };
    if (!isNPC) {
        const userStats = await getAllUserStatLevels(user.id);
        playerStats = userStats;
    }
    const finalMaxStamina = isNPC ? BASE_STAMINA : GetMaxStamina(playerStats.endurance, statusEffects, user.id);
    let finalMaxHealth;
    if (isNPC) {
        finalMaxHealth = user.health;
    }
    else {
        finalMaxHealth = await user.maxHealth;
    }
    return {
        id: playerId,
        userType: userType,
        username: isNPC ? user.name : user.username,
        avatar: isNPC ? user.image : user.avatar || "",
        level: user.level,
        currentHealth: isNPC ? user.health : user.currentHealth,
        maxHealth: finalMaxHealth,
        currentStamina: finalMaxStamina,
        maxStamina: finalMaxStamina,
        attributes: {
            strength: playerStats.strength,
            defence: playerStats.defence,
            dexterity: playerStats.dexterity,
            intelligence: playerStats.intelligence,
            endurance: playerStats.endurance,
            vitality: playerStats.vitality,
        },
        weaponDamage: isNPC ? user.weaponDamage : undefined,
        ammo: isNPC ? 999 : await BattleEquipment.GetAmmoForUser(user.id, equipment),
        damageTaken: 0,
        currentTurn: 0,
        statusEffects: {
            ...battleStatusEffects,
            ...initialPlayerDebuffs,
        },
        abilities: isNPC ? undefined : await TalentHelper.GetEquippedAbilities(user),
        buffs,
        equipment,
        isBoss: userType === "npc" ? user?.boss || false : false,
    };
};
const getDefenderType = (battleType) => {
    if (battleType === "pvp")
        return "player";
    if (battleType === "pve-rooftop")
        return "rooftop_npc";
    return "npc";
};
const getFirstAttacker = (player, opponent) => {
    const playerInitiative = player.userType === "player" ? 0.5 + getInitiativeModifier(player.attributes.vitality) : 0.5;
    const opponentInitiative = opponent.userType === "player" ? 0.5 + getInitiativeModifier(opponent.attributes.vitality) : 0.5;
    const playerRoll = playerInitiative + Math.random() * 0.1;
    const opponentRoll = opponentInitiative + Math.random() * 0.1;
    return playerRoll > opponentRoll ? player : opponent;
};
export const createBattleState = async (currentUser, target, battleId, battleValidUntil, battleType) => {
    const initialPlayerDebuffs = battleType === "pve-rooftop" ? target.initialDebuff : undefined;
    const player = await createPlayerState(currentUser, "player", initialPlayerDebuffs);
    const opponent = await createPlayerState(target, getDefenderType(battleType));
    const attacker = getFirstAttacker(player, opponent);
    const combatLog = [
        {
            id: 1,
            timestamp: Date.now(),
            round: 0,
            actorId: String(player.id),
            targetId: String(opponent.id),
            action: "battle_start",
        },
    ];
    const battleState = {
        id: battleId,
        state: BATTLE_STATE.IN_PROGRESS,
        battleType,
        startTime: Date.now(),
        validUntil: battleValidUntil,
        currentRound: 1,
        aggressorId: player.id,
        firstAttackerId: attacker.id,
        combatLog: combatLog,
        players: {
            [player.id]: player,
            [opponent.id]: opponent,
        },
    };
    await saveBattleState(battleState, battleId, currentUser, target);
    return battleState;
};
export const updateBattleState = async (battleId, battleState) => {
    return await redisClient.set(getBattleKey(battleId), JSON.stringify(battleState));
};
export const cleanupBattleState = async (battleState) => {
    await redisClient.del(getBattleKey(battleState.id));
    if (battleState.players && typeof battleState.players === "object") {
        for (const playerId of Object.keys(battleState.players)) {
            await redisClient.del(getPlayerActiveBattleKey(playerId));
        }
    }
};
export const handleBattleTimeout = async (battleState) => {
    if (!battleState.players || Object.keys(battleState.players).length === 0) {
        logger.warn(`handleBattleTimeout: Battle ${battleState.id} is missing player data – performing cleanup only`);
        await cleanupBattleState(battleState);
        return;
    }
    const aggressorId = Number.parseInt(battleState.aggressorId);
    const targetPlayer = Object.values(battleState.players).find((player) => player.id !== String(aggressorId));
    const user = await UserRepository.getUserById(aggressorId);
    if (!user) {
        logger.error(`User not found for battle timeout: ${aggressorId}`);
        await cleanupBattleState(battleState);
        return;
    }
    const updateValues = {
        currentHealth: Math.ceil(user.currentHealth / 2),
    };
    if (battleState.battleType === "pve") {
        updateValues.roguelikeMap = Prisma.JsonNull;
    }
    await UserService.updateUser(aggressorId, updateValues);
    await cleanupBattleState(battleState);
    if (battleState.players && typeof battleState.players === "object") {
        for (const playerId of Object.keys(battleState.players)) {
            if (playerId.startsWith(NPC_ID_PREFIX))
                continue;
            NotificationService.NotifyUser(Number.parseInt(playerId), "fight_timeout", {
                battleId: battleState.id,
                battleType: battleState.battleType,
                aggressorId,
                targetId: targetPlayer && targetPlayer.id,
            });
        }
    }
    logAction({
        action: "BATTLE_TIMEOUT",
        userId: String(aggressorId),
        info: { battleId: battleState.id, battleType: battleState.battleType, targetId: targetPlayer },
    });
};
export const validateBattleState = async (userId) => {
    const battleState = await getActiveBattleForUser(userId);
    if (!battleState) {
        return { error: "Active battle not found" };
    }
    if (battleState.state === BATTLE_STATE.FINISHED) {
        return { error: "Battle is already over" };
    }
    if (Date.now() > battleState.validUntil) {
        logger.info(`Battle expired for user ID: ${userId}`);
        await handleBattleTimeout(battleState);
        return { error: "Battle has timed out" };
    }
    const playerState = battleState.players[userId];
    let targetState;
    if (battleState.battleType === "pvp") {
        targetState = Object.values(battleState.players).find((player) => player.id !== userId);
    }
    else {
        const targetKey = Object.keys(battleState.players).find((key) => key.startsWith(NPC_ID_PREFIX));
        targetState = targetKey ? battleState.players[targetKey] : null;
    }
    return { battleState, playerState, targetState };
};
export const sanitizeBattleStateForFrontend = (battleState) => {
    const sanitizedPlayers = {};
    for (const [playerId, player] of Object.entries(battleState.players)) {
        sanitizedPlayers[playerId] = {
            id: player.id,
            username: player.username,
            userType: player.userType,
            avatar: player.avatar,
            level: player.level,
            currentHealth: player.currentHealth,
            maxHealth: player.maxHealth,
            currentStamina: player.currentStamina,
            maxStamina: player.maxStamina,
            currentTurn: player.currentTurn,
            isBoss: player.isBoss,
            statusEffects: player.statusEffects,
            equipment: player.equipment,
        };
    }
    return {
        id: battleState.id,
        state: battleState.state,
        battleType: battleState.battleType,
        startTime: battleState.startTime,
        validUntil: battleState.validUntil,
        currentRound: battleState.currentRound,
        aggressorId: battleState.aggressorId,
        firstAttackerId: battleState.firstAttackerId,
        combatLog: battleState.combatLog,
        players: sanitizedPlayers,
    };
};
