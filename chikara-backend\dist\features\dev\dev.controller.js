import { redisClient } from "../../config/redisClient.js";
import * as sockets from "../../config/socket.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as StatusEffectService from "../../core/statuseffect.service.js";
import * as UserService from "../../core/user.service.js";
import * as BattleController from "../battle/battle.controller.js";
import * as devRepository from "../../repositories/dev.repository.js";
import { evolvePet } from "../pets/pets.controller.js";
import { addPetXp } from "../pets/pets.helpers.js";
import { LogErrorStack, logger } from "../../utils/log.js";
import * as UserRepository from "../../repositories/user.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";
export const SendNotification = (userId, type, details) => {
    sockets.SendNotification(userId, {
        type,
        details,
    });
    return { data: "Notification sent" };
};
export const TestEmail = async () => {
    return { error: "not implemented" };
};
export const AddXp = async (userId, xp) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    const newXP = await UserService.AddXPToUser(currentUser, Number.parseInt(xp.toString()));
    return { data: newXP };
};
export const FullHealEveryone = async () => {
    const users = await devRepository.findAllUsers();
    for (const user of users) {
        const maxHealth = await user.maxHealth;
        const updateData = {
            hospitalisedUntil: null,
            hospitalisedHealingType: null,
            hospitalisedReason: null,
            jailedUntil: null,
            jailReason: null,
            currentHealth: maxHealth,
            energy: 100,
            actionPoints: 10,
        };
        await UserService.updateUser(user.id, updateData);
        await StatusEffectService.removeUserStatusEffects(user.id);
    }
    return { data: "All users healed" };
};
export const FullHeal = async (userId) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    const maxHealth = await currentUser.maxHealth;
    await UserService.updateUser(currentUser.id, {
        currentHealth: maxHealth,
        energy: 100,
        actionPoints: currentUser.maxActionPoints,
    });
    await StatusEffectService.removeUserStatusEffects(currentUser.id);
    return { data: "User healed" };
};
const getPlayerActiveBattleKey = (playerId) => `player:${playerId}:activeBattle`;
const resetUserState = async (user) => {
    const maxHealth = await user.maxHealth;
    await UserService.updateUser(user.id, {
        hospitalisedUntil: null,
        hospitalisedHealingType: null,
        hospitalisedReason: null,
        jailedUntil: null,
        jailReason: null,
        currentHealth: maxHealth,
        energy: 100,
        actionPoints: user.maxActionPoints,
    });
};
export const StartRandomPVPBattle = async (userId) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    const target = await devRepository.findRandomUser(userId);
    if (!target) {
        return { error: "No target found", statusCode: 400 };
    }
    await redisClient.del(getPlayerActiveBattleKey(currentUser.id));
    await redisClient.del(getPlayerActiveBattleKey(target.id));
    await resetUserState(target);
    await resetUserState(currentUser);
    const updatedCurrentUser = await UserRepository.getUserById(userId);
    const updatedTarget = await UserRepository.getUserById(target.id);
    if (!updatedCurrentUser || !updatedTarget) {
        return { error: "Failed to fetch updated user data", statusCode: 500 };
    }
    const result = await BattleController.initiatePVPBattle(updatedCurrentUser.id, updatedTarget.id);
    if ("error" in result) {
        return { error: result.error, statusCode: 400 };
    }
    return { data: result.data };
};
export const cleanupAllBattles = async () => {
    try {
        const keys = await redisClient.keys("battle:*");
        const playerKeys = await redisClient.keys("player:*:activeBattle");
        if (keys.length === 0 && playerKeys.length === 0) {
            logger.info("No battles to clean up");
            return { error: "No battles to clean up", statusCode: 400 };
        }
        const pipeline = redisClient.multi();
        for (const key of [...keys, ...playerKeys]) {
            pipeline.del(key);
        }
        await pipeline.exec();
        logger.info(`Cleaned up ${keys.length} battles`);
        return {
            data: {
                message: "Successfully cleaned up all battles",
                battlesRemoved: keys.length,
                playerBattlesRemoved: playerKeys.length,
            },
        };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: error instanceof Error ? error.message : String(error), statusCode: 500 };
    }
};
export const ResetQuests = async (userId) => {
    await devRepository.deleteQuestProgress(userId);
    return { data: "Quests reset" };
};
export const AddCash = async (userId, amount = 5000) => {
    const updatedUser = await UserRepository.incrementUserCash(userId, amount);
    return { data: { message: "Cash added", newCash: updatedUser.cash } };
};
export const AddStats = async (userId, amount = 200) => {
    await devRepository.incrementUserStats(userId, amount);
    return { data: "Stats added" };
};
export const AddAllItems = async (userId) => {
    const items = await ItemRepository.findAllItems();
    const promises = items.map((item) => {
        return devRepository.createUserItem(userId, item.id, 1);
    });
    await Promise.all(promises);
    return { data: "All items added" };
};
export const RemoveStats = async (userId, amount = 200) => {
    await devRepository.decrementUserStats(userId, amount);
    return { data: "Stats removed" };
};
export const StartRandomMap = async (userId) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }
    const randomZone = Math.floor(Math.random() * 100);
    const maxHealth = await currentUser.maxHealth;
    const updateData = {
        hospitalisedUntil: null,
        hospitalisedHealingType: null,
        hospitalisedReason: null,
        jailedUntil: null,
        jailReason: null,
        currentHealth: maxHealth,
        energy: 100,
        actionPoints: 10,
        roguelikeMap: undefined,
        roguelikeHighscore: randomZone + 1,
    };
    await UserService.updateUser(currentUser.id, updateData);
    const locations = ["church", "shrine", "mall", "alley", "school"];
    const randomLoc = Math.floor(Math.random() * locations.length);
    return {
        data: {
            level: randomZone,
            location: locations[randomLoc],
            userId,
        },
    };
};
export const CompleteAllQuests = async (userId) => {
    const quests = await devRepository.findAllQuests();
    const promises = quests.map((quest) => {
        return devRepository.createQuestProgress(userId, quest.id, "complete");
    });
    await Promise.all(promises);
    const traderPromises = [];
    for (let i = 0; i < 6; i++) {
        traderPromises.push(devRepository.createTraderRep(userId, i + 1, 4));
    }
    await Promise.all(traderPromises);
    return { data: "All quests completed" };
};
export const SendAIChatMessage = async () => {
    return { data: "Not implemented" };
};
export const RemoveAllEffects = async (userId) => {
    await devRepository.removeEffects(userId);
    return { data: "Success" };
};
export const AddRandomEffects = async (userId) => {
    try {
        const { db } = await import("../../lib/db.js");
        const UserHelper = await import("../user/user.helpers.js");
        const user = await UserHelper.GetUserByIdWithAssociations(userId);
        if (!user) {
            return { error: "User not found" };
        }
        const randomEffects = await db.status_effect.findMany({
            where: { disabled: false },
            orderBy: { id: "asc" },
            take: 100,
        });
        if (!randomEffects || randomEffects.length === 0) {
            return { error: "No status effects found" };
        }
        const shuffled = [...randomEffects].sort(() => 0.5 - Math.random());
        const selectedEffects = shuffled.slice(0, 4);
        const appliedEffects = [];
        for (const effect of selectedEffects) {
            const result = await StatusEffectService.ApplyStatusEffectToUser(user, effect);
            if (result) {
                appliedEffects.push({
                    name: effect.name,
                    tier: effect.tier,
                    duration: effect.duration,
                });
            }
        }
        return {
            data: {
                message: `Applied ${appliedEffects.length} random status effects to user`,
                effects: appliedEffects,
            },
        };
    }
    catch (error) {
        logger.error("Error adding random effects: " + error);
        return { error: "Error adding random effects" };
    }
};
export const AddItem = async (userId, itemId, quantity = 1) => {
    const itemExists = await ItemRepository.findItemById(itemId);
    if (!itemExists) {
        return { error: `Item with ID ${itemId} not found`, statusCode: 404 };
    }
    await InventoryService.AddItemToUser({
        userId,
        itemId,
        amount: quantity,
        isTradeable: true,
    });
    return {
        data: {
            message: `Added ${quantity} of item '${itemExists.name}' to user inventory`,
            item: {
                id: itemExists.id,
                name: itemExists.name,
                quantity,
            },
        },
    };
};
export const HatchEggs = async (userId) => {
    const eggs = await devRepository.findUserEggs(userId);
    if (!eggs || eggs.length === 0) {
        return { error: "No eggs found" };
    }
    for (const egg of eggs) {
        const evolutionData = {
            current: "egg",
            next: "baby",
            progress: 100,
            requiredEggProgress: 100,
        };
        await devRepository.updatePetProgress(userId, egg.id, evolutionData);
        await evolvePet(userId, egg.id);
    }
    return { data: eggs };
};
export const SetAllPetsHappiness = async (userId) => {
    try {
        const result = await devRepository.setAllPetsHappiness(userId);
        return {
            data: {
                message: `Set happiness to 100 for ${result.count} pets`,
                petsUpdated: result.count,
            },
        };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: error instanceof Error ? error.message : String(error), statusCode: 500 };
    }
};
export const AddXpToAllPets = async (userId, xpAmount = 100) => {
    try {
        const pets = await devRepository.findUserPets(userId);
        const updatedPets = [];
        for (const pet of pets) {
            await addPetXp(pet, xpAmount);
            updatedPets.push(pet);
        }
        return {
            data: {
                message: `Added ${xpAmount} XP to ${updatedPets.length} pets`,
                petsUpdated: updatedPets.length,
            },
        };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: error instanceof Error ? error.message : String(error), statusCode: 500 };
    }
};
export const DeleteExploreNodes = async (userId) => {
    try {
        const result = await devRepository.deleteExploreNodes(userId);
        return {
            data: {
                message: `Deleted ${result.count} explore nodes for user`,
                nodesDeleted: result.count,
            },
        };
    }
    catch (error) {
        LogErrorStack({ error });
        return { error: error instanceof Error ? error.message : String(error), statusCode: 500 };
    }
};
