import { z } from "zod";
export const ApplyForJobSchema = z.object({
    jobId: z.number().int().positive(),
});
export const GetJobRequirementsSchema = z.object({
    level: z.number().int().positive(),
    jobId: z.number().int().positive(),
});
export const ChangePayoutTimeSchema = z.object({
    time: z.number().int().positive(),
});
const jobSchema = {
    applyForJob: ApplyForJobSchema,
    getJobRequirements: GetJobRequirementsSchema,
    changePayoutTime: ChangePayoutTimeSchema,
};
export default jobSchema;
