import { z } from "zod";
const actionlogValidation = {
    getMostRecentActions: z.object({
        search: z.string().optional(),
        limit: z.number().int().positive().max(1000).default(100),
    }),
    getUserActions: z.object({
        userId: z.number().int().positive(),
        search: z.string().optional(),
        limit: z.number().int().positive().max(1000).default(100),
    }),
    getAllActionsAffectingUser: z.object({
        userId: z.number().int().positive(),
        search: z.string().optional(),
        limit: z.number().int().positive().max(1000).default(100),
    }),
};
export default actionlogValidation;
