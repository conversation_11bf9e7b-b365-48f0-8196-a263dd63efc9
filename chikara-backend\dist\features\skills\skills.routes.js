import * as MiningController from "./mining.controller.js";
import * as ScavengingController from "./scavenging/scavenging.controller.js";
import * as skillsValidation from "./skills.validation.js";
import authHelper from "../../middleware/authMiddleware.js";
import validate from "../../middleware/validate.js";
import { routeHandler } from "../../utils/routeHandler.js";
import { Router } from "express";
const router = Router();
router.post("/start-mining", authHelper.IsLoggedIn, validate(skillsValidation.startMiningSchema), routeHandler(async (req) => {
    return await MiningController.startMining(req.user.id, req.body.difficulty);
}));
router.post("/process-swing", authHelper.IsLoggedIn, validate(skillsValidation.processSwingSchema), routeHandler(async (req) => {
    return await MiningController.processSwing(req.user.id, req.body.hitPosition, req.body.targetPosition);
}));
router.get("/mining-session", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await MiningController.getMiningSession(req.user.id);
}));
router.post("/cancel-mining", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await MiningController.cancelMining(req.user.id);
}));
router.post("/scavenging/generate-grid", authHelper.IsLoggedIn, validate(skillsValidation.generateScavengingGridSchema), routeHandler(async (req) => {
    return await ScavengingController.generateGrid(req.user.id, req.body.difficultyTier);
}));
router.post("/scavenging/reveal-cell", authHelper.IsLoggedIn, validate(skillsValidation.revealCellSchema), routeHandler(async (req) => {
    const { sessionId, row, col } = req.body;
    return await ScavengingController.revealCell(req.user.id, sessionId, row, col);
}));
router.get("/scavenging/active-session", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await ScavengingController.getActiveSession(req.user.id);
}));
router.delete("/scavenging/end-session", authHelper.IsLoggedIn, validate(skillsValidation.endSessionSchema), routeHandler(async (req) => {
    const { sessionId } = req.body;
    return await ScavengingController.endSession(req.user.id, sessionId);
}));
router.post("/scavenging/reset-grid", authHelper.IsAdmin, validate(skillsValidation.resetGridSchema), routeHandler(async (req) => {
    const { sessionId } = req.body;
    return await ScavengingController.resetGrid(req.user.id, sessionId);
}));
router.get("/scavenging/dev-grid/:sessionId", authHelper.IsAdmin, validate(skillsValidation.devGridSchema, "params"), routeHandler(async (req) => {
    const { sessionId } = req.params;
    return await ScavengingController.displayDevGrid(sessionId);
}));
export default router;
