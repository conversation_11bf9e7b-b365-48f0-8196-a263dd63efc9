import * as BattleHelpers from "../features/battle/helpers/battle.helpers.js";
const env = process.env.NODE_ENV || "development";
const devEnvs = new Set(["development", "dev"]);
const RESTRICT_ACCESS = process.env.RESTRICT_ACCESS === "true";
const isAdmin = (user) => user.userType === "admin";
const isModerator = (user) => user.userType === "prefect";
const sendErrorResponse = (res, status, message) => {
    res.status(status).json({
        success: false,
        data: null,
        error: message,
    });
};
const checkAuthenticated = (req, res) => {
    if (!req.user) {
        sendErrorResponse(res, 401, "Unauthorized");
        return false;
    }
    return true;
};
const checkUserState = async (user, res) => {
    if (user.banExpires) {
        sendErrorResponse(res, 403, "Can't do that while banned");
        return false;
    }
    if (user.jailedUntil) {
        sendErrorResponse(res, 403, "Can't do that while in jail");
        return false;
    }
    if (user.hospitalisedUntil) {
        sendErrorResponse(res, 403, "Can't do that while in hospital");
        return false;
    }
    if (await BattleHelpers.IsUserInBattle(user)) {
        sendErrorResponse(res, 403, "Can't do that while in a battle");
        return false;
    }
    if (user.missionEnds) {
        sendErrorResponse(res, 403, "Can't do that while on a mission");
        return false;
    }
    return true;
};
const checkRestrictedAccess = (req, res) => {
    if (devEnvs.has(env)) {
        return true;
    }
    if (RESTRICT_ACCESS && !isAdmin(req.user)) {
        sendErrorResponse(res, 403, "Access restricted");
        return false;
    }
    return true;
};
export const IsLoggedIn = (req, res, next) => {
    if (!checkAuthenticated(req, res))
        return;
    const user = req.user;
    if (user.banExpires && req.originalUrl !== "/current-user-info") {
        sendErrorResponse(res, 403, "Can't do that while banned");
        return;
    }
    if (!checkRestrictedAccess(req, res))
        return;
    next();
};
export const IsLoggedInAndCanMakeStateChanges = async (req, res, next) => {
    if (!checkAuthenticated(req, res))
        return;
    const user = req.user;
    if (!(await checkUserState(user, res)))
        return;
    if (!checkRestrictedAccess(req, res))
        return;
    next();
};
export const IsAdmin = (req, res, next) => {
    if (!checkAuthenticated(req, res))
        return;
    const user = req.user;
    if (user.banExpires) {
        sendErrorResponse(res, 403, "Can't do that while banned");
        return;
    }
    if (!isAdmin(user)) {
        sendErrorResponse(res, 403, "Unauthorized");
        return;
    }
    next();
};
export const IsModerator = (req, res, next) => {
    if (!checkAuthenticated(req, res))
        return;
    const user = req.user;
    if (!(isAdmin(user) || isModerator(user))) {
        sendErrorResponse(res, 403, "Unauthorized");
        return;
    }
    next();
};
const isDevelopmentEnv = (req, res, next) => {
    if (!devEnvs.has(env)) {
        sendErrorResponse(res, 404, "Not available in current environment");
        return;
    }
    next();
};
const isAuthenticatedMiddleware = (req, res, next) => {
    if (!checkAuthenticated(req, res))
        return;
    next();
};
export const IsDevEnv = [isDevelopmentEnv, isAuthenticatedMiddleware];
export default {
    IsLoggedIn,
    IsLoggedInAndCanMakeStateChanges,
    IsAdmin,
    IsDevEnv,
    IsModerator,
};
