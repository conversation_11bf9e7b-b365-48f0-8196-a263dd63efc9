import * as EquipmentService from "./equipment.service.js";
import * as UserRepository from "../repositories/user.repository.js";
import * as ItemRepository from "../repositories/item.repository.js";
export const AddItemToUser = async ({ userId, itemId, amount, isTradeable, upgradeLevel, quality, tx, }) => {
    const existingItem = await UserRepository.findUserItemByUserIdAndItemId(userId, itemId, isTradeable, upgradeLevel, quality, tx);
    if (existingItem) {
        existingItem.count += amount;
        return await UserRepository.updateUserItemCount(existingItem, existingItem.count, tx);
    }
    return await UserRepository.createUserItem(userId, itemId, amount, isTradeable, upgradeLevel, quality, tx);
};
export const SubtractItemFromUser = async ({ userId, itemId, amount, isTradeable, upgradeLevel, tx, }) => {
    const items = await UserRepository.findAllUserItemsByUserIdAndItemId(userId, itemId, isTradeable, upgradeLevel, tx);
    if (items.length === 0) {
        throw `User ${userId} does not have item ${itemId} matching the specified criteria`;
    }
    const totalItemCount = items.reduce((total, item) => total + item.count, 0);
    if (totalItemCount < amount) {
        throw `User ${userId} does not have enough of item ${itemId} matching the specified criteria`;
    }
    let remainingAmount = amount;
    const sortedItems = [...items].sort((a, b) => {
        if (isTradeable !== undefined)
            return 0;
        return (a.isTradeable ? 1 : 0) - (b.isTradeable ? 1 : 0);
    });
    for (const item of sortedItems) {
        if (remainingAmount <= 0)
            break;
        if (item.count <= remainingAmount) {
            remainingAmount -= item.count;
            const itemDetails = await ItemRepository.findItemById(itemId);
            if (itemDetails && EquipmentService.IsItemEquippable(itemDetails)) {
                const equippedItem = await EquipmentService.findEquippedItemBySlot(userId, itemDetails.itemType);
                if (equippedItem && equippedItem.userItemId === item.id) {
                    await EquipmentService.UnequipItemFromSlot(userId, itemDetails.itemType, tx);
                }
            }
            await UserRepository.deleteUserItem(item, tx);
        }
        else {
            item.count -= remainingAmount;
            await UserRepository.updateUserItemCount(item, item.count, tx);
            remainingAmount = 0;
        }
    }
};
export const SubtractUserItemFromUser = async (userItemId, amount, tx) => {
    const userItem = await UserRepository.findUserItemById(userItemId, tx);
    if (!userItem) {
        throw `User item ${userItemId} does not exist`;
    }
    if (userItem.count < amount) {
        throw `User item ${userItemId} does not have enough items (has ${userItem.count}, needs ${amount})`;
    }
    if (!userItem.userId) {
        throw `User item ${userItemId} does not have a user attached`;
    }
    if (userItem.item && EquipmentService.IsItemEquippable(userItem.item)) {
        const equippedItem = await EquipmentService.findEquippedItemBySlot(userItem.userId, userItem.item.itemType);
        if (equippedItem && equippedItem.userItemId === userItem.id) {
            await EquipmentService.UnequipItemFromSlot(userItem.userId, userItem.item.itemType, tx);
        }
    }
    if (userItem.count === amount) {
        await UserRepository.deleteUserItem(userItem, tx);
    }
    else {
        await UserRepository.updateUserItemCount(userItem, userItem.count - amount, tx);
    }
};
export const UserHasItem = async (userId, itemId) => {
    const res = await UserRepository.findUserItemByUserIdAndItemId(userId, itemId);
    return res !== null;
};
export const UserHasNumberOfItem = async (userId, itemId, count) => {
    const items = await UserRepository.findAllUserItemsByUserIdAndItemId(userId, itemId);
    const totalItemCount = items.reduce((total, item) => total + item.count, 0);
    return items !== null && totalItemCount >= count;
};
export const UserHasTradeableNumberOfItem = async (userId, itemId, count) => {
    const items = await UserRepository.findTradeableUserItems(userId, itemId);
    const totalItemCount = items.reduce((total, item) => total + item.count, 0);
    return items !== null && totalItemCount >= count;
};
