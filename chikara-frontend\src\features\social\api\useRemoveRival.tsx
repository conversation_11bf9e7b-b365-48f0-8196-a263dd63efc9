import { orpc } from "@/lib/orpc";
import { type UseMutationOptions, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

interface RemoveRivalParams {
    rivalId: number;
}

interface RemoveRivalResponse {
    removed: boolean;
}

const useRemoveRival = (options: Partial<UseMutationOptions<RemoveRivalResponse, Error, RemoveRivalParams>> = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.social.removeRival.mutationOptions({
            onSuccess: () => {
                toast.success("Rival removed successfully");
                // Invalidate rivals list to refresh the data
                queryClient.invalidateQueries({ queryKey: orpc.social.getRivals.key() });
            },
            onError: (error) => {
                toast.error(error.message || "Failed to remove rival");
            },
            ...options,
        })
    );
};

export default useRemoveRival;
