import { cleanupExpiredSessions } from "../../../../features/skills/scavenging/scavenging.state.js";
import { LogErrorStack, logger } from "../../../../utils/log.js";
async function cleanupExpiredScavengingSessions() {
    try {
        const cleanedCount = await cleanupExpiredSessions();
        if (cleanedCount > 0) {
            logger.info(`Cleaned up ${cleanedCount} expired scavenging sessions`);
        }
    }
    catch (error) {
        LogErrorStack({ message: `Error on scheduled scavenging session cleanup`, error });
    }
}
export default cleanupExpiredScavengingSessions;
